#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仗剑江湖行 - Python WebSocket服务器
负责处理游戏逻辑、数据存储和实时通信
"""

import asyncio
import json
import logging
import traceback
import secrets
import time
from datetime import datetime
from typing import Dict, List, Optional

import websockets
from websockets.server import WebSocketServerProtocol
import aiosqlite
from event_manager import event_manager
from realm_system import RealmSystem
from item_system import item_system, ItemType, ItemQuality, load_items_config
from martial_system import load_wugong_config
from enhanced_martial_system import get_martial_info, get_martial_by_level, get_martial_move, format_battle_description, get_martial_coefficient
from bonus_system import BonusSystem
import random
import math
from map_data import load_maps_config
from shop_system import shop_system
from market_system import MarketSystem
import uuid
from item_utils import standardize_item
import os
import copy
from crafting_system import CraftingSystem
from battle_system import simulate_battle, simulate_battle_stepwise
from reward_system import add_reward_to_player
from inventory_system import (
    add_item_to_inventory as inv_add_item_to_inventory,
    handle_get_inventory_data as inv_handle_get_inventory_data,
    handle_equip_item as inv_handle_equip_item,
    handle_unequip_item as inv_handle_unequip_item,
    handle_destroy_item as inv_handle_destroy_item,
    can_equip_item as inv_can_equip_item,
    handle_expand_inventory as inv_handle_expand_inventory
)
from attribute_system import auto_attribute_regeneration
import unicodedata

# 导入新的处理模块
try:
    from martial_handler import MartialHandler
    from realm_handler import RealmHandler
    from battle_handler import BattleHandler
    from event_handler import EventHandler
    from chat_system import ChatSystem
    from sect_system import SectSystem
    from quest_system import QuestSystem
except ImportError:
    # 如果直接运行server.py，使用相对路径
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from martial_handler import MartialHandler
    from realm_handler import RealmHandler
    from battle_handler import BattleHandler
    from event_handler import EventHandler
    from chat_system import ChatSystem
    from sect_system import SectSystem
    from quest_system import QuestSystem

# 配置日志 - 优化日志级别和轮转
import logging.handlers

# 创建日志轮转处理器，防止日志文件过大
file_handler = logging.handlers.RotatingFileHandler(
    'game_server.log',
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,  # 保留5个备份文件
    encoding='utf-8'
)

logging.basicConfig(
    level=logging.WARNING,  # 改为WARNING级别，减少日志输出
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        file_handler
    ]
)

# 禁止第三方库DEBUG日志
logging.getLogger('aiosqlite').setLevel(logging.WARNING)
logging.getLogger('sqlite3').setLevel(logging.WARNING)
logging.getLogger('asyncio').setLevel(logging.WARNING)
logging.getLogger('websockets').setLevel(logging.WARNING)
logger = logging.getLogger(__name__)

# 定义需要记录的重要消息类型
IMPORTANT_MESSAGE_TYPES = {
    'register', 'login', 'auth', 'adventure', 'meditation', 'healing',
    'equip_item', 'unequip_item', 'shop_action', 'market_action',
    'guild_action', 'crafting_action', 'realm_breakthrough', 'use_item',
    'study_martial', 'train_martial', 'redeem_code', 'send_chat_message',
    'sect_action', 'quest_action'
}



class GameServer:
    def __init__(self):
        self.db_path = os.path.join(os.path.dirname(__file__), 'game.db')
        self.init_db_task = asyncio.get_event_loop().create_task(self.init_db())
        self.clients: Dict[str, WebSocketServerProtocol] = {}
        self.player_data: Dict[str, dict] = {}
        self.user_accounts: Dict[str, dict] = {}  # 用户账号数据
        # 启动时一次性加载配置到内存
        self.game_data = self.init_game_data()
        self.bonus_system = BonusSystem(self.game_data)  # 初始化增益系统

        # 初始化各种处理器
        self.martial_handler = MartialHandler(self.game_data)
        self.realm_handler = RealmHandler(self.bonus_system)
        self.battle_handler = BattleHandler(self.game_data)
        self.event_handler = EventHandler(self.game_data, self.realm_handler, self.battle_handler)
        self.chat_system = ChatSystem(self.db_path, self)
        self.sect_system = SectSystem(self.db_path, self)
        self.quest_system = QuestSystem(self.db_path, self)

        # 缓存武功、物品、地图配置
        self.items_config = self.game_data['items']
        self.maps_config = self.game_data['maps']
        self.wugong_config = self.game_data['wugong']
        # 游戏服务器初始化完成
        self.market_orders = []  # 玩家市场订单
        self.market_system = MarketSystem(self)
        self.crafting_system = CraftingSystem(self)
        # 启动自动属性恢复任务（体力、气血、内力、精力）
        self.attribute_regen_task = asyncio.get_event_loop().create_task(auto_attribute_regeneration(self))
    
    async def init_db(self):
        async with aiosqlite.connect(self.db_path) as db:
            # 用户表 - 添加性别字段
            await db.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                character_name TEXT NOT NULL,
                gender TEXT DEFAULT '男',
                token TEXT,
                created_at TEXT NOT NULL,
                last_login TEXT,
                status TEXT DEFAULT 'active'
            )
            """)
            
            # 检查是否需要添加性别字段（兼容旧版本）
            # try:
            #     await db.execute("ALTER TABLE users ADD COLUMN gender TEXT DEFAULT '男'")
            #     logger.info("已添加性别字段到users表")
            # except aiosqlite.OperationalError:
            #     logger.info("性别字段已存在，跳过添加")
            
            # 玩家数据表
            await db.execute("""
            CREATE TABLE IF NOT EXISTS players (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                data TEXT NOT NULL,
                last_login TEXT,
                created_at TEXT NOT NULL,
                FOREIGN KEY(user_id) REFERENCES users(id)
            )
            """)
            

            
            # 物品交易记录表
            await db.execute("""
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL,
                item_id TEXT,
                item_name TEXT,
                quantity INTEGER,
                price INTEGER,
                timestamp TEXT NOT NULL,
                FOREIGN KEY(user_id) REFERENCES users(id)
            )
            """)

            # 兑换码表
            await db.execute("""
            CREATE TABLE IF NOT EXISTS redeem_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                rewards TEXT NOT NULL,
                max_uses INTEGER DEFAULT 1,
                current_uses INTEGER DEFAULT 0,
                expires_at TEXT,
                created_at TEXT NOT NULL,
                created_by TEXT,
                status TEXT DEFAULT 'active'
            )
            """)

            # 兑换码使用记录表
            await db.execute("""
            CREATE TABLE IF NOT EXISTS redeem_code_usage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                code_id INTEGER NOT NULL,
                code TEXT NOT NULL,
                rewards TEXT NOT NULL,
                redeemed_at TEXT NOT NULL,
                FOREIGN KEY(user_id) REFERENCES users(id),
                FOREIGN KEY(code_id) REFERENCES redeem_codes(id),
                UNIQUE(user_id, code_id)
            )
            """)
            
            await db.commit()
            # 插入测试账号
            await self.insert_test_data(db)
            logger.info("数据库初始化完成，包含用户、玩家数据、交易记录、兑换码表")

    async def insert_test_data(self, db):
        # 检查是否已有测试账号
        async with db.execute("SELECT id FROM users WHERE username=?", ("testuser",)) as cursor:
            if await cursor.fetchone():
                return
        token = secrets.token_hex(16)
        created_at = datetime.now().isoformat()
        await db.execute("INSERT INTO users (username, password, character_name, gender, token, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                         ("testuser", "123456", "测试侠客", "男", token, created_at))
        await db.commit()
        async with db.execute("SELECT id FROM users WHERE username=?", ("testuser",)) as cursor:
            user_row = await cursor.fetchone()
            user_id = user_row[0]
        player_data = self.create_new_player(str(user_id), "测试侠客", "男")
        await db.execute("INSERT INTO players (user_id, data) VALUES (?, ?)", (user_id, json.dumps(player_data)))
        await db.commit()
        logger.info("已插入测试账号 testuser/123456")

    def init_game_data(self) -> dict:
        """初始化游戏数据"""
        logger.info("开始初始化游戏数据...")
        # 动态加载物品
        items_data = load_items_config()
        # 动态加载地图
        maps_data = load_maps_config()
        # 动态加载武功（修正：用enhanced_martial_system的load_wugong_config）
        wugong_data = load_wugong_config()
        # 动态加载怪物
        import json, os
        monsters_path = os.path.join(os.path.dirname(__file__), 'monsters.json')
        if os.path.exists(monsters_path):
            with open(monsters_path, encoding='utf-8') as f:
                monsters_data = json.load(f)
            # 自动补全max_hp字段
            for m in monsters_data:
                if 'max_hp' not in m or m['max_hp'] != m['hp']:
                    m['max_hp'] = m['hp']
        else:
            monsters_data = []
        game_data = {
            'items': items_data,
            'maps': maps_data,
            'wugong': wugong_data,
            'monsters': monsters_data
        }
        return game_data
    
    def generate_talent_attributes(self):
        """
        随机生成天赋属性（力量、悟性、身法、根骨），总和100，每项≥15
        """
        min_value = 15
        remain = 100 - 4 * min_value
        # 先生成3个分割点
        points = sorted([random.randint(0, remain) for _ in range(3)])
        a = points[0]
        b = points[1] - points[0]
        c = points[2] - points[1]
        d = remain - points[2]
        return {
            '力量': min_value + a,
            '悟性': min_value + b,
            '身法': min_value + c,
            '根骨': min_value + d
        }

    def get_realm_info(self, experience: int) -> dict:
        """根据历练值计算境界信息"""
        return RealmSystem.get_realm_info(experience)
    
    def init_martial_skills(self) -> dict:
        """初始化武功技能，返回dict"""
        return self.martial_handler.init_martial_skills()

    def get_martial_type(self, martial_name):
        # 获取武功类型，兼容 enhanced_martial_system
        return self.martial_handler.get_martial_type(martial_name)
    
    def create_new_player(self, user_id: str, character_name: str, gender: str = '男') -> dict:
        """创建新玩家数据，支持性别和天赋"""
        talent = self.generate_talent_attributes()
        
        # 初始历练值
        initial_experience = 0
        realm_info = self.get_realm_info(initial_experience)
        
        return {
            'id': user_id,
            'name': character_name,
            'character_name': character_name,  # 兼容字段
            'gender': gender,
            'talent': talent,  # 四项天赋
            'fortune': 1,      # 富源
            'hp': 100,
            'max_hp': 100,
            'mp': 50,
            'max_mp': 50,
            'attack': 10,
            'defense': 5,
            'hit_rate': 0.8,
            'dodge': 1,  # 闪避百分比
            'crit': 1,   # 暴击百分比
            'critical_damage': 1.5,
            'block_rate': 0.05,
            'block_value': 5,
            'agility': 10,
            'internal_power': 10,
            'skill_mastery': 10,
            'meridian_flow': 10,
            'energy': 100,
            'max_energy': 100,
            'spirit': 100,
            'max_spirit': 100,
            'comprehension': 10,
            'reputation': 0,
            'karma': 0,
            'killing_intent': 0,
            'friendliness': 0,
            'money': 1000,
            'gold': 0,
            'status': 'normal',
            'inventory': [],
            'inventory_capacity': 50,  # 背包容量
            'experience': initial_experience,  # 历练值
            'realm_info': realm_info,  # 境界信息
            'skill_points': 0,  # 武学点
            'equipment': {
                'main_hand': None,    # 主手武器
                'off_hand': None,     # 副手武器
                'helmet': None,       # 头盔
                'necklace': None,     # 项链
                'armor': None,        # 衣服
                'cloak': None,        # 披风
                'pants': None,        # 裤子
                'shoes': None,        # 鞋子
                'bracelet1': None,    # 手镯1
                'bracelet2': None,    # 手镯2
                'ring1': None,        # 戒指1
                'ring2': None,        # 戒指2
                'medal': None,        # 勋章
            },
            'gather_tools': {
                'mining': None,
                'logging': None,
                'herbalism': None,
                'skinning': None
            },
            'gather_skills': {
                'mining': {'level': 1, 'exp': 0},
                'logging': {'level': 1, 'exp': 0},
                'herbalism': {'level': 1, 'exp': 0},
                'skinning': {'level': 1, 'exp': 0}
            },
            'skills': [],
            # 新增：武学技能 - 初始化增强武功系统的基础武功
            'martial_skills': self.init_martial_skills(),
            'created_at': datetime.now().isoformat(),
            'last_login': datetime.now().isoformat(),
            'current_map': 'changan',
            'event_log': [],
            'adventure_count': 0,  # 闯江湖次数统计
        }
    
    async def handle_client(self, websocket: WebSocketServerProtocol, path: str):
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = None
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    response = await self.process_message(data, websocket)
                    if response:
                        response_json = json.dumps(response, ensure_ascii=False)
                        try:
                            await websocket.send(response_json)
                        except Exception as e:
                            logger.warning(f"[{client_address}] WebSocket已关闭，消息未发送: {e}")
                except json.JSONDecodeError as e:
                    logger.error(f"[{client_address}] JSON解析失败: {e}, 原始消息: {message}")
                    error_msg = {'type': 'error', 'data': {'message': f'消息格式错误: {e}'}}
                    try:
                        await websocket.send(json.dumps(error_msg, ensure_ascii=False))
                    except Exception as e:
                        logger.warning(f"[{client_address}] WebSocket已关闭，消息未发送: {e}")
                except Exception as e:
                    logger.error(f"[{client_address}] 消息处理异常: {e}")
                    logger.error(f"[{client_address}] 异常详情: {traceback.format_exc()}")
                    error_msg = {'type': 'error', 'data': {'message': f'服务器异常: {e}'}}
                    try:
                        await websocket.send(json.dumps(error_msg, ensure_ascii=False))
                    except Exception as e:
                        logger.warning(f"[{client_address}] WebSocket已关闭，消息未发送: {e}")
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"[{client_address}] 客户端断开连接")
            user_id = getattr(websocket, 'user_id', None)
            if user_id and user_id in self.clients and self.clients[user_id] == websocket:
                del self.clients[user_id]
                logger.info(f"[user_id={user_id}] 已从客户端列表移除")
            if user_id and user_id in self.player_data:
                del self.player_data[user_id]
                logger.info(f"[user_id={user_id}] 已从玩家数据列表移除")
        except Exception as e:
            logger.error(f"[{client_address}] 连接处理异常: {e}")
            logger.error(f"[{client_address}] 异常详情: {traceback.format_exc()}")
        finally:
            user_id = getattr(websocket, 'user_id', None)
            if user_id and user_id in self.clients and self.clients[user_id] == websocket:
                del self.clients[user_id]
                logger.info(f"[user_id={user_id}] 已从客户端列表移除(finally)")
            if user_id and user_id in self.player_data:
                del self.player_data[user_id]
                logger.info(f"[user_id={user_id}] 已从玩家数据列表移除(finally)")
            logger.info(f"[{client_address}] 连接处理结束")

    async def process_message(self, message: dict, websocket: WebSocketServerProtocol) -> Optional[dict]:
        # 消息格式健壮性校验
        if not isinstance(message, dict) or 'type' not in message:
            return {'type': 'error', 'data': {'message': "消息格式错误，缺少'type'字段"}}
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        msg_type = message.get('type')

        # 只记录重要消息类型的日志
        if msg_type in IMPORTANT_MESSAGE_TYPES:
            logger.info(f"[process_message] 收到消息类型: {msg_type}")

        try:
            data = message.get('data', {})
            if msg_type == 'register':
                logger.info(f"[{client_address}] 处理注册请求")
                return await self.handle_register(data, websocket)
            elif msg_type == 'login':
                logger.info(f"[{client_address}] 处理登录请求")
                return await self.handle_login(data, websocket)
            elif msg_type == 'auth':
                logger.info(f"[{client_address}] 处理认证请求")
                return await self.handle_auth(data, websocket)
            elif msg_type == 'get_player_data':
                # 频繁请求，不记录日志
                return await self.handle_get_player_data(websocket)
            elif msg_type == 'get_inventory_data':
                # 频繁请求，不记录日志
                return await self.handle_get_inventory_data(websocket)
            elif msg_type == 'get_skills_data':
                # 频繁请求，不记录日志
                return await self.handle_get_skills_data(websocket)
            elif msg_type == 'adventure':
                logger.info(f"[{client_address}] 处理闯江湖请求")
                return await self.handle_adventure(websocket)
            elif msg_type == 'gather_action':
                # 采集操作，记录日志
                logger.info(f"[{client_address}] 处理采集操作请求")
                # 保存消息数据供采集处理使用
                websocket.last_gather_type = data.get('gatherType')
                websocket.last_message_data = data
                return await self.handle_gather_action(websocket)
            elif msg_type == 'meditation':
                logger.info(f"[{client_address}] 处理打坐请求")
                return await self.handle_meditation(websocket)
            elif msg_type == 'healing':
                logger.info(f"[{client_address}] 处理疗伤请求")
                return await self.handle_healing(websocket)
            elif msg_type == 'equip_item':
                logger.info(f"[{client_address}] 处理装备物品请求")
                return await self.handle_equip_item(data, websocket)
            elif msg_type == 'unequip_item':
                logger.info(f"[{client_address}] 处理卸下装备请求")
                return await self.handle_unequip_item(data, websocket)
            elif msg_type == 'expand_inventory':
                # 扩充背包，记录日志
                logger.info(f"[{client_address}] 处理扩充背包请求")
                return await self.handle_expand_inventory(websocket)
            elif msg_type == 'equipment_action':
                # 装备操作，不记录日志（频繁）
                return await self.handle_equipment_action(data, websocket)
            elif msg_type == 'skill_action':
                # 武功操作，不记录日志（频繁）
                return await self.handle_skill_action(data, websocket)
            elif msg_type == 'shop_action':
                logger.info(f"[{client_address}] 处理商店操作请求")
                return await self.handle_shop_action(data, websocket)
            elif msg_type == 'market_action':
                logger.info(f"[{client_address}] 处理市场操作请求")
                return await self.handle_market_action(data, websocket)
            elif msg_type == 'guild_action':
                logger.info(f"[{client_address}] 处理门派操作请求")
                return await self.handle_guild_action(data, websocket)
            elif msg_type == 'crafting_action':
                logger.info(f"[{client_address}] 处理打造操作请求")
                return await self.handle_crafting_action(data, websocket)
            elif msg_type == 'get_map_list':
                # 获取地图列表，不记录日志（频繁）
                return await self.handle_get_map_list(websocket)
            elif msg_type == 'select_map':
                # 选择地图，记录日志
                logger.info(f"[{client_address}] 处理选择地图请求")
                return await self.handle_select_map(data, websocket)
            elif msg_type == 'ping':
                # 心跳请求，不记录日志
                return {'type': 'pong', 'data': {'timestamp': data.get('timestamp')}}
            elif msg_type == 'realm_breakthrough':
                logger.info(f"[{client_address}] 处理境界突破请求")
                return await self.handle_realm_breakthrough(websocket)
            elif msg_type == 'use_item':
                logger.info(f"[{client_address}] 处理使用物品请求")
                return await self.handle_use_item(data, websocket)
            elif msg_type == 'destroy_item':
                # 销毁物品，记录日志
                logger.info(f"[{client_address}] 处理销毁物品请求")
                return await self.handle_destroy_item(data, websocket)
            elif msg_type == 'study_martial':
                logger.info(f"[{client_address}] 处理学习武学请求")
                return await self.handle_study_martial(data, websocket)
            elif msg_type == 'get_martial_data':
                # 获取武学数据，不记录日志（频繁）
                return await self.handle_get_martial_data(websocket)
            elif msg_type == 'use_martial':
                # 装备武学，记录日志
                logger.info(f"[{client_address}] 处理装备武学请求")
                return await self.handle_use_martial(data, websocket)
            elif msg_type == 'unequip_martial':
                # 卸下武学，记录日志
                logger.info(f"[{client_address}] 处理卸下武学请求")
                return await self.handle_unequip_martial(data, websocket)
            elif msg_type == 'unequip':
                logger.info(f"[{client_address}] 处理卸下请求")
                # 根据数据类型判断是卸下装备还是武功
                if 'slot_type' in data or 'slot' in data:
                    # 卸下装备 - 兼容 slot 和 slot_type 参数
                    if 'slot' in data and 'slot_type' not in data:
                        data['slot_type'] = data['slot']
                    return await self.handle_unequip_item(data, websocket)
                elif 'martial_type' in data or 'martial_name' in data or 'skill_name' in data:
                    # 卸下武功 - 兼容多种武功名称字段
                    if 'skill_name' in data and 'martial_name' not in data:
                        data['martial_name'] = data['skill_name']
                    return await self.handle_unequip_martial(data, websocket)
                else:
                    return {'type': 'error', 'data': {'message': '卸下请求参数错误，缺少slot/slot_type或martial_name/skill_name'}}
            elif msg_type == 'get_martial_move':
                # 获取武功招式，不记录日志（频繁）
                return await self.handle_get_martial_move(data, websocket)
            elif msg_type == 'get_bonus_summary':
                # 获取增益摘要，不记录日志（非常频繁）
                user_id = getattr(websocket, 'user_id', None)
                if not user_id:
                    return {'type': 'error', 'data': {'message': '未认证'}}
                player = self.player_data.get(str(user_id))
                if not player:
                    return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
                summary = self.get_bonus_summary(player)
                return {'type': 'bonus_summary', 'data': summary}
            elif msg_type == 'get_map_npcs':
                # 获取地图NPC，不记录日志（非常频繁）
                return await self.handle_get_map_npcs(websocket)
            elif msg_type == 'npc_function':
                # NPC功能，记录日志
                logger.info(f"[{client_address}] 处理NPC功能请求")
                return await self.handle_npc_function(data, websocket)
            elif msg_type == 'get_wugong_config':
                # 获取武功配置，不记录日志（频繁）
                return await self.handle_get_wugong_config(websocket)
            elif msg_type == 'get_martial_configs':
                # 获取武功配置列表，不记录日志（频繁）
                return await self.handle_get_martial_configs(websocket)
            elif msg_type == 'get_items_config':
                # 获取物品配置，不记录日志（频繁）
                return await self.handle_get_items_config(websocket)
            elif msg_type == 'get_maps_config':
                # 获取地图配置，不记录日志（频繁）
                return await self.handle_get_maps_config(websocket)
            elif msg_type == 'change_player_stats':
                # 属性变化，记录日志
                logger.info(f"[{client_address}] 处理属性变化请求")
                return await self.handle_change_player_stats(data, websocket)
            elif msg_type == 'encounter_monster':
                # 遇怪，记录日志
                logger.info(f"[{client_address}] 处理遇怪请求")
                return await self.handle_encounter_monster(player, data, websocket)
            elif msg_type == 'start_battle_from_encounter':
                # 开始战斗，记录日志
                logger.info(f"[{client_address}] 处理开始战斗请求")
                return await self.handle_start_battle_from_encounter(data, websocket)
            elif msg_type == 'escape_battle':
                logger.info(f"[{client_address}] 处理逃跑请求")
                return await self.handle_escape_battle(data, websocket)
            elif msg_type == 'train_martial':
                logger.info(f"[{client_address}] 处理修炼武学请求")
                return await self.train_martial(data, websocket)
            elif msg_type == 'get_ranking':
                # 获取排行榜，不记录日志（频繁）
                return await self.handle_get_ranking(data, websocket)
            elif msg_type == 'redeem_code':
                logger.info(f"[{client_address}] 处理兑换码请求")
                return await self.handle_redeem_code(data, websocket)
            elif msg_type == 'get_redeem_history':
                # 获取兑换历史，不记录日志（频繁）
                return await self.handle_get_redeem_history(websocket)
            elif msg_type == 'get_map_players':
                # 获取地图玩家，不记录日志（非常频繁）
                return await self.handle_get_map_players(data, websocket)
            elif msg_type == 'player_action':
                # 玩家交互，记录日志
                logger.info(f"[{client_address}] 处理玩家交互请求")
                return await self.handle_player_action(data, websocket)
            elif msg_type == 'get_chat_messages':
                # 获取聊天消息，不记录日志（频繁）
                return await self.chat_system.handle_get_chat_messages(data, websocket)
            elif msg_type == 'send_chat_message':
                logger.info(f"[{client_address}] 处理发送聊天消息请求")
                return await self.chat_system.handle_send_chat_message(data, websocket)
            elif msg_type == 'sect_action':
                logger.info(f"[{client_address}] 处理门派操作请求")
                return await self.sect_system.handle_sect_action(data, websocket)
            elif msg_type == 'quest_action':
                logger.info(f"[{client_address}] 处理任务操作请求")
                return await self.quest_system.handle_quest_action(data, websocket)
            else:
                return {'type': 'error', 'data': {'message': f'未知消息类型: {msg_type}'}}
        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            logger.error(f"[{client_address}] process_message异常: {e}")
            logger.error(f"[{client_address}] 异常详情: {tb}")
            return {'type': 'error', 'data': {'message': f'服务器异常: {e}', 'traceback': tb}}

    async def handle_register(self, data, websocket):
        username = data.get('username')
        password = data.get('password')
        character_name = data.get('character_name') or data.get('characterName')
        gender = data.get('gender', '男')
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # 检查用户名是否已存在
                async with db.execute("SELECT id FROM users WHERE username=?", (username,)) as cursor:
                    if await cursor.fetchone():
                        return {'type': 'register_failed', 'data': {'message': '账号已存在'}}
                # 创建新用户
                token = secrets.token_hex(16)
                created_at = datetime.now().isoformat()
                await db.execute(
                    "INSERT INTO users (username, password, character_name, gender, token, created_at) VALUES (?, ?, ?, ?, ?, ?)",
                    (username, password, character_name, gender, token, created_at)
                )
                await db.commit()
                # 获取新创建的用户ID
                async with db.execute("SELECT id FROM users WHERE username=?", (username,)) as cursor:
                    user_row = await cursor.fetchone()
                    user_id = user_row[0]
                # 创建新玩家数据
                player_data = self.create_new_player(str(user_id), character_name, gender)
                # 保存玩家数据到数据库
                await db.execute("INSERT INTO players (user_id, data, created_at) VALUES (?, ?, ?)", (user_id, json.dumps(player_data, ensure_ascii=False), created_at))
                await db.commit()
                # 注册成功后，将玩家数据加载到内存中
                self.clients[str(user_id)] = websocket
                websocket.user_id = str(user_id)
                self.player_data[str(user_id)] = player_data
                logger.info(f"[register] 注册成功，玩家数据已保存到数据库并加载到内存: {character_name} (ID: {user_id})")
                return {
                    'type': 'register_success',
                    'data': {
                        'token': token,
                        'userInfo': {
                            'username': username,
                            'characterName': character_name,
                            'userId': str(user_id),
                            'gender': gender,
                            'talent': player_data['talent'],
                            'fortune': player_data['fortune']
                        }
                    }
                }
        except Exception as e:
            logger.error(f"注册失败: {e}")
            return {'type': 'register_failed', 'data': {'message': f'注册失败: {str(e)}'}}

    async def handle_login(self, data, websocket):
        username = data.get('username')
        password = data.get('password')
        try:
            async with aiosqlite.connect(self.db_path) as db:
                # 验证用户名和密码
                async with db.execute("SELECT id, password, character_name, gender, token FROM users WHERE username=?", (username,)) as cursor:
                    row = await cursor.fetchone()
                    if not row or row[1] != password:
                        # 失败时立即推送并return
                        resp = {'type': 'login_failed', 'data': {'message': '账号或密码错误'}}
                        await websocket.send(json.dumps(resp, ensure_ascii=False))
                        return resp
                    user_id, _, character_name, gender, token = row
                # 生成新token
                new_token = secrets.token_hex(16)
                await db.execute("UPDATE users SET token = ?, last_login = ? WHERE id = ?", 
                                (new_token, datetime.now().isoformat(), user_id))
                await db.commit()
                # 从数据库加载玩家数据
                async with db.execute("SELECT data FROM players WHERE user_id=?", (user_id,)) as cursor:
                    pdata = await cursor.fetchone()
                    if pdata:
                        player_data = json.loads(pdata[0])
                        player_data['last_login'] = datetime.now().isoformat()
                        if 'gender' not in player_data:
                            player_data['gender'] = gender or '男'
                        if 'talent' not in player_data:
                            player_data['talent'] = self.generate_talent_attributes()
                        if 'fortune' not in player_data:
                            player_data['fortune'] = 1
                        if 'martial_skills' not in player_data or not player_data['martial_skills']:
                            player_data['martial_skills'] = self.init_martial_skills()
                        else:
                            # 检查并补全生活技能的完整字段
                            life_skills = ['采药', '伐木', '挖矿', '剥皮']
                            for skill_name in life_skills:
                                # 查找现有的生活技能
                                existing_skill = None
                                for ms in player_data['martial_skills']:
                                    if ms.get('name') == skill_name or ms.get('名称') == skill_name:
                                        existing_skill = ms
                                        break

                                if existing_skill:
                                    # 补全缺失的字段
                                    level = existing_skill.get('level', existing_skill.get('等级', 0))
                                    exp = existing_skill.get('exp', existing_skill.get('经验', 0))
                                    coefficient = 60
                                    need_exp = coefficient * (level + 1) ** 2

                                    existing_skill.update({
                                        'name': skill_name,
                                        '名称': skill_name,
                                        'level': level,
                                        '等级': level,
                                        'exp': exp,
                                        '经验': exp,
                                        'maxExp': need_exp,
                                        '最大经验': need_exp,
                                        'type': '生活技能',
                                        '类型': '生活技能',
                                        'quality': '普通',
                                        '品质': '普通',
                                        'unlocked': True,
                                        '解锁': True,
                                        'equipped': False,
                                        '装备': False
                                    })
                                else:
                                    # 创建缺失的生活技能
                                    coefficient = 60
                                    level = 0
                                    need_exp = coefficient * (level + 1) ** 2

                                    new_skill = {
                                        'name': skill_name,
                                        '名称': skill_name,
                                        'level': level,
                                        '等级': level,
                                        'exp': 0,
                                        '经验': 0,
                                        'maxExp': need_exp,
                                        '最大经验': need_exp,
                                        'unlocked': True,
                                        '解锁': True,
                                        'equipped': False,
                                        '装备': False,
                                        'type': '生活技能',
                                        '类型': '生活技能',
                                        'quality': '普通',
                                        '品质': '普通',
                                        '是否可招架': '否'
                                    }
                                    player_data['martial_skills'].append(new_skill)
                        if 'adventure_count' not in player_data:
                            player_data['adventure_count'] = 0
                        await self.save_player_data(str(user_id), player_data)
                    else:
                        player_data = self.create_new_player(str(user_id), character_name, gender or '男')
                        await db.execute(
                            "INSERT INTO players (user_id, data, created_at) VALUES (?, ?, ?)",
                            (user_id, json.dumps(player_data, ensure_ascii=False), datetime.now().isoformat())
                        )
                        await db.commit()
                self.clients[str(user_id)] = websocket
                self.player_data[str(user_id)] = player_data
                websocket.user_id = str(user_id)
                logger.info(f"[{str(user_id)}] 登录成功，玩家数据已从数据库加载到内存: {character_name} (ID: {user_id})")
                # 成功时立即推送并return
                resp = {
                    'type': 'login_success',
                    'data': {
                        'token': new_token,
                        'userInfo': {
                            'username': username,
                            'characterName': character_name,
                            'userId': str(user_id),
                            'gender': player_data.get('gender', gender or '男'),
                            'talent': player_data.get('talent', self.generate_talent_attributes()),
                            'fortune': player_data.get('fortune', 1)
                        }
                    }
                }
                await websocket.send(json.dumps(resp, ensure_ascii=False))
                return resp
        except Exception as e:
            logger.error(f"登录失败: {e}")
            # 失败时立即推送并return
            resp = {'type': 'login_failed', 'data': {'message': f'登录失败: {str(e)}'}}
            await websocket.send(json.dumps(resp, ensure_ascii=False))
            return resp

    async def handle_auth(self, data, websocket):
        token = data.get('token')
        
        try:
            # 根据token获取用户信息
            user_info = await self.get_user_by_token(token)
            if not user_info:
                return {'type': 'auth_failed', 'data': {'message': '认证失败，token无效'}}
            
            user_id, username, character_name, gender = user_info
            
            # 从数据库加载玩家数据
            player_data = await self.load_player_data(str(user_id))
            if not player_data:
                # 如果没有玩家数据，创建新的
                player_data = self.create_new_player(str(user_id), character_name, gender or '男')
                await self.save_player_data(str(user_id), player_data)
            else:
                # 兼容旧数据：如果没有性别字段，添加默认值
                if 'gender' not in player_data:
                    player_data['gender'] = gender or '男'
                # 兼容旧数据：如果没有天赋字段，生成默认天赋
                if 'talent' not in player_data:
                    player_data['talent'] = self.generate_talent_attributes()
                # 兼容旧数据：如果没有富源字段，添加默认值
                if 'fortune' not in player_data:
                    player_data['fortune'] = 1
                # 兼容旧数据：补全境界和武学点相关字段
                if 'experience' not in player_data:
                    player_data['experience'] = 0
                if 'skill_points' not in player_data:
                    player_data['skill_points'] = 0
                # 兼容老账号：如果没有 martial_skills 字段，自动补全
                if 'martial_skills' not in player_data or not player_data['martial_skills']:
                    player_data['martial_skills'] = self.init_martial_skills()
                # 兼容旧数据：如果没有闯江湖次数字段，添加默认值
                if 'adventure_count' not in player_data:
                    player_data['adventure_count'] = 0
                # 更新境界信息
                player_data['realm_info'] = self.get_realm_info(player_data['experience'])
                # 保存基础属性值，用于境界增益计算
                base_attrs = ['max_hp', 'max_mp', 'max_energy', 'attack', 'defense', 'dodge', 'crit']
                for attr in base_attrs:
                    if attr in player_data and f'base_{attr}' not in player_data:
                        player_data[f'base_{attr}'] = player_data[attr]
                # 应用装备、武功和境界增益并保存
                player_data = await self.update_player_bonuses_and_save(str(user_id), player_data)
                # 保存更新后的数据
                await self.save_player_data(str(user_id), player_data)
            
            # 更新最后登录时间
            player_data['last_login'] = datetime.now().isoformat()
            await self.save_player_data(str(user_id), player_data)
            
            # 认证成功后，将玩家数据加载到内存中
            self.clients[str(user_id)] = websocket
            self.player_data[str(user_id)] = player_data
            
            # 设置websocket的用户ID属性
            websocket.user_id = str(user_id)
            
            logger.info(f"[{str(user_id)}] 认证成功，玩家数据已从数据库加载到内存: {character_name} (ID: {user_id})")
            
            return {
                'type': 'auth_success', 
                'data': {
                    'player_id': user_id, 
                    'player': player_data,
                    'userInfo': {
                        'username': username,
                        'characterName': character_name,
                        'userId': str(user_id),
                        'gender': player_data.get('gender', gender or '男'),
                        'talent': player_data.get('talent', self.generate_talent_attributes()),
                        'fortune': player_data.get('fortune', 1)
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"认证失败: {e}")
            return {'type': 'auth_failed', 'data': {'message': f'认证失败: {str(e)}'}}
    
    async def handle_get_player_data(self, websocket: WebSocketServerProtocol) -> dict:
        """获取玩家数据"""
        user_id = getattr(websocket, 'user_id', None)

        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证，请重新登录'}}

        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在，请重新登录'}}
        
        # 兼容老账号，自动补全闪避和暴击
        if 'dodge' not in player:
            player['dodge'] = 1
        if 'crit' not in player:
            player['crit'] = 1
        # 兼容老账号：补全所有基础属性字段
        default_attrs = {
            'max_hp': 100, 'max_mp': 50, 'max_energy': 100, 'max_spirit': 100,
            'attack': 10, 'defense': 5, 'hp': 100, 'mp': 50, 'energy': 100, 'spirit': 100
        }
        for k, v in default_attrs.items():
            if k not in player:
                player[k] = v
        # 修正当前属性不超过最大值
        for k, maxk in [('hp', 'max_hp'), ('mp', 'max_mp'), ('energy', 'max_energy'), ('spirit', 'max_spirit')]:
            if player.get(k, 0) > player.get(maxk, 0):
                player[k] = player[maxk]
        # 如果 mp/hp/energy/spirit 小于0，也修正为0
        for k in ['hp', 'mp', 'energy', 'spirit']:
            if player.get(k, 0) < 0:
                player[k] = 0
        
        # 兼容老账号：如果没有 current_map 字段或为空，自动补上 'changan' 并保存
        if 'current_map' not in player or not player['current_map']:
            player['current_map'] = 'changan'
            user_id = player.get('id')
            if not player.get('id'):
                player['id'] = str(user_id)
            if user_id:
                await self.save_player_data(user_id, player)
        # 新增：补充地图中文名
        current_map_id = player.get('current_map', 'changan')
        maps = self.game_data['maps']
        map_data = next((m for m in maps.values() if m['id'] == current_map_id), None)
        player['map_name'] = map_data.get('名称', '未知') if map_data else '未知'
        # 兼容老账号：martial_skills 为 dict 时自动转为 list
        if isinstance(player.get('martial_skills'), dict):
            skills_list = []
            for name, info in player['martial_skills'].items():
                skill = {'name': name, '名称': name}
                skill.update(info)
                # 确保中英文字段都存在
                if 'level' in skill and '等级' not in skill:
                    skill['等级'] = skill['level']
                if 'exp' in skill and '经验' not in skill:
                    skill['经验'] = skill['exp']
                if 'type' in skill and '类型' not in skill:
                    skill['类型'] = skill['type']
                if 'quality' in skill and '品质' not in skill:
                    skill['品质'] = skill['quality']
                if 'unlocked' in skill and '解锁' not in skill:
                    skill['解锁'] = skill['unlocked']
                if 'equipped' in skill and '装备' not in skill:
                    skill['装备'] = skill['equipped']
                # 补全 type 字段
                if 'type' not in skill:
                    from enhanced_martial_system import get_martial_info
                    martial_info = get_martial_info(name)
                    skill['type'] = martial_info.get('类型') or martial_info.get('type') or '未知' if martial_info else '未知'
                    skill['类型'] = skill['type']
                skills_list.append(skill)
            player['martial_skills'] = skills_list
            await self.save_player_data(user_id, player)
        # 兼容：如果本来就是 list，确保每个元素都有 type 字段
        elif isinstance(player.get('martial_skills'), list):
            changed = False
            # 统计每个类型已装备的武功名
            equipped_by_type = {}
            for skill in player['martial_skills']:
                if skill.get('equipped'):
                    equipped_by_type[skill.get('type')] = skill['name']
            # 重新标记 equipped 字段，保证每种类型只有一个 true
            for skill in player['martial_skills']:
                # 自动补全 type 字段
                if 'type' not in skill:
                    from enhanced_martial_system import get_martial_info
                    martial_info = get_martial_info(skill['name'])
                    skill['type'] = martial_info.get('类型') or martial_info.get('type') or '未知' if martial_info else '未知'
                    changed = True
                # 自动补全 equipped 字段
                if skill.get('type') in equipped_by_type:
                    skill['equipped'] = (skill['name'] == equipped_by_type[skill.get('type')])
                else:
                    # 默认未装备
                    skill['equipped'] = False
            if changed:
                await self.save_player_data(user_id, player)
            elif isinstance(player.get('martial_skills'), list):
                changed = False
                for skill in player['martial_skills']:
                    if 'type' not in skill:
                        from enhanced_martial_system import get_martial_info
                        martial_info = get_martial_info(skill['name'])
                        skill['type'] = martial_info.get('类型') or martial_info.get('type') or '未知' if martial_info else '未知'
                        changed = True
                if changed:
                    await self.save_player_data(user_id, player)
                # 兼容老账号：补全境界和武学点相关字段
                if 'experience' not in player:
                    player['experience'] = 0
                if 'skill_points' not in player:
                    player['skill_points'] = 0
        
        # 兼容老账号：补全equipment所有标准槽位
        standard_equipment_slots = [
            'main_hand', 'off_hand', 'helmet', 'necklace', 'armor', 'cloak', 'pants', 'shoes',
            'bracelet1', 'bracelet2', 'ring1', 'ring2', 'medal'
        ]
        if 'equipment' not in player or not isinstance(player['equipment'], dict):
            player['equipment'] = {slot: None for slot in standard_equipment_slots}
        else:
            for slot in standard_equipment_slots:
                if slot not in player['equipment']:
                    player['equipment'][slot] = None
        
        # 更新境界信息
        player['realm_info'] = self.get_realm_info(player['experience'])
        
        # 保存基础属性值，用于境界增益计算
        base_attrs = ['max_hp', 'max_mp', 'max_energy', 'attack', 'defense', 'dodge', 'crit']
        for attr in base_attrs:
            if attr in player and f'base_{attr}' not in player:
                player[f'base_{attr}'] = player[attr]
        
        # 应用装备、武功和境界增益并保存
        player = await self.update_player_bonuses_and_save(user_id, player)
        
        # 补全装备详细信息
        items_config = self.game_data.get('items', {})
        type_icon_map = {
            'weapon': '🗡️',
            'armor': '🛡️',
            'helmet': '⛑️',
            'necklace': '📿',
            'ring': '💍',
            'medal': '🎖️',
            'cloak': '🧥',
            'pants': '👖',
            'shoes': '👞',
            'bracelet': '🔗',
            'offhand': '🛡️',
        }
        for slot, equip in player['equipment'].items():
            if equip and isinstance(equip, dict):
                item_id = equip.get('id')
                if item_id and item_id in items_config:
                    equip_info = items_config[item_id]
                    equip['name'] = equip_info.get('name', '')
                    equip['icon'] = equip_info.get('icon') or type_icon_map.get(equip_info.get('type'), '📦')
                    equip['type'] = equip_info.get('type', '')
                else:
                    # 没查到物品配置，兜底
                    equip['icon'] = '📦'
        
        # 修正当前属性不超过最大值
        for k, maxk in [('hp', 'max_hp'), ('mp', 'max_mp'), ('energy', 'max_energy')]:
            if player.get(k, 0) > player.get(maxk, 0):
                player[k] = player[maxk]
        
        # 补全背包物品unique_id
        for item in player['inventory']:
            if 'unique_id' not in item or not item['unique_id']:
                item['unique_id'] = str(uuid.uuid4())
        
        # 物品标准化
        player['inventory'] = [standardize_item(i) for i in player['inventory']]
        # 装备栏标准化
        if 'equipment' in player:
            for slot, equip in player['equipment'].items():
                if equip:
                    player['equipment'][slot] = standardize_item(equip)
        
        response = {
            'type': 'player_data',
            'data': player
        }
        
        return response
    
    async def handle_get_inventory_data(self, websocket: WebSocketServerProtocol) -> dict:
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        data = await inv_handle_get_inventory_data(player)
        return {'type': 'inventory_data', 'data': data}
    
    async def handle_get_skills_data(self, websocket: WebSocketServerProtocol) -> dict:
        """获取武功数据"""
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        
        return {
            'type': 'skills_data',
            'data': player['skills']
        }
    
    async def handle_adventure(self, websocket: WebSocketServerProtocol) -> dict:
        """处理闯江湖事件"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        logger.info(f"[{client_address}] 闯江湖请求 - 客户端ID: {user_id}")
        logger.info(f"[闯江湖] user_id={user_id}, 当前在线玩家: {list(self.player_data.keys())}")
        
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法闯江湖")
            return {'type': 'error', 'data': {'message': '未认证'}}
        
        player = self.player_data.get(str(user_id))
        logger.info(f"[{client_address}] 玩家状态: {player.get('status', 'normal')}")
        if player.get('status', 'normal') != 'normal':
            logger.warning(f"[{client_address}] 玩家状态不佳，无法闯江湖: {player.get('status', 'normal')}")
            return {
                'type': 'error',
                'data': {'message': '你当前状态不佳，无法闯江湖'}
            }
        
        # 检查体力值
        current_energy = player.get('energy', 100)
        if current_energy < 1:
            logger.warning(f"[{client_address}] 体力值不足，无法闯江湖: {current_energy}")
            return {
                'type': 'error',
                'data': {'message': '体力值不足，无法闯江湖'}
            }
        
        # 消耗体力值
        player['energy'] = current_energy - 1

        # 增加闯江湖次数统计
        player['adventure_count'] = player.get('adventure_count', 0) + 1

        # 获取当前地图
        current_map_id = player.get('current_map', 'forest')

        # 获取地图信息
        maps = self.game_data['maps']
        map_data = next((m for m in maps.values() if m['id'] == current_map_id), None)
        if not map_data:
            logger.warning(f"[{client_address}] 未找到地图: {current_map_id}")
            return {'type': 'error', 'data': {'message': '地图信息错误'}}

        # 生成随机事件
        event = self.generate_random_event(current_map_id)
        
        # 处理事件
        map_level = map_data.get('level') or map_data.get('等级', 1)
        # 集成怪物权重逻辑：如果是NPC事件，按权重抽怪
        if event['type'] == 2:  # 遭遇NPC（怪物）
            monster = self.get_random_monster_for_map(current_map_id)
            if monster:
                await self.handle_encounter_monster(player, monster, websocket)
                return None  # 事件已推送或进入战斗
        result = await event_manager.process_event(event, player, map_level, server=self, user_id=user_id)
        # 关键修正：事件奖励后立即同步内存，防止后续流程覆盖 add_item_to_inventory 的背包数据
        self.player_data[str(user_id)] = player
        # 奇遇事件全服公告
        if event['type'] == 5:
            if '秘籍残页' in result.get('rewards', {}):
                player_name = player.get('name', '神秘玩家')
                book_name = result['rewards']['秘籍残页']
                announcement = f'{player_name}遇到了一位绝世高手获得了{book_name}'
                await self.broadcast_announcement(announcement)
            elif '装备' in result.get('rewards', {}):
                player_name = player.get('name', '神秘玩家')
                equip_name = result['rewards']['装备']
                announcement = f'{player_name}走了狗屎运捡到了{equip_name}'
                await self.broadcast_announcement(announcement)
        
        # 添加历练值奖励（应用境界增益）
        base_experience_reward = map_data.get('experience_reward', 10)
        experience_gain_bonus = player.get('experience_gain_bonus', 0)
        experience_reward = base_experience_reward + experience_gain_bonus
        
        old_experience = player.get('experience', 0)
        player['experience'] = old_experience + experience_reward
        result['rewards']['历练值'] = experience_reward
        
        # 保存当前体力值，避免被境界增益覆盖
        current_energy_after_consumption = player['energy']
        
        # 更新境界信息
        player['realm_info'] = self.get_realm_info(player['experience'])
        
        # 使用统一的增益系统应用境界增益，避免重复应用
        player = await self.update_player_bonuses_and_save(str(user_id), player)
        
        # 恢复体力值，确保消耗不被覆盖
        player['energy'] = current_energy_after_consumption
        
        # 检查是否突破境界
        old_realm = self.get_realm_info(old_experience)['current_realm']
        new_realm = player['realm_info']['current_realm']
        if old_realm != new_realm:
            result['realm_breakthrough'] = {
                'old_realm': old_realm,
                'new_realm': new_realm,
                'message': f'恭喜！你突破到了{new_realm}境界！'
            }
            # 境界突破奖励武学点（应用境界增益）
            base_skill_points_reward = 5
            skill_points_gain_bonus = player.get('skill_points_gain_bonus', 0)
            skill_points_reward = base_skill_points_reward + skill_points_gain_bonus
            player['skill_points'] = player.get('skill_points', 0) + skill_points_reward
            result['rewards']['武学点'] = skill_points_reward

        # 保存玩家数据
        self.player_data[str(user_id)] = player
        
        # 记录事件日志
        log_entry = {
            'type': event['type'],
            'name': event['name'],
            'content': result['content'],
            'rewards': result.get('rewards', {}),
            'timestamp': datetime.now().isoformat()
        }
        player['event_log'].insert(0, log_entry)
        if len(player['event_log']) > 50:
            player['event_log'] = player['event_log'][:50]
        
        # 采集事件特殊处理：先推送采集点事件，不直接采集
        if event['type'] == 3:  # 采集事件
            gather_type_map = {
                'herb': 'herbalism',
                'mining': 'mining',
                'logging': 'logging',
                'skinning': 'skinning'
            }

            # 根据地图配置决定采集类型
            map_gather_config = map_data.get('采集物品', {})
            available_gather_types = []
            gather_type_weights = []

            if map_gather_config:
                # 检查是否是新格式（支持权重配置）
                if '采集类型权重' in map_gather_config:
                    # 新格式：有权重配置
                    type_weights = map_gather_config['采集类型权重']
                    items_config = map_gather_config.get('采集物品配置', {})

                    for gather_type_key, weight in type_weights.items():
                        if gather_type_key in items_config and items_config[gather_type_key]:
                            available_gather_types.append(gather_type_key)
                            gather_type_weights.append(weight)

                elif any(key in map_gather_config for key in ['herbalism', 'mining', 'logging', 'skinning']):
                    # 旧格式：直接配置采集类型（等权重）
                    for gather_type_key in ['herbalism', 'mining', 'logging', 'skinning']:
                        if gather_type_key in map_gather_config and isinstance(map_gather_config[gather_type_key], dict):
                            if map_gather_config[gather_type_key]:  # 确保不为空
                                available_gather_types.append(gather_type_key)
                                gather_type_weights.append(1.0)  # 等权重

            if available_gather_types:
                # 根据权重选择采集类型
                import random
                if gather_type_weights:
                    gather_type_std = random.choices(available_gather_types, weights=gather_type_weights)[0]
                    print(f"[DEBUG] 根据权重选择采集类型: {gather_type_std} (权重: {dict(zip(available_gather_types, gather_type_weights))})")
                else:
                    gather_type_std = random.choice(available_gather_types)
                    print(f"[DEBUG] 等权重随机选择采集类型: {gather_type_std}")
            else:
                # 回退到默认或事件指定的类型
                gather_type = event.get('gatherType', 'herbalism')
                gather_type_std = gather_type_map.get(gather_type, 'herbalism')
                print(f"[DEBUG] 使用默认采集类型: {gather_type_std}")
            map_level = map_data.get('level', 1)

            # 修正：使用工具类型名称而不是具体工具名称
            tool_type_names = {
                'mining': '矿镐',
                'logging': '斧头',
                'herbalism': '镰刀',
                'skinning': '小刀'
            }
            skill_names = {
                'mining': '挖矿',
                'logging': '伐木',
                'herbalism': '采药',
                'skinning': '剥皮'
            }
            tool_name = tool_type_names.get(gather_type_std, '工具')
            skill_name = skill_names.get(gather_type_std, '采集')

            # 根据地图配置选择采集物品名称
            map_gather_items = map_data.get('采集物品', {})
            current_map_id = player.get('current_map', 'unknown')
            print(f"[DEBUG] 采集事件生成 - 地图: {current_map_id}, 采集类型: {gather_type_std}, 采集物品配置: {map_gather_items}")

            resource_name = '资源'  # 默认值

            if map_gather_items:
                # 检查是否是新格式（有采集物品配置）
                if '采集物品配置' in map_gather_items and gather_type_std in map_gather_items['采集物品配置']:
                    # 新格式：有采集物品配置
                    type_items = map_gather_items['采集物品配置'][gather_type_std]
                    if type_items:
                        import random
                        # 根据概率权重选择
                        items = list(type_items.keys())
                        weights = list(type_items.values())
                        resource_name = random.choices(items, weights=weights)[0]
                        print(f"[DEBUG] 新格式(采集物品配置) - 从{gather_type_std}类型中选择资源: {resource_name} (权重: {dict(zip(items, weights))})")
                    else:
                        print(f"[DEBUG] 新格式 - {gather_type_std}类型无可用资源")
                elif gather_type_std in map_gather_items and isinstance(map_gather_items[gather_type_std], dict):
                    # 旧格式：按采集类型分组
                    type_items = map_gather_items[gather_type_std]
                    if type_items:
                        import random
                        # 根据概率权重选择
                        items = list(type_items.keys())
                        weights = list(type_items.values())
                        resource_name = random.choices(items, weights=weights)[0]
                        print(f"[DEBUG] 旧格式(分组) - 从{gather_type_std}类型中选择资源: {resource_name}")
                    else:
                        print(f"[DEBUG] 旧格式 - {gather_type_std}类型无可用资源")
                elif isinstance(map_gather_items, dict) and any(isinstance(v, (int, float)) for v in map_gather_items.values()):
                    # 最旧格式：直接列出物品（兼容性）
                    import random
                    items = list(map_gather_items.keys())
                    weights = list(map_gather_items.values())
                    resource_name = random.choices(items, weights=weights)[0]
                    print(f"[DEBUG] 最旧格式 - 选择资源: {resource_name}")
                else:
                    print(f"[DEBUG] 地图配置格式无法识别: {map_gather_items}")

            if resource_name == '资源':
                # 回退到原来的逻辑
                gather_info = event_manager.get_gather_event_for_map_level(gather_type_std, map_level)
                resource_name = gather_info['resource']['name'] if gather_info and gather_info['resource'] else '资源'
                print(f"[DEBUG] 使用默认逻辑选择资源: {resource_name}")

            # 获取采集品的等级要求
            from item_system import load_items_config
            items_config = load_items_config()
            resource_level = 1  # 默认等级

            # 查找采集品的等级
            for item_id, item_data in items_config.items():
                if item_data.get('name') == resource_name:
                    resource_level = int(item_data.get('level', 1))
                    break

            print(f"[DEBUG] 采集品 {resource_name} 需要等级: {resource_level}")

            # 根据采集品等级确定需要的工具描述
            if resource_level <= 1:
                required_tool_desc = f"{tool_name}(1级以上) - {skill_name}技能"
            else:
                required_tool_desc = f"{tool_name}({resource_level}级以上) - {skill_name}技能"

            gather_info_dict = {
                'type': 3,  # 采集事件类型
                'eventType': 'gathering',  # 保留原有字段用于兼容
                'content': f'你发现了一片{resource_name}资源地，可以采集{resource_name}。',
                'gatherType': gather_type_std,
                'maxTimes': 1,
                'requiredTool': tool_name,
                'requiredToolDesc': required_tool_desc,
                'resource': resource_name,
                'resourceLevel': resource_level,
                'fixedResource': resource_name  # 新增：固定采集的资源名称
            }
            response = {
                'type': 'game_event',
                'data': gather_info_dict
            }
            logger.info(f"[{client_address}] 采集点事件响应: {response}")
            return response
        
        # 其他事件正常处理
        response = {
            'type': 'game_event',
            'data': {
                'type': event['type'],
                'content': result['content'],
                'rewards': result.get('rewards', {})
            }
        }
        return response

    async def handle_gather_action(self, websocket: WebSocketServerProtocol) -> dict:
        """处理采集请求，返回采集结果事件"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法采集")
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[{client_address}] 玩家数据不存在: {user_id}")
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        # 获取采集类型和固定资源
        gather_type = None
        fixed_resource = None

        # 从消息数据中获取采集类型和固定资源
        if hasattr(websocket, 'last_message_data') and websocket.last_message_data:
            gather_type = websocket.last_message_data.get('gatherType')
            fixed_resource = websocket.last_message_data.get('fixedResource')

        # 兼容旧的方式
        if not gather_type and hasattr(websocket, 'last_gather_type'):
            gather_type = websocket.last_gather_type

        if not gather_type:
            return {'type': 'error', 'data': {'message': '采集类型未指定'}}

        print(f"[DEBUG] 采集请求 - 类型: {gather_type}, 固定资源: {fixed_resource}")

        # 采集点类型：mining/logging/herbalism/skinning
        # 采集判定
        result = await event_manager.process_gathering(player, gather_type, fixed_resource)

        # 处理奖励：将采集到的物品添加到背包
        rewards = result.get('rewards', {})
        if '物品' in rewards:
            item_data = rewards['物品']
            # 添加物品到背包
            success = await self.add_item_to_inventory(str(user_id), player, {
                'id': item_data['id'],
                'quantity': item_data['quantity']
            })
            if not success:
                logger.warning(f"[{client_address}] 采集物品添加失败: {item_data['name']} x{item_data['quantity']}")

        # 写入日志
        log_entry = {
            'type': 'gathering_result',
            'name': '采集结果',
            'content': result['content'],
            'rewards': result.get('rewards', {}),
            'timestamp': datetime.now().isoformat()
        }
        player['event_log'].insert(0, log_entry)
        if len(player['event_log']) > 50:
            player['event_log'] = player['event_log'][:50]

        # 保存玩家数据
        self.save_player_data(str(user_id), player)

        response = {
            'type': 'gathering_result',
            'data': {
                'content': result['content'],
                'rewards': result.get('rewards', {}),
                'player_data': {
                    'inventory': player.get('inventory', []),
                    'gather_skills': player.get('gather_skills', {})
                }
            }
        }
        return response

    def generate_random_event(self, map_id: str = None) -> dict:
        """生成随机事件"""
        # 使用事件管理器生成随机事件
        return event_manager.generate_random_event(map_id)
    
    def process_event(self, event: dict, player: dict, map_level: int = 1) -> dict:
        """处理事件"""
        event_type = event['type']
        if event_type == 1:  # 好运事件
            return self.process_good_fortune(player)
        elif event_type == 2:  # 遭遇NPC
            return self.process_npc_encounter(player)
        # 采集事件不在这里处理，交由 handle_adventure 单独处理
        elif event_type == 4:  # 普通事件
            return self.process_empty_event(player, map_level)
        elif event_type == 5:  # 奇遇事件
            return self.process_adventure(player)
        elif event_type == 6:  # 恩怨事件
            return self.process_enmity(player)
        elif event_type == 7:  # 组队事件
            return self.process_team_event(player)
        elif event_type == 8:  # 商队事件
            return self.process_caravan_event(player)
        elif event_type == 9:  # 江湖传闻
            return self.process_rumor_event(player)
        elif event_type == 10:  # 天气事件
            return self.process_weather_event(player)
        elif event_type == 11:  # 神秘事件
            return self.process_mystery_event(player)
        elif event_type == 12:  # 节日事件
            return self.process_festival_event(player)
        else:
            return {
                'content': '你遇到了一个神秘的事件...',
                'rewards': {'历练值': map_level * 3}
            }
    
    def process_good_fortune(self, player: dict) -> dict:
        """处理好运事件 - 已迁移到 event_manager.py"""
        # 这个方法已经不再使用，统一使用 event_manager.py 中的版本
        # 保留简化版本用于兼容性
        import random

        base_money = random.randint(100, 300)
        player['money'] = player.get('money', 0) + base_money

        return {
            'content': '你遇到了好运，获得了一些银两。',
            'rewards': {'银两': base_money}
        }
    
    def process_npc_encounter(self, player: dict) -> dict:
        """处理NPC遭遇"""
        import random
        user_id = player.get('id')
        npc_encounters = [
            {
                'name': '神秘剑客',
                'content': '你遇到了一位神秘的剑客，他正在练习剑法。',
                'type': 'friendly',
                'rewards': {'武功经验': 40, '声望': 15}
            },
            {
                'name': '江湖郎中',
                'content': '你遇到了一位江湖郎中，他正在为病人治病。',
                'type': 'friendly',
                'rewards': {'疗伤药': 5, '声望': 10}
            },
            {
                'name': '山贼头目',
                'content': '你遇到了一个山贼头目，他想要抢劫你！',
                'type': 'hostile',
                'enemy': {'hp': 180, 'attack': 25, 'skills': ['山贼刀法']},
                'rewards': {'银两': 200, '声望': 20}
            },
            {
                'name': '隐世高人',
                'content': '你遇到了一位隐世高人，他正在钓鱼。',
                'type': 'friendly',
                'rewards': {'悟性': 15, '声望': 25}
            },
            {
                'name': '江湖艺人',
                'content': '你遇到了一位江湖艺人，他正在表演杂技。',
                'type': 'friendly',
                'rewards': {'银两': 50, '声望': 5}
            },
            {
                'name': '武林前辈',
                'content': '你遇到了一位武林前辈，他正在指导弟子练武。',
                'type': 'friendly',
                'rewards': {'武功经验': 60, '声望': 20}
            },
            {
                'name': '江湖骗子',
                'content': '你遇到了一个江湖骗子，他想要骗你的钱财！',
                'type': 'hostile',
                'enemy': {'hp': 80, 'attack': 15, 'skills': ['骗术']},
                'rewards': {'银两': 100, '声望': 10}
            },
            {
                'name': '游方僧人',
                'content': '你遇到了一位游方僧人，他正在化缘。',
                'type': 'friendly',
                'rewards': {'悟性': 10, '声望': 8}
            },
            {
                'name': '江湖豪侠',
                'content': '你遇到了一位江湖豪侠，他正在喝酒。',
                'type': 'friendly',
                'rewards': {'银两': 80, '声望': 12}
            },
            {
                'name': '神秘杀手',
                'content': '你被一个神秘杀手盯上了，他想要取你性命！',
                'type': 'hostile',
                'enemy': {'hp': 120, 'attack': 30, 'skills': ['暗杀术']},
                'rewards': {'银两': 300, '声望': 30}
            }
        ]
        encounter = random.choice(npc_encounters)
        content = encounter['content']
        rewards = encounter['rewards'].copy()
        if encounter['type'] == 'hostile':
            battle_result = simulate_battle(player, encounter['enemy'])
            if battle_result['win']:
                content += f"\n经过一番激战，你战胜了{encounter['name']}！"
                self._apply_rewards(user_id, player, rewards)
            else:
                content += f"\n你被{encounter['name']}击败了，受了重伤。"
                player['hp'] = max(1, player['hp'] - 25)
                player['status'] = 'injured'
                rewards = {'体力': -15}
        else:
            content += f"\n你与{encounter['name']}进行了友好的交流。"
            self._apply_rewards(user_id, player, rewards)
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_empty_event(self, player: dict, map_level: int = 1) -> dict:
        """处理普通事件"""
        import random
        
        stories = [
            '你漫步在青山绿水间，感受着江湖的宁静与美好。微风轻拂，带来阵阵花香。',
            '远处传来悠扬的笛声，让你想起了那些江湖传说。笛声如泣如诉，诉说着江湖的恩怨情仇。',
            '你坐在山巅，看着云卷云舒，心中涌起无限感慨。江湖路远，侠义长存。',
            '你在一家小茶馆里品茶，听着茶客们谈论江湖轶事，感受着市井生活的温馨。',
            '你漫步在古镇的石板路上，看着古老的建筑，仿佛穿越回了那个侠客纵横的年代。',
            '你在一处瀑布前驻足，听着水声轰鸣，感受着大自然的力量，心中有所感悟。',
            '你遇到了一位老渔夫，他正在修补渔网。你与他闲聊，听他讲述江湖往事。',
            '你在一座古桥上驻足，看着桥下的流水，想起了那些在桥上相遇的江湖故事。',
            '你在一处竹林中小憩，听着竹叶沙沙作响，感受着这份宁静与祥和。',
            '你遇到了一位卖艺的老人，他正在表演杂技。你驻足观看，为他的技艺所折服。',
            '你在一处花海中漫步，看着五彩缤纷的花朵，心情变得愉悦起来。',
            '你遇到了一位正在练字的老先生，他的字迹苍劲有力，你从中感受到了武学的真谛。'
        ]
        
        story = random.choice(stories)
        
        # 根据地图等级给予历练值奖励
        base_experience = map_level * 5  # 基础历练值 = 地图等级 * 5
        experience_bonus = random.randint(0, map_level * 2)  # 随机额外历练值
        total_experience = base_experience + experience_bonus
        
        # 偶尔给予一些小的奖励
        rewards = {'历练值': total_experience}
        if random.random() < 0.3:  # 30%概率获得小奖励
            small_rewards = [
                {'体力': 5},
                {'内力': 3},
                {'声望': 1},
                {'悟性': 2}
            ]
            additional_reward = random.choice(small_rewards)
            rewards.update(additional_reward)
            
            # 应用奖励
            if '体力' in rewards:
                player['stamina'] = min(player.get('maxStamina', 100), player.get('stamina', 100) + rewards['体力'])
            if '内力' in rewards:
                player['mp'] = min(player.get('maxMp', 50), player.get('mp', 50) + rewards['内力'])
            if '声望' in rewards:
                player['reputation'] = player.get('reputation', 0) + rewards['声望']
        
        return {
            'content': story,
            'rewards': rewards
        }
    
    def process_adventure(self, player: dict) -> dict:
        """处理奇遇事件"""
        import random
        
        # 残页物品ID列表
        canye_items = [
            'basic_jianfa_canyie', 'basic_daofa_canyie', 'basic_quanfa_canyie',
            'basic_zhaojia_canyie', 'basic_qinggong_canyie', 'basic_neigong_canyie',
            'basic_anqifa_canyie', 'dushu_xiezi_canyie', 'taiji_quan_canyie',
            'dugu_jiujian_canyie', 'xianglong_shibazhang_canyie', 'dagou_bangfa_canyie',
            'lingbo_weibu_canyie', 'shenxing_bai bian_canyie', 'zixia_gong_canyie',
            'huntian_qigong_canyie', 'yunu_xinjing_canyie', 'xiaoyao_xinfa_canyie',
            'bibo_xinfa_canyie', 'yijin_jing_canyie', 'shenlong_xinfa_canyie',
            'murong_xinfa_canyie', 'tianshan_liuyangzhang_canyie', 'tianyu_qijian_canyie',
            'wanhua_jianfa_canyie', 'basic_dufa_canyie', 'basic_anqi_canyie'
        ]
        
        adventure_events = [
            {
                'content': '你在一座古墓中发现了一本失传已久的武功秘籍！',
                'rewards': {'残页': random.choice(canye_items), '声望': 20, '悟性': 15}
            },
            {
                'content': '你遇到了一位隐世高人，他传授了你一门绝世武功！',
                'rewards': {'残页': random.choice(canye_items), '声望': 30, '武功经验': 100}
            },
            {
                'content': '你发现了一个隐藏的宝藏，里面有无数的金银财宝！',
                'rewards': {'银两': random.randint(800, 1500), '声望': 15}
            },
            {
                'content': '你在一处秘境中遇到了一只神兽，它送给你一颗神珠！',
                'rewards': {'神珠': 1, '声望': 25, '内力': 50}
            },
            {
                'content': '你在一座古塔中发现了传说中的神兵利器！',
                'rewards': {'装备': '神兵利器', '声望': 20, '攻击力': 50}
            },
            {
                'content': '你遇到了一位仙人，他指点你修炼，让你功力大增！',
                'rewards': {'内力': 100, '悟性': 20, '声望': 30}
            },
            {
                'content': '你在一处遗迹中发现了古代武学的秘密！',
                'rewards': {'残页': random.choice(canye_items), '悟性': 25, '声望': 20}
            },
            {
                'content': '你遇到了一位江湖传奇人物，他传授了你一些绝技！',
                'rewards': {'残页': random.choice(canye_items), '声望': 35, '武功经验': 80}
            }
        ]
        
        event = random.choice(adventure_events)
        content = event['content']
        rewards = event['rewards'].copy()
        # 统一通过 reward_system 发放奖励
        add_reward_to_player(player, rewards)
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_enmity(self, player: dict) -> dict:
        """处理恩怨事件"""
        import random
        
        enmity_events = [
            {
                'content': '你遇到了一个仇家，他认出了你，二话不说就向你出手！',
                'enemy': {'name': '仇家', 'hp': 150, 'attack': 25, 'skills': ['复仇刀法']},
                'rewards': {'声望': 10, '银两': 100}
            },
            {
                'content': '你被一群山贼包围了，他们想要抢劫你！',
                'enemy': {'name': '山贼团伙', 'hp': 200, 'attack': 20, 'skills': ['围攻战术']},
                'rewards': {'声望': 15, '银两': 150}
            },
            {
                'content': '你遇到了一个江湖败类，他正在欺负无辜百姓，你挺身而出！',
                'enemy': {'name': '江湖败类', 'hp': 120, 'attack': 22, 'skills': ['阴险毒招']},
                'rewards': {'声望': 20, '银两': 80}
            },
            {
                'content': '你被一个神秘杀手盯上了，他想要取你性命！',
                'enemy': {'name': '神秘杀手', 'hp': 100, 'attack': 30, 'skills': ['暗杀术']},
                'rewards': {'声望': 25, '银两': 200}
            },
            {
                'content': '你遇到了一个武林败类，他想要抢夺你的武功秘籍！',
                'enemy': {'name': '武林败类', 'hp': 180, 'attack': 28, 'skills': ['抢夺术']},
                'rewards': {'声望': 18, '银两': 120}
            }
        ]
        
        event = random.choice(enmity_events)
        content = event['content']
        enemy = event['enemy']
        rewards = event['rewards'].copy()
        
        # 进行战斗
        battle_result = self.simulate_battle(player, enemy)
        
        if battle_result['win']:
            content += f"\n经过一番激战，你战胜了{enemy['name']}！"
            # 应用奖励
            if '银两' in rewards:
                player['money'] += rewards['银两']
            if '声望' in rewards:
                player['reputation'] = player.get('reputation', 0) + rewards['声望']
        else:
            content += f"\n你被{enemy['name']}击败了，受了重伤。"
            player['hp'] = max(1, player['hp'] - 30)
            player['status'] = 'injured'
            rewards = {'体力': -20}
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_team_event(self, player: dict) -> dict:
        """处理组队事件"""
        import random
        
        team_events = [
            {
                'content': '你遇到了一群江湖侠客，他们邀请你一起剿灭山贼。',
                'teammates': ['铁掌侠', '飞剑客', '神刀手'],
                'enemy': {'name': '山贼团伙', 'hp': 300, 'attack': 25},
                'rewards': {'声望': 30, '银两': 200, '团队经验': 50}
            },
            {
                'content': '你与几位武林同道一起护送商队，途中遇到了劫匪。',
                'teammates': ['镖师', '剑客', '武师'],
                'enemy': {'name': '劫匪团伙', 'hp': 250, 'attack': 22},
                'rewards': {'声望': 25, '银两': 150, '团队经验': 40}
            },
            {
                'content': '你与江湖朋友一起探索古墓，遇到了守护者。',
                'teammates': ['考古学家', '机关师', '武学大师'],
                'enemy': {'name': '古墓守护者', 'hp': 400, 'attack': 35},
                'rewards': {'声望': 40, '武功经验': 100, '团队经验': 80}
            },
            {
                'content': '你与武林同道一起参加武林大会，与对手切磋。',
                'teammates': ['少林弟子', '武当弟子', '峨眉弟子'],
                'enemy': {'name': '对手团队', 'hp': 280, 'attack': 28},
                'rewards': {'声望': 35, '武功经验': 80, '团队经验': 60}
            }
        ]
        
        event = random.choice(team_events)
        content = event['content']
        teammates = event['teammates']
        enemy = event['enemy']
        rewards = event['rewards'].copy()
        
        # 组队战斗，成功率更高
        battle_result = self.simulate_battle(player, enemy)
        
        if battle_result['win']:
            content += f"\n在{', '.join(teammates)}的帮助下，你们成功战胜了{enemy['name']}！"
            # 应用奖励
            if '银两' in rewards:
                player['money'] += rewards['银两']
            if '声望' in rewards:
                player['reputation'] = player.get('reputation', 0) + rewards['声望']
            if '武功经验' in rewards:
                # 这里应该增加武功经验
                pass
        else:
            content += f"\n尽管有队友的帮助，你们还是被{enemy['name']}击败了。"
            player['hp'] = max(1, player['hp'] - 15)
            rewards = {'体力': -10}
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_caravan_event(self, player: dict) -> dict:
        """处理商队事件"""
        import random
        
        caravans = [
            {
                'name': '西域商队',
                'desc': '来自西域的商队，带来了异域珍宝',
                'items': ['silk_scarf', 'jade_ornament', 'spice_bag'],
                'prices': [200, 500, 150]
            },
            {
                'name': '江南商队',
                'desc': '江南水乡的商队，贩卖丝绸茶叶',
                'items': ['silk_robe', 'tea_leaves', 'bamboo_fan'],
                'prices': [300, 100, 80]
            },
            {
                'name': '北方商队',
                'desc': '北方草原的商队，带来皮毛马匹',
                'items': ['fur_coat', 'horse_saddle', 'wool_blanket'],
                'prices': [400, 600, 120]
            }
        ]
        
        caravan = random.choice(caravans)
        item_index = random.randint(0, len(caravan['items']) - 1)
        item_id = caravan['items'][item_index]
        price = caravan['prices'][item_index]
        
        # 检查玩家是否有足够的银两
        if player.get('money', 0) >= price:
            player['money'] -= price
            # 这里应该添加物品到背包
            content = f"你遇到了{caravan['name']}。{caravan['desc']}你花费{price}银两购买了一件商品。"
            rewards = {'银两': -price, '物品': '商队商品'}
        else:
            content = f"你遇到了{caravan['name']}。{caravan['desc']}可惜你银两不足，无法购买。"
            rewards = {}
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_rumor_event(self, player: dict) -> dict:
        """处理江湖传闻事件"""
        import random
        
        rumors = [
            {
                'content': '听说少林寺藏经阁最近有异动，可能有绝世武功现世。',
                'effect': 'reputation',
                'value': 10
            },
            {
                'content': '江湖传闻，最近有一批神秘高手在各地出现，似乎在寻找什么。',
                'effect': 'awareness',
                'value': 5
            },
            {
                'content': '听说最近江湖上出现了一个神秘的杀手组织，专门针对武林高手。',
                'effect': 'caution',
                'value': 3
            },
            {
                'content': '传闻说，最近有一批珍贵的药材在深山中出现，价值连城。',
                'effect': 'knowledge',
                'value': 8
            },
            {
                'content': '听说最近江湖上出现了一个神秘的剑客，剑法出神入化。',
                'effect': 'inspiration',
                'value': 6
            }
        ]
        
        rumor = random.choice(rumors)
        content = rumor['content']
        rewards = {}
        
        # 根据传闻类型给予不同奖励
        if rumor['effect'] == 'reputation':
            player['reputation'] = player.get('reputation', 0) + rumor['value']
            rewards['声望'] = rumor['value']
        elif rumor['effect'] == 'awareness':
            # 增加对江湖的认知
            rewards['江湖认知'] = rumor['value']
        elif rumor['effect'] == 'caution':
            # 增加谨慎度
            rewards['谨慎度'] = rumor['value']
        elif rumor['effect'] == 'knowledge':
            # 增加知识
            rewards['知识'] = rumor['value']
        elif rumor['effect'] == 'inspiration':
            # 增加灵感
            rewards['灵感'] = rumor['value']
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_weather_event(self, player: dict) -> dict:
        """处理天气事件"""
        import random
        
        weather_events = [
            {
                'weather': '晴天',
                'content': '阳光明媚，你感到心情愉悦，修炼效率提升。',
                'effect': 'positive',
                'rewards': {'修炼效率': 1.2}
            },
            {
                'weather': '阴天',
                'content': '阴云密布，你感到有些压抑，但也能静心修炼。',
                'effect': 'neutral',
                'rewards': {}
            },
            {
                'weather': '雨天',
                'content': '细雨绵绵，你躲在屋檐下，听着雨声思考武学。',
                'effect': 'positive',
                'rewards': {'悟性': 5}
            },
            {
                'weather': '雪天',
                'content': '雪花纷飞，你感到寒冷，但雪景让你想起了江湖传说。',
                'effect': 'mixed',
                'rewards': {'体力': -10, '灵感': 8}
            },
            {
                'weather': '雷雨',
                'content': '电闪雷鸣，你感到震撼，对天地之力有了新的感悟。',
                'effect': 'positive',
                'rewards': {'内力': 20, '悟性': 10}
            }
        ]
        
        event = random.choice(weather_events)
        content = event['content']
        rewards = event['rewards']
        
        # 应用天气效果
        if '体力' in rewards:
            player['stamina'] = max(1, player.get('stamina', 100) + rewards['体力'])
        if '内力' in rewards:
            player['mp'] = min(player.get('maxMp', 50), player.get('mp', 50) + rewards['内力'])
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_mystery_event(self, player: dict) -> dict:
        """处理神秘事件"""
        import random
        
        mystery_events = [
            {
                'content': '你发现了一个古老的石碑，上面刻着神秘的符文。经过仔细研究，你领悟了一些武学奥义。',
                'rewards': {'武功经验': 50, '悟性': 15}
            },
            {
                'content': '你遇到了一个神秘的老人，他传授了你一些江湖经验。',
                'rewards': {'江湖经验': 30, '声望': 10}
            },
            {
                'content': '你发现了一个隐藏的山洞，里面有一些珍贵的药材。',
                'rewards': {'药材': '珍贵药材 x3', '银两': 100}
            },
            {
                'content': '你遇到了一只受伤的灵兽，你帮助了它。它感激地送给你一颗灵珠。',
                'rewards': {'灵珠': 1, '声望': 20}
            },
            {
                'content': '你发现了一本残破的武功秘籍，虽然不完整，但对你有所启发。',
                'rewards': {'武功经验': 40, '悟性': 10}
            }
        ]
        
        event = random.choice(mystery_events)
        rewards = event['rewards'].copy()
        add_reward_to_player(player, rewards)
        return {
            'content': event['content'],
            'rewards': rewards
        }
    
    def process_festival_event(self, player: dict) -> dict:
        """处理节日事件"""
        import random
        from datetime import datetime
        
        current_month = datetime.now().month
        
        festival_events = [
            {
                'name': '春节',
                'months': [1, 2],
                'content': '春节到了！家家户户张灯结彩，你感受到了浓浓的节日气氛。',
                'rewards': {'银两': 200, '声望': 15, '节日祝福': '新年快乐！'}
            },
            {
                'name': '端午节',
                'months': [5, 6],
                'content': '端午节到了！你看到了赛龙舟的热闹场面，还吃到了美味的粽子。',
                'rewards': {'体力': 20, '节日祝福': '端午安康！'}
            },
            {
                'name': '中秋节',
                'months': [9, 10],
                'content': '中秋节到了！明月当空，你与江湖朋友一起赏月，品尝月饼。',
                'rewards': {'内力': 30, '声望': 10, '节日祝福': '中秋快乐！'}
            },
            {
                'name': '重阳节',
                'months': [10, 11],
                'content': '重阳节到了！你登高望远，感受秋高气爽，心情愉悦。',
                'rewards': {'体力': 25, '悟性': 8, '节日祝福': '重阳安康！'}
            }
        ]
        
        # 根据当前月份选择合适的节日
        suitable_festivals = [f for f in festival_events if current_month in f['months']]
        if not suitable_festivals:
            # 如果没有合适的节日，随机选择一个
            festival = random.choice(festival_events)
        else:
            festival = random.choice(suitable_festivals)
        
        content = festival['content']
        rewards = festival['rewards'].copy()
        
        # 应用奖励
        if '银两' in rewards:
            player['money'] += rewards['银两']
        if '体力' in rewards:
            player['stamina'] = min(player.get('maxStamina', 100), player.get('stamina', 100) + rewards['体力'])
        if '内力' in rewards:
            player['mp'] = min(player.get('maxMp', 50), player.get('mp', 50) + rewards['内力'])
        if '声望' in rewards:
            player['reputation'] = player.get('reputation', 0) + rewards['声望']
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def simulate_battle(self, player: dict, enemy: dict) -> dict:
        """模拟战斗，支持敌人武功出招描述"""
        return self.battle_handler.simulate_battle(player, enemy)
    
    async def add_item_to_inventory(self, user_id: str, player: dict, item: dict):
        return await inv_add_item_to_inventory(player, item, user_id=user_id, save_func=self.save_player_data)
    
    async def handle_meditation(self, websocket: WebSocketServerProtocol) -> dict:
        """处理打坐（MUD式持续打坐）"""
        import asyncio
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        # 必须掌握基本内功
        martial_skills = player.get('martial_skills', {})
        has_basic_neigong = False
        # 兼容dict和list
        if isinstance(martial_skills, dict):
            skills_iter = martial_skills.values()
        else:
            skills_iter = martial_skills
        for skill in skills_iter:
            if skill.get('name') == '基本内功':
                has_basic_neigong = True
                break
        if not has_basic_neigong:
            return {'type': 'error', 'data': {'message': '你尚未掌握基本内功，无法打坐修炼'}}
        # 只有在长安城可打坐
        if player.get('current_map') != 'changan':
            return {'type': 'error', 'data': {'message': '只有在长安城内才能打坐修炼！'}}
        if player['mp'] >= player['max_mp']:
            return {'type': 'error', 'data': {'message': '你的内力已满，无需打坐'}}
        if player['spirit'] < 1:
            return {'type': 'error', 'data': {'message': '精力不足，无法打坐'}}
        # 进入打坐状态
        player['status'] = 'meditating'
        await self.notify_player_data_update(user_id, player)
        # MUD风格打坐起始文本
        log_entry = {
            'type': 'meditation',
            'name': '打坐',
            'content': '你盘膝而坐，凝神静气，开始打坐恢复内力。',
            'timestamp': datetime.now().isoformat()
        }
        player['event_log'].insert(0, log_entry)
        if len(player['event_log']) > 50:
            player['event_log'] = player['event_log'][:50]
        await self.save_player_data(user_id, player)
        await self.notify_player_data_update(user_id, player)
        await websocket.send(json.dumps({'type': 'meditation_log', 'data': {'message': log_entry['content']}}, ensure_ascii=False))
        # 持续打坐循环
        while player['spirit'] >= 1 and player['mp'] < player['max_mp']:
            restore_amount = min(5, player['max_mp'] - player['mp'])
            player['spirit'] -= 1
            player['mp'] += restore_amount
            if player['mp'] > player['max_mp']:
                player['mp'] = player['max_mp']
            # MUD风格每步文本
            step_log = {
                'type': 'meditation',
                'name': '打坐',
                'content': f"你调息吐纳，消耗1点精力，恢复{restore_amount}点内力。当前内力：{player['mp']}/{player['max_mp']}，精力：{player['spirit']}/{player['max_spirit']}",
                'timestamp': datetime.now().isoformat()
            }
            player['event_log'].insert(0, step_log)
            if len(player['event_log']) > 50:
                player['event_log'] = player['event_log'][:50]
            await self.save_player_data(user_id, player)
            await self.notify_player_data_update(user_id, player)
            await websocket.send(json.dumps({'type': 'meditation_log', 'data': {'message': step_log['content']}}, ensure_ascii=False))
            await asyncio.sleep(0.5)
        # 打坐结束
        if player['mp'] >= player['max_mp']:
            msg = '你缓缓收功，内力已恢复至巅峰，打坐结束。'
        else:
            msg = '你精力耗尽，只得暂时收功，打坐结束。'
        end_log = {
            'type': 'meditation',
            'name': '打坐',
            'content': msg,
            'timestamp': datetime.now().isoformat()
        }
        player['status'] = 'normal'
        player['event_log'].insert(0, end_log)
        if len(player['event_log']) > 50:
            player['event_log'] = player['event_log'][:50]
        await self.save_player_data(user_id, player)
        await self.notify_player_data_update(user_id, player)
        await websocket.send(json.dumps({'type': 'meditation_log', 'data': {'message': msg}}, ensure_ascii=False))
        return {'type': 'success', 'data': {'message': msg}}
    
    async def handle_healing(self, websocket: WebSocketServerProtocol) -> dict:
        """处理疗伤（MUD式持续疗伤）"""
        import asyncio
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        if player.get('status', 'normal') != 'normal':
            return {'type': 'error', 'data': {'message': '你当前状态无法疗伤'}}
        # 查找已装备的内功心法，且不是"基本内功"
        martial_skills = player.get('martial_skills', {})
        equipped_neigong = None
        # 兼容dict和list
        if isinstance(martial_skills, dict):
            skills_iter = martial_skills.values()
        else:
            skills_iter = martial_skills
        for skill in skills_iter:
            if skill.get('equipped') and (skill.get('type') == '内功' or skill.get('类型') == '内功') and skill.get('name') != '基本内功':
                equipped_neigong = skill
                break
        if not equipped_neigong:
            return {'type': 'error', 'data': {'message': '你未装备任何内功心法，无法疗伤'}}
        if player['hp'] >= player['max_hp']:
            return {'type': 'error', 'data': {'message': '你气血已满，无需疗伤'}}
        if player['mp'] < 10:
            return {'type': 'error', 'data': {'message': '内力不足，无法疗伤'}}
        # 进入疗伤状态
        player['status'] = 'healing'
        await self.notify_player_data_update(user_id, player)
        # MUD风格疗伤起始文本
        log_entry = {
            'type': 'healing',
            'name': '疗伤',
            'content': f"你盘膝坐下，调息凝神，运转{equipped_neigong['name']}，开始疗伤。",
            'timestamp': datetime.now().isoformat()
        }
        player['event_log'].insert(0, log_entry)
        if len(player['event_log']) > 50:
            player['event_log'] = player['event_log'][:50]
        await self.save_player_data(user_id, player)
        await self.notify_player_data_update(user_id, player)
        await websocket.send(json.dumps({'type': 'healing_log', 'data': {'message': log_entry['content']}}, ensure_ascii=False))
        # 持续疗伤循环
        while player['mp'] >= 10 and player['hp'] < player['max_hp']:
            heal_amount = min(10, player['max_hp'] - player['hp'])
            player['mp'] -= 10
            player['hp'] += heal_amount
            if player['hp'] > player['max_hp']:
                player['hp'] = player['max_hp']
            # MUD风格每步文本
            step_log = {
                'type': 'healing',
                'name': '疗伤',
                'content': f"你运功调息，消耗10点内力，恢复{heal_amount}点气血。当前气血：{player['hp']}/{player['max_hp']}，内力：{player['mp']}/{player['max_mp']}",
                'timestamp': datetime.now().isoformat()
            }
            player['event_log'].insert(0, step_log)
            if len(player['event_log']) > 50:
                player['event_log'] = player['event_log'][:50]
            await self.save_player_data(user_id, player)
            await self.notify_player_data_update(user_id, player)
            await websocket.send(json.dumps({'type': 'healing_log', 'data': {'message': step_log['content']}}, ensure_ascii=False))
            await asyncio.sleep(0.5)
        # 疗伤结束
        if player['hp'] >= player['max_hp']:
            msg = '你缓缓收功，气血已恢复至巅峰，疗伤结束。'
        else:
            msg = '你内力耗尽，只得暂时收功，疗伤结束。'
        end_log = {
            'type': 'healing',
            'name': '疗伤',
            'content': msg,
            'timestamp': datetime.now().isoformat()
        }
        player['status'] = 'normal'
        player['event_log'].insert(0, end_log)
        if len(player['event_log']) > 50:
            player['event_log'] = player['event_log'][:50]
        await self.save_player_data(user_id, player)
        await self.notify_player_data_update(user_id, player)
        await websocket.send(json.dumps({'type': 'healing_log', 'data': {'message': msg}}, ensure_ascii=False))
        return {'type': 'success', 'data': {'message': msg}}
    
    async def handle_equip_item(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        unique_id = data.get('unique_id')
        slot_type = data.get('slot_type')
        if not unique_id or not slot_type:
            return {'type': 'error', 'data': {'message': '参数错误'}}
        result = await inv_handle_equip_item(
            player, unique_id, slot_type, user_id=user_id,
            save_func=self.save_player_data, update_bonuses_func=self.update_player_bonuses_and_save
        )
        # 推送最新数据
        if result['type'] == 'equip_success':
            try:
                await websocket.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
                await self.notify_player_data_update(user_id, player)
            except Exception as e:
                logger.error(f"[EQUIP] 推送player_data失败: {e}")
        return result
    
    async def handle_unequip_item(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        slot_type = data.get('slot_type')
        if slot_type is None:
            return {'type': 'error', 'data': {'message': '参数错误'}}
        result = await inv_handle_unequip_item(
            player, slot_type, user_id=user_id,
            save_func=self.save_player_data, update_bonuses_func=self.update_player_bonuses_and_save
        )
        # 推送最新数据
        if result['type'] == 'unequip_success':
            await self.notify_player_data_update(user_id, player)
        return result
    
    async def handle_expand_inventory(self, websocket: WebSocketServerProtocol) -> dict:
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        result = await inv_handle_expand_inventory(player, user_id=user_id, save_func=self.save_player_data)
        return result
    
    def can_equip_item(self, item: dict, slot_type: str) -> bool:
        return inv_can_equip_item(item, slot_type)
    

    

    
    async def update_player_bonuses_and_save(self, user_id: str, player: dict) -> dict:
        """
        更新玩家增益并持久化保存
        
        Args:
            user_id: 用户ID
            player: 玩家数据
            
        Returns:
            更新后的玩家数据
        """
        # 应用增益
        updated_player = self.bonus_system.apply_bonuses_to_player(player)
        
        # 更新内存中的数据
        self.player_data[str(user_id)] = updated_player
        
        # 持久化保存
        await self.save_player_data(user_id, updated_player)
        
        logger.info(f"玩家 {user_id} 增益更新完成并已保存")
        return updated_player
    
    def get_bonus_summary(self, player: dict) -> dict:
        """
        获取玩家增益摘要
        
        Args:
            player: 玩家数据
            
        Returns:
            增益摘要
        """
        return self.bonus_system.get_bonus_summary(player)
    
    def get_client_id(self, websocket: WebSocketServerProtocol) -> Optional[str]:
        """获取客户端ID，重构为直接返回 websocket.user_id"""
        return getattr(websocket, 'user_id', None)
    
    # 其他处理方法（简化实现）
    async def handle_equipment_action(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        logger.info(f"[handle_equipment_action] data={data}, websocket.user_id={getattr(websocket, 'user_id', None)}")
        return await self.crafting_system.handle_crafting_action(data, websocket)
    
    async def handle_skill_action(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        return {'type': 'success', 'data': {'message': '武功操作成功'}}
    
    async def handle_shop_action(self, data, websocket: WebSocketServerProtocol) -> dict:
        import logging
        logger = logging.getLogger("shop_action")
        action = data.get('action', 'list')
        npc_id = data.get('npc_id')
        logger.info(f"[商店] action={action}, npc_id={npc_id}, data={data}")
        if not npc_id:
            logger.error("[商店] 缺少NPC参数")
            return {'type': 'error', 'data': {'message': '缺少NPC参数'}}
        if action == 'buy':
            # 购买逻辑
            item_id = data.get('item_id')
            quantity = int(data.get('quantity', 1))
            if not item_id or quantity < 1:
                return {'type': 'error', 'data': {'message': '参数错误'}}
            # 获取商店可售物品
            shop_items = shop_system.get_shop_items(npc_id)
            shop_item = next((item for item in shop_items if item['id'] == item_id), None)
            if not shop_item:
                return {'type': 'error', 'data': {'message': '该物品不在商店售卖'}}
            price = shop_item.get('price', 0) * quantity
            # 获取玩家
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                return {'type': 'error', 'data': {'message': '未认证'}}
            player = self.player_data.get(str(user_id))
            if not player:
                return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
            # 检查银两
            if player.get('money', 0) < price:
                return {'type': 'error', 'data': {'message': '银两不足'}}
            # 检查背包空间
            if not self.can_add_item_to_inventory(player, {'id': item_id, 'quantity': quantity}):
                return {'type': 'error', 'data': {'message': '背包已满，无法获得新物品'}}
            # 扣钱
            player['money'] -= price
            # 添加物品（统一走 add_item_to_inventory，自动处理堆叠/保存）
            success = await self.add_item_to_inventory(user_id, player, {'id': item_id, 'quantity': quantity})
            if not success:
                return {'type': 'error', 'data': {'message': '背包已满，无法获得新物品'}}
            # 推送最新数据
            await websocket.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
            await websocket.send(json.dumps({'type': 'inventory_data', 'data': {'inventory': player['inventory'], 'capacity': capacity}}, ensure_ascii=False))
            # 持久化存储
            user_id = player.get('id')
            if not player.get('id'):
                player['id'] = str(user_id)
            if user_id:
                await self.save_player_data(user_id, player)
            logger.info(f"[商店] 玩家{user_id} 购买商品: npc_id={npc_id}, item_id={item_id}, quantity={quantity}, shop_item={shop_item}")
            return {'type': 'success', 'data': {'message': f'成功购买{shop_item["name"]} x{quantity}'}}
        elif action == 'sell':
            # 售出逻辑
            item_id = data.get('item_id')
            quantity = int(data.get('quantity', 1))
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                return {'type': 'error', 'data': {'message': '未认证'}}
            player = self.player_data.get(str(user_id))
            if not player:
                return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
            result = shop_system.sell_item(player, npc_id, item_id, quantity)
            # 推送最新数据
            capacity = player.get('inventory_capacity', 50)
            await websocket.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
            await websocket.send(json.dumps({'type': 'inventory_data', 'data': {'inventory': player['inventory'], 'capacity': capacity}}, ensure_ascii=False))
            # 持久化存储
            user_id = player.get('id')
            if not player.get('id'):
                player['id'] = str(user_id)
            if user_id:
                await self.save_player_data(user_id, player)
            return result
        else:
            # 查询商店物品列表
            items = shop_system.get_shop_items(npc_id)
            logger.info(f"[商店] npc_id={npc_id} 返回商品数: {len(items)}")
            return {'type': 'shop_items', 'data': {'npc_id': npc_id, 'items': items}}
    
    async def handle_market_action(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        return await self.market_system.handle_market_action(data, websocket)
    
    async def handle_guild_action(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        return {'type': 'success', 'data': {'message': '门派操作成功'}}
    
    async def handle_crafting_action(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        logger.info(f"[handle_crafting_action] data={data}, websocket.user_id={getattr(websocket, 'user_id', None)}")
        return await self.crafting_system.handle_crafting_action(data, websocket)
    
    async def handle_craft_item(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理物品合成请求"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法合成物品")
            return {'type': 'craft_failed', 'data': {'message': '未认证'}}
        
        player = self.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[{client_address}] 玩家数据不存在: {user_id}")
            return {'type': 'craft_failed', 'data': {'message': '玩家数据不存在'}}
        
        target_item_id = data.get('item_id')
        if not target_item_id:
            return {'type': 'craft_failed', 'data': {'message': '请指定要合成的物品'}}
        
        # 获取目标物品信息
        target_item = item_system.get_item(target_item_id)
        if not target_item:
            return {'type': 'craft_failed', 'data': {'message': '物品不存在'}}
        
        if not target_item.craftable:
            return {'type': 'craft_failed', 'data': {'message': '该物品无法合成'}}
        
        # 解析合成配方
        craft_recipe = target_item.craft_recipe
        if not craft_recipe:
            return {'type': 'craft_failed', 'data': {'message': '该物品没有合成配方'}}
        
        # 解析配方格式：材料名:数量,材料名:数量
        try:
            materials_needed = {}
            recipe_parts = craft_recipe.split(',')
            for part in recipe_parts:
                if ':' in part:
                    material_name, quantity = part.split(':', 1)
                    materials_needed[material_name.strip()] = int(quantity.strip())
        except Exception as e:
            logger.error(f"解析合成配方失败: {craft_recipe}, 错误: {e}")
            return {'type': 'craft_failed', 'data': {'message': '合成配方格式错误'}}
        
        # 检查材料是否足够
        inventory = player.get('inventory', [])
        materials_available = {}
        
        # 统计背包中的材料数量
        for item in inventory:
            item_name = item.get('name', '')
            if item_name in materials_needed:
                materials_available[item_name] = materials_available.get(item_name, 0) + item.get('quantity', 1)
        
        # 检查是否所有材料都足够
        missing_materials = []
        for material_name, needed_quantity in materials_needed.items():
            available_quantity = materials_available.get(material_name, 0)
            if available_quantity < needed_quantity:
                missing_materials.append(f"{material_name}(需要{needed_quantity}，现有{available_quantity})")
        
        if missing_materials:
            return {
                'type': 'craft_failed', 
                'data': {'message': f'材料不足: {", ".join(missing_materials)}'}
            }
        
        # 检查体力是否足够（合成消耗10点体力）
        if player.get('energy', 0) < 10:
            return {'type': 'craft_failed', 'data': {'message': '体力不足，需要10点体力'}}
        
        # 开始合成
        try:
            # 消耗材料
            for material_name, needed_quantity in materials_needed.items():
                remaining_quantity = needed_quantity
                
                # 从背包中移除材料
                for i in range(len(inventory) - 1, -1, -1):  # 从后往前遍历，避免索引问题
                    item = inventory[i]
                    if item.get('name', '') == material_name:
                        item_quantity = item.get('quantity', 1)
                        if item_quantity <= remaining_quantity:
                            # 完全消耗这个物品
                            remaining_quantity -= item_quantity
                            inventory.pop(i)
                        else:
                            # 部分消耗
                            item['quantity'] = item_quantity - remaining_quantity
                            remaining_quantity = 0
                        
                        if remaining_quantity <= 0:
                            break
            
            # 消耗体力
            player['energy'] -= 10
            
            # 生成合成物品
            crafted_item = {
                'id': target_item_id,
                'name': target_item.name,
                'type': target_item.type.value,
                'quality': target_item.quality.value,
                'description': target_item.description,
                'icon': target_item.icon,
                'price': target_item.price,
                'sell_price': target_item.sell_price,
                'quantity': 1,
                'unique_id': str(uuid.uuid4())
            }
            
            # 添加物品到背包
            success = await self.add_item_to_inventory(user_id, player, crafted_item)
            
            if success:
                # 保存玩家数据
                await self.save_player_data(str(user_id), player)
                
                logger.info(f"玩家 {user_id} 成功合成物品: {target_item.name}")
                
                return {
                    'type': 'craft_success',
                    'data': {
                        'message': f'成功合成 {target_item.name}',
                        'crafted_item': crafted_item,
                        'inventory': player.get('inventory', []),
                        'energy': player.get('energy', 0)
                    }
                }
            else:
                return {'type': 'craft_failed', 'data': {'message': '背包已满，无法获得合成物品'}}
                
        except Exception as e:
            logger.error(f"合成物品失败: {e}")
            return {'type': 'craft_failed', 'data': {'message': f'合成失败: {str(e)}'}}
    
    async def handle_get_craftable_items(self, websocket: WebSocketServerProtocol) -> dict:
        logger.info(f"[handle_get_craftable_items] user_id={getattr(websocket, 'user_id', None)}")
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法获取可合成物品")
            return {'type': 'get_craftable_failed', 'data': {'message': '未认证'}}
        
        player = self.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[{client_address}] 玩家数据不存在: {user_id}")
            return {'type': 'get_craftable_failed', 'data': {'message': '玩家数据不存在'}}
        
        try:
            # 获取所有可合成的物品
            craftable_items = []
            inventory = player.get('inventory', [])
            
            # 统计背包中的材料
            materials_available = {}
            for item in inventory:
                item_name = item.get('name', '')
                materials_available[item_name] = materials_available.get(item_name, 0) + item.get('quantity', 1)
            
            # 遍历所有物品，找到可合成的
            for item_id, item_obj in item_system.items.items():
                if item_obj.craftable and item_obj.craft_recipe:
                    # 解析配方
                    try:
                        materials_needed = {}
                        recipe_parts = item_obj.craft_recipe.split(',')
                        for part in recipe_parts:
                            if ':' in part:
                                material_name, quantity = part.split(':', 1)
                                materials_needed[material_name.strip()] = int(quantity.strip())
                        
                        # 检查材料是否足够
                        can_craft = True
                        missing_materials = []
                        for material_name, needed_quantity in materials_needed.items():
                            available_quantity = materials_available.get(material_name, 0)
                            if available_quantity < needed_quantity:
                                can_craft = False
                                missing_materials.append(f"{material_name}(需要{needed_quantity}，现有{available_quantity})")
                        
                        craftable_items.append({
                            'id': item_id,
                            'name': item_obj.name,
                            'type': item_obj.type.value,
                            'quality': item_obj.quality.value,
                            'description': item_obj.description,
                            'icon': item_obj.icon,
                            'craft_recipe': item_obj.craft_recipe,
                            'can_craft': can_craft,
                            'missing_materials': missing_materials if not can_craft else []
                        })
                        
                    except Exception as e:
                        logger.error(f"解析物品 {item_id} 的合成配方失败: {e}")
                        continue
            
            return {
                'type': 'get_craftable_success',
                'data': {
                    'craftable_items': craftable_items,
                    'energy': player.get('energy', 0)
                }
            }
            
        except Exception as e:
            logger.error(f"获取可合成物品失败: {e}")
            return {'type': 'get_craftable_failed', 'data': {'message': f'获取失败: {str(e)}'}}

    async def handle_select_map(self, data, websocket):
        """处理地图切换请求"""
        map_id = data.get('map_id')
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        # 查找地图
        maps = self.game_data['maps']
        target_map = next((m for m in maps.values() if m['id'] == map_id), None)
        if not target_map:
            return {'type': 'error', 'data': {'message': '地图不存在'}}
        # 校验进入要求
        req = target_map.get('enter_requirements', {})
        # 使用攻击力作为进入条件（替代等级）
        if 'attack' in req and player.get('attack', 10) < req.get('attack', 0):
            return {'type': 'error', 'data': {'message': f"攻击力不足，需{req.get('attack', 0)}点攻击力"}}
        if 'item' in req and req['item']:
            if not any(i['name'] == req['item'] for i in player['inventory']):
                return {'type': 'error', 'data': {'message': f"缺少进入道具：{req['item']}"}}
        # 记录原地图ID
        old_map_id = player.get('current_map')

        # 切换地图并持久化
        player['current_map'] = map_id
        await self.save_player_data(user_id, player)

        # 广播地图玩家更新
        if old_map_id and old_map_id != map_id:
            # 更新原地图的玩家列表
            await self.broadcast_map_players_update(old_map_id)

        # 更新新地图的玩家列表
        await self.broadcast_map_players_update(map_id)

        return {'type': 'select_map_success', 'data': {'map_id': map_id, 'map_name': target_map.get('名称', '未知')}}

    async def handle_get_map_list(self, websocket: WebSocketServerProtocol):
        return {'type': 'map_list', 'data': load_maps_config()}

    # ==================== 数据库持久化方法 ====================
    
    async def save_player_data(self, user_id: str, player_data: dict):
        """保存玩家数据到数据库"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "UPDATE players SET data = ? WHERE user_id = ?",
                    (json.dumps(player_data, ensure_ascii=False), user_id)
                )
                await db.commit()

        except Exception as e:

            raise e

    async def load_player_data(self, user_id: str) -> Optional[dict]:
        """从数据库加载玩家数据"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute("SELECT data FROM players WHERE user_id = ?", (user_id,)) as cursor:
                    row = await cursor.fetchone()
                    if row:
                        player_data = json.loads(row[0])
                        player_name = player_data.get('name', player_data.get('character_name', '未知'))
                        logger.info(f"从数据库加载玩家数据: {player_name} (ID: {user_id})")
                        return player_data
                    else:
                        logger.warning(f"数据库中没有找到玩家数据: {user_id}")
                        return None
        except Exception as e:
            logger.error(f"加载玩家数据失败: {e}")
            return None

    async def save_user_token(self, user_id: str, token: str):
        """更新用户token"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "UPDATE users SET token = ? WHERE id = ?",
                    (token, user_id)
                )
                await db.commit()
                logger.info(f"用户token已更新: {user_id}")
        except Exception as e:
            logger.error(f"更新用户token失败: {e}")
            raise e

    async def get_user_by_token(self, token: str) -> Optional[tuple]:
        """根据token获取用户信息"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute(
                    "SELECT id, username, character_name, gender FROM users WHERE token = ?",
                    (token,)
                ) as cursor:
                    row = await cursor.fetchone()
                    if row:
                        logger.info(f"根据token找到用户: {row[1]} (ID: {row[0]})")
                        return row
                    else:
                        logger.warning(f"根据token未找到用户: {token}")
                        return None
        except Exception as e:
            logger.error(f"根据token查询用户失败: {e}")
            return None

    async def update_player_last_login(self, user_id: str):
        """更新玩家最后登录时间"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "UPDATE players SET last_login = ? WHERE user_id = ?",
                    (datetime.now().isoformat(), user_id)
                )
                await db.commit()
                logger.info(f"更新玩家最后登录时间: {user_id}")
        except Exception as e:
            logger.error(f"更新玩家最后登录时间失败: {e}")



    async def handle_realm_breakthrough(self, websocket: WebSocketServerProtocol) -> dict:
        """处理境界突破请求"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法突破境界")
            return {'type': 'breakthrough_failed', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[{client_address}] 玩家数据不存在: {user_id}")
            return {'type': 'breakthrough_failed', 'data': {'message': '玩家数据不存在'}}
        
        # 获取当前境界信息
        realm_info = player.get('realm_info', {})
        progress = realm_info.get('progress', 0)
        next_min = realm_info.get('next_min', 0)
        current_max = realm_info.get('current_max', 0)
        
        # 判断是否可以突破
        if progress < next_min:
            return {'type': 'breakthrough_failed', 'data': {'message': '历练值不足，无法突破境界'}}
        
        # 计算需要的突破丹数量
        current_realm_level = realm_info.get('current_level', 0)
        required_pills = 10 + current_realm_level * 2  # 基础10颗，每级递增2颗
        
        # 检查突破丹数量
        breakthrough_pills = 0
        pill_index = -1
        for i, item in enumerate(player['inventory']):
            if item.get('id') == 'breakthrough_pill':
                breakthrough_pills = item.get('quantity', 0)
                pill_index = i
                break
        
        if breakthrough_pills < required_pills:
            return {'type': 'breakthrough_failed', 'data': {'message': f'突破丹不足，需要{required_pills}颗，当前只有{breakthrough_pills}颗'}}
        
        # 消耗突破丹
        if breakthrough_pills == required_pills:
            player['inventory'].pop(pill_index)
        else:
            player['inventory'][pill_index]['quantity'] -= required_pills
        
        old_realm = realm_info.get('current_realm', '未知')
        
        # 境界突破，提升境界
        player['experience'] = progress
        player['realm_info'] = self.get_realm_info(player['experience'])
        new_realm = player['realm_info'].get('current_realm', '未知')
        
        # 使用统一的增益系统应用境界增益，避免重复应用
        player = await self.update_player_bonuses_and_save(str(user_id), player)
        
        # 境界突破奖励武学点
        base_skill_points_reward = 5
        skill_points_gain_bonus = player.get('skill_points_gain_bonus', 0)
        skill_points_reward = base_skill_points_reward + skill_points_gain_bonus
        player['skill_points'] = player.get('skill_points', 0) + skill_points_reward
        
        # 保存玩家数据
        self.player_data[str(user_id)] = player
        
        # 推送成功消息
        return {
            'type': 'breakthrough_success',
            'data': {
                'old_realm': old_realm,
                'new_realm': new_realm,
                'message': f'恭喜！你突破到了{new_realm}境界，消耗{required_pills}颗突破丹，获得{skill_points_reward}武学点！'
            }
        }

    async def handle_use_item(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理使用物品"""
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        unique_id = data.get('unique_id')
        if not unique_id:
            return {'type': 'error', 'data': {'message': '参数错误'}}
        item_index = next((i for i, item in enumerate(player['inventory']) if item.get('unique_id') == unique_id), -1)
        if item_index == -1:
            return {'type': 'error', 'data': {'message': '物品不存在'}}
        item = player['inventory'][item_index]
        item_obj = item_system.get_item(item.get('id', ''))
        
        if not item_obj:
            return {'type': 'use_item_failed', 'data': {'message': '物品数据错误'}}
        
        # 检查物品是否可以使用
        if not item_system.can_use(item_obj):
            return {'type': 'use_item_failed', 'data': {'message': '此物品无法使用'}}
        
        # 处理物品效果
        message = f'使用了 {item["name"]}'
        effects_applied = []

        # 检查是否是门派令牌
        sect_id = None
        logger.error(f"检查门派令牌: item={item}, item_obj={item_obj}")
        logger.error(f"item_obj类型: {type(item_obj)}, item_obj.__dict__={getattr(item_obj, '__dict__', {})}")

        # 检查多种可能的sect_token位置
        if hasattr(item_obj, 'sect_token'):
            sect_id = getattr(item_obj, 'sect_token')
            logger.error(f"找到sect_token属性: {sect_id}")
        elif hasattr(item_obj, 'data') and isinstance(item_obj.data, dict) and 'sect_token' in item_obj.data:
            sect_id = item_obj.data['sect_token']
            logger.error(f"找到data.sect_token: {sect_id}")
        elif hasattr(item_obj, '__dict__') and 'data' in item_obj.__dict__ and isinstance(item_obj.__dict__['data'], dict):
            sect_id = item_obj.__dict__['data'].get('sect_token')
            logger.error(f"找到__dict__.data.sect_token: {sect_id}")
        elif hasattr(item_obj, '__dict__') and 'sect_token' in item_obj.__dict__:
            sect_id = item_obj.__dict__.get('sect_token')
            logger.error(f"找到根级别sect_token: {sect_id}")

        if not sect_id:
            logger.error(f"未找到sect_token，尝试所有可能的属性:")
            for attr in dir(item_obj):
                if not attr.startswith('_'):
                    value = getattr(item_obj, attr, None)
                    logger.error(f"  {attr}: {value}")
                    if attr == 'sect_token':
                        sect_id = value
                        logger.error(f"通过dir()找到sect_token: {sect_id}")
                        break

        logger.error(f"最终识别的sect_id: {sect_id}")

        if sect_id:
                # 处理门派令牌使用
                result = await self.handle_sect_token_use(user_id, player, sect_id, item['name'])
                if result['success']:
                    # 扣除物品数量
                    if item['quantity'] > 1:
                        item['quantity'] -= 1
                    else:
                        player['inventory'].pop(item_index)

                    # 保存数据
                    self.player_data[str(user_id)] = player
                    await self.save_player_data(str(user_id), player)  # 确保保存到数据库
                    await self.notify_player_data_update(user_id, player)
                    logger.info(f"门派令牌使用成功，已保存玩家数据")

                    return {
                        'type': 'use_item_success',
                        'data': {
                            'message': result['message'],
                            'inventory': player['inventory']
                        }
                    }
                else:
                    return {'type': 'use_item_failed', 'data': {'message': result['message']}}

        for effect in item_obj.effects:
            if effect.type == 'heal':
                old_hp = player['hp']
                player['hp'] = min(player['hp'] + effect.value, player['max_hp'])
                healed = player['hp'] - old_hp
                effects_applied.append(f'恢复{healed}点生命值')
            elif effect.type == 'mana':
                old_mp = player['mp']
                player['mp'] = min(player['mp'] + effect.value, player['max_mp'])
                restored = player['mp'] - old_mp
                effects_applied.append(f'恢复{restored}点内力')
            elif effect.type == 'energy':
                old_energy = player['energy']
                player['energy'] = min(player['energy'] + effect.value, player['max_energy'])
                restored = player['energy'] - old_energy
                effects_applied.append(f'恢复{restored}点精力')
            elif effect.type == 'breakthrough':
                effects_applied.append('突破丹需要在境界突破时使用')
                return {'type': 'use_item_failed', 'data': {'message': '突破丹需要在境界突破时使用'}}
        
        if effects_applied:
            message = f'使用了 {item["name"]}，{", ".join(effects_applied)}'
        
        # 扣除物品数量
        if item['quantity'] > 1:
            item['quantity'] -= 1
        else:
            player['inventory'].pop(item_index)
        
        # 保存数据
        self.player_data[str(user_id)] = player
        
        await self.notify_player_data_update(user_id, player)
        
        return {
            'type': 'use_item_success',
            'data': {
                'message': message,
                'inventory': player['inventory']
            }
        }

    async def handle_sect_token_use(self, user_id: str, player: dict, sect_id: str, token_name: str) -> dict:
        """处理门派令牌使用"""
        try:
            # 检查玩家是否已经加入门派
            logger.error(f"检查门派系统: hasattr(self, 'sect_system')={hasattr(self, 'sect_system')}")
            if hasattr(self, 'sect_system'):
                # 检查是否已加入门派
                async with aiosqlite.connect(self.db_path) as db:
                    async with db.execute('''
                        SELECT sect_id FROM sect_members WHERE user_id = ?
                    ''', (user_id,)) as cursor:
                        existing_sect = await cursor.fetchone()
                        logger.error(f"玩家 {user_id} 现有门派: {existing_sect}")

                if existing_sect:
                    return {
                        'success': False,
                        'message': '你已经加入了门派，无法使用门派令牌'
                    }

                # 获取门派配置
                logger.error(f"门派系统配置: {self.sect_system.sects_config}")
                sect_config = self.sect_system.sects_config['sects'].get(sect_id)
                logger.error(f"门派 {sect_id} 的配置: {sect_config}")
                if not sect_config:
                    logger.error(f"门派 {sect_id} 不存在，可用门派: {list(self.sect_system.sects_config['sects'].keys())}")
                    return {
                        'success': False,
                        'message': '门派不存在'
                    }

                # 加入门派
                logger.error(f"准备加入门派: user_id={user_id}, sect_id={sect_id}")
                await self.sect_system.init_sect_tables()
                async with aiosqlite.connect(self.db_path) as db:
                    try:
                        await db.execute('''
                            INSERT INTO sect_members (user_id, sect_id, rank, contribution, join_time)
                            VALUES (?, ?, 1, 0, CURRENT_TIMESTAMP)
                        ''', (user_id, sect_id))
                        await db.commit()
                        logger.error(f"成功插入门派数据: user_id={user_id}, sect_id={sect_id}")
                    except Exception as e:
                        logger.error(f"插入门派数据失败: {e}")
                        return {
                            'success': False,
                            'message': f'加入门派失败: {str(e)}'
                        }

                # 发送全服公告
                player_name = player.get('name') or player.get('character_name') or '神秘玩家'
                sect_name = sect_config.get('name', '未知门派')
                announcement = f'🎉 恭喜 {player_name} 幸运的加入了 {sect_name}！'

                # 广播公告
                logger.error(f"准备发送全服公告: {announcement}")
                await self.broadcast_announcement(announcement)
                logger.error(f"全服公告已发送")

                # 记录日志
                logger.error(f"玩家 {player_name} 使用 {token_name} 成功加入门派 {sect_name}")

                return {
                    'success': True,
                    'message': f'成功加入{sect_name}！欢迎来到{sect_name}大家庭！'
                }
            else:
                return {
                    'success': False,
                    'message': '门派系统未初始化'
                }

        except Exception as e:
            logger.error(f"处理门派令牌使用失败: {e}")
            return {
                'success': False,
                'message': f'加入门派失败: {str(e)}'
            }

    async def broadcast_announcement(self, announcement: str):
        """广播全服公告"""
        try:
            # 构建公告消息
            announcement_data = {
                'type': 'system_announcement',
                'data': {
                    'message': announcement,
                    'timestamp': datetime.now().isoformat()
                }
            }

            # 发送给所有连接的客户端
            disconnected_clients = []
            for client_id, client_ws in self.clients.items():
                try:
                    await client_ws.send(json.dumps(announcement_data, ensure_ascii=False))
                except Exception as e:
                    logger.warning(f"向客户端 {client_id} 发送公告失败: {e}")
                    disconnected_clients.append(client_id)

            # 清理断开的连接
            for client_id in disconnected_clients:
                if client_id in self.clients:
                    del self.clients[client_id]

            logger.info(f"全服公告已发送: {announcement}")

        except Exception as e:
            logger.error(f"广播全服公告失败: {e}")

    async def handle_destroy_item(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        unique_id = data.get('unique_id')
        if not unique_id:
            return {'type': 'error', 'data': {'message': '参数错误'}}
        result = await inv_handle_destroy_item(player, unique_id, user_id=user_id, save_func=self.save_player_data)
        return result

    async def handle_study_martial(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理读书学武请求"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法读书学武")
            return {'type': 'study_martial_failed', 'data': {'message': '未认证'}}
        
        player = self.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[{client_address}] 玩家数据不存在: {user_id}")
            return {'type': 'study_martial_failed', 'data': {'message': '玩家数据不存在'}}
        
        skill_name = data.get('skill_name')
        if not skill_name:
            return {'type': 'study_martial_failed', 'data': {'message': '请指定要学习的武学'}}
        
        martial_skills = player.get('martial_skills', {})
        if skill_name not in martial_skills:
            return {'type': 'study_martial_failed', 'data': {'message': '武学不存在'}}
        
        skill_data = martial_skills[skill_name]
        if not skill_data.get('unlocked', False):
            return {'type': 'study_martial_failed', 'data': {'message': '该武学尚未解锁，需要找老师学习'}}
        
        # 检查体力是否足够
        if player['energy'] < 10:
            return {'type': 'study_martial_failed', 'data': {'message': '体力不足，需要10点体力'}}
        
        # 消耗体力
        player['energy'] -= 10
        
        # 计算经验值增长
        current_level = skill_data.get('level', 0)
        current_exp = skill_data.get('exp', 0)
        
        # 使用增强武功系统计算经验值增长
        martial_info = load_wugong_config.get_martial_info(skill_name)
        if martial_info:
            # 使用增强武功系统的系数
            coefficient = martial_info.get('coefficient', 50)
            base_exp_gain = 10 + current_level * 2
        else:
            # 兼容旧系统
            martial_type = self.get_martial_type(skill_name)
            coefficient = 1.0  # 默认系数
            base_exp_gain = 10 + current_level * 2
        
        # 根据境界加成经验值
        realm_bonus = player.get('realm_info', {}).get('exp_bonus', 0)
        
        # 应用悟性增益到经验值获取
        intelligence = player.get('talent', {}).get('悟性', 15)
        intelligence_bonus = max(0, intelligence - 15) * 0.01  # 每点悟性+1%经验获取
        intelligence_multiplier = 1.0 + intelligence_bonus
        
        # 计算最终经验值获取
        exp_gain = int((base_exp_gain + realm_bonus) * intelligence_multiplier)
        
        # 更新经验值
        new_exp = current_exp + exp_gain
        skill_data['exp'] = new_exp
        
        # 检查是否升级（使用二次方公式）
        exp_needed = coefficient * (current_level + 1) ** 2
        level_up = False
        new_level = current_level
        
        if new_exp >= exp_needed:
            new_level = current_level + 1
            skill_data['level'] = new_level
            skill_data['exp'] = new_exp - exp_needed
            level_up = True

            # ====== 天赋加点逻辑 ======
            # 拳脚类武学
            fist_skills = ['基本拳法', '基本掌法', '基本腿法', '基本爪法', '基本指法', '基本手法', '基本鞭法', '基本锤法']
            # 轻功
            agility_skills = ['基本轻功']
            # 内功
            constitution_skills = ['基本内功']
            # 读书写字
            wisdom_skills = ['读书写字']
            # 只在基础武学生效（生活技能不参与天赋加点）
            if skill_name in fist_skills + agility_skills + constitution_skills + wisdom_skills:
                # 计算本次升级是否跨越10的倍数
                old_ten = current_level // 10
                new_ten = new_level // 10
                if new_ten > old_ten:
                    if 'talent' not in player:
                        player['talent'] = self.generate_talent_attributes()
                    if skill_name in fist_skills:
                        player['talent']['力量'] += 1
                    elif skill_name in agility_skills:
                        player['talent']['身法'] += 1
                    elif skill_name in constitution_skills:
                        player['talent']['根骨'] += 1
                    elif skill_name in wisdom_skills:
                        player['talent']['悟性'] += 1
            # ====== 天赋加点逻辑结束 ======
            
            # ====== 属性加成逻辑 ======
            # 定义武学类型的基础增益常数G（生活技能不参与属性加成）
            skill_gain_constants = {
                # 拳脚类武学 - 增加攻击
                '基本拳法': 3, '基本掌法': 3, '基本腿法': 3, '基本爪法': 3, 
                '基本指法': 3, '基本手法': 3, '基本鞭法': 3, '基本锤法': 3,
                # 轻功和招架 - 增加防御
                '基本轻功': 2, '基本招架': 2,
                # 内功 - 增加气血
                '基本内功': 5
            }
            
            # 计算属性增量（生活技能不参与）
            if skill_name in skill_gain_constants:
                G = skill_gain_constants[skill_name]
                # 使用平方根公式：ΔA = G × (√L - √(L-1))
                import math
                delta_attr = G * (math.sqrt(new_level) - math.sqrt(new_level - 1))
                
                # 根据武学类型增加对应属性
                if skill_name in ['基本拳法', '基本掌法', '基本腿法', '基本爪法', '基本指法', '基本手法', '基本鞭法', '基本锤法']:
                    # 拳脚类增加攻击
                    if 'attack' not in player:
                        player['attack'] = 0
                    player['attack'] += delta_attr
                elif skill_name in ['基本轻功', '基本招架']:
                    # 轻功和招架增加防御
                    if 'defense' not in player:
                        player['defense'] = 0
                    player['defense'] += delta_attr
                elif skill_name == '基本内功':
                    # 内功增加气血
                    if 'hp' not in player:
                        player['hp'] = 100
                    if 'max_hp' not in player:
                        player['max_hp'] = 100
                    player['hp'] += delta_attr
                    player['max_hp'] += delta_attr
            # ====== 属性加成逻辑结束 ======
            
            # 检查是否可以解锁对应的入门武学
            unlocked_skill = self.martial_handler.check_martial_unlock(skill_name, new_level, martial_skills)
            if unlocked_skill:
                logger.info(f"玩家 {user_id} 解锁了新武学: {unlocked_skill}")
        
        # 保存数据
        self.player_data[str(user_id)] = player
        
        # 返回结果
        base_exp = base_exp_gain + realm_bonus
        intelligence_bonus_exp = exp_gain - base_exp
        
        message = f'学习了{skill_name}，获得{exp_gain}经验值'
        if intelligence_bonus_exp > 0:
            message += f'(悟性加成+{intelligence_bonus_exp})'
        if level_up:
            message += f'，{skill_name}提升到{new_level}级！'
            unlocked_skill = self.martial_handler.check_martial_unlock(skill_name, new_level, martial_skills)
            if unlocked_skill:
                message += f'解锁了{unlocked_skill}！'
        
        # 获取当前武学的实际经验值
        current_skill_exp = skill_data.get('exp', 0)
        
        return {
            'type': 'study_martial_success',
            'data': {
                'message': message,
                'skill_name': skill_name,
                'exp_gain': exp_gain,
                'base_exp': base_exp,
                'intelligence_bonus_exp': intelligence_bonus_exp,
                'intelligence_bonus_percentage': round(intelligence_bonus * 100, 1),
                'level_up': level_up,
                'new_level': new_level,
                'martial_skills': martial_skills,
                'energy': player.get('energy', 0),
                'max_energy': player.get('max_energy', 0),
                'new_exp': current_skill_exp
            }
        }

    async def handle_get_martial_data(self, websocket: WebSocketServerProtocol) -> dict:
        """处理获取武学数据请求"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法获取武学数据")
            return {'type': 'get_martial_data_failed', 'data': {'message': '未认证'}}
        
        player = self.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[{client_address}] 玩家数据不存在: {user_id}")
            return {'type': 'get_martial_data_failed', 'data': {'message': '玩家数据不存在'}}
        
        martial_skills = player.get('martial_skills', [])
        # 兼容 martial_skills 为 list 的情况，临时转为 dict 用于处理，但不保存
        if isinstance(martial_skills, list):
            martial_skills_dict = {entry['name']: entry for entry in martial_skills if 'name' in entry}
        else:
            # 如果是dict，转换为list格式并保存
            skills_list = []
            for name, info in martial_skills.items():
                skill = {'name': name, '名称': name}
                skill.update(info)
                skills_list.append(skill)
            player['martial_skills'] = skills_list
            await self.save_player_data(str(user_id), player)
            martial_skills_dict = martial_skills
        
        # 按武功类型分组
        grouped_skills = {
            '剑法': [],
            '刀法': [],
            '拳法': [],
            '空手': [],
            '轻功': [],
            '内功': [],
            '暗器': [],
            '生活技能': [],
            '招架': []  # 新增，确保有招架分组
        }
        
        def normalize_name(name):
            if not name:
                return ''
            # 去除前后空格，全角转半角
            name = unicodedata.normalize('NFKC', name.strip())
            return name
        
        for skill_name, skill_data in martial_skills_dict.items():
            norm_skill_name = normalize_name(skill_name)
            martial_info = None
            # 兼容配置key的各种格式
            for k in self.game_data['wugong'].keys():
                if normalize_name(k) == norm_skill_name:
                    martial_info = self.game_data['wugong'][k]
                    break
            if martial_info:
                coefficient = get_martial_coefficient(skill_name)
                current_level = skill_data.get('level', 0)
                max_exp = coefficient * (current_level + 1) ** 2
                quality = martial_info.get('quality', '普通')
                martial_type = martial_info.get('type', '未知')
                # 新增：招式解锁状态
                moves = []
                for move in martial_info.get('招式列表', []):
                    move_name = move.get('名称')
                    unlock_level = move.get('解锁等级', 0)
                    moves.append({
                        'name': move_name,
                        'unlock_level': unlock_level,
                        'unlocked': current_level >= unlock_level
                    })
                skill_info = {
                    'name': skill_name,
                    'level': skill_data.get('level', 0),
                    'exp': skill_data.get('exp', 0),
                    'unlocked': skill_data.get('unlocked', False),
                    'max_exp': max_exp,
                    'equipped': skill_data.get('equipped', False),
                    'quality': quality,
                    'type': martial_type,
                    'moves': moves,
                    '是否可招架': martial_info.get('是否可招架', '否')
                }
                # 只将采药、伐木、挖矿、剥皮归为生活技能
                if skill_name in ['采药', '伐木', '挖矿', '剥皮']:
                    grouped_skills['生活技能'].append(skill_info)
                elif martial_type in grouped_skills and martial_type != '生活技能':
                    grouped_skills[martial_type].append(skill_info)
                # 新增：可招架武功额外加入招架分组
                if martial_info.get('是否可招架') == '是':
                    grouped_skills['招架'].append(skill_info)
            else:
                current_level = skill_data.get('level', 0)
                max_exp = 50 * (current_level + 1) ** 2
                quality = '普通'
                martial_type = '未知'
                moves = []
                skill_info = {
                    'name': skill_name,
                    'level': skill_data.get('level', 0),
                    'exp': skill_data.get('exp', 0),
                    'unlocked': skill_data.get('unlocked', False),
                    'max_exp': max_exp,
                    'equipped': skill_data.get('equipped', False),
                    'quality': quality,
                    'type': martial_type,
                    'moves': moves
                }
                if skill_name in ['采药', '伐木', '挖矿', '剥皮']:
                    grouped_skills['生活技能'].append(skill_info)
                elif martial_type in grouped_skills and martial_type != '生活技能':
                    grouped_skills[martial_type].append(skill_info)
        
        # 确保max_energy有正确的值
        max_energy = player.get('max_energy', 100)
        if max_energy == 0:
            max_energy = 100
            player['max_energy'] = max_energy
        
        # 确保所有分组都是数组，兼容对象/数组混用
        for key in grouped_skills:
            if not isinstance(grouped_skills[key], list):
                if isinstance(grouped_skills[key], dict):
                    grouped_skills[key] = list(grouped_skills[key].values())
                else:
                    grouped_skills[key] = []
        # 日志：输出每个武学的 moves 字段内容
        for key in grouped_skills:
            for skill in grouped_skills[key]:
                name = skill.get('name')
                moves = skill.get('moves')
                import logging
                logging.info(f"[get_martial_data] 武学: {name}, moves: {moves}")
        
        return {
            'type': 'get_martial_data_success',
            'data': {
                'martial_skills': grouped_skills,
                'energy': player.get('energy', 0),
                'max_energy': max_energy
            }
        }

    async def handle_use_martial(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理武学使用/装备请求"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法使用武学")
            return {'type': 'use_martial_failed', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[{client_address}] 玩家数据不存在: {user_id}")
            return {'type': 'use_martial_failed', 'data': {'message': '玩家数据不存在'}}
        skill_name = data.get('skill_name')
        if not skill_name:
            logger.error(f"装备武功失败，未收到skill_name，data={data}")
            return {'type': 'use_martial_failed', 'data': {'message': '请指定要使用的武学'}}

        # 规范化技能名称，去除空白字符并统一编码
        import unicodedata
        skill_name = unicodedata.normalize('NFKC', str(skill_name).strip())
        martial_skills = player.get('martial_skills', [])
        # 兼容 martial_skills 为 list 的情况，临时转为 dict 用于处理
        if isinstance(martial_skills, list):
            logger.info(f"martial_skills 为 list，临时转为 dict 用于处理")
            martial_skills_dict = {}
            for entry in martial_skills:
                if 'name' in entry:
                    # 规范化键名
                    normalized_name = unicodedata.normalize('NFKC', str(entry['name']).strip())
                    martial_skills_dict[normalized_name] = entry
        else:
            # 如果是dict，需要转换为list格式并保存
            logger.info(f"martial_skills 为 dict，转换为 list 格式")
            skills_list = []
            for name, info in martial_skills.items():
                skill = {'name': name, '名称': name}
                skill.update(info)
                skills_list.append(skill)
            player['martial_skills'] = skills_list
            await self.save_player_data(str(user_id), player)
            # 重新构建dict用于处理
            martial_skills_dict = {}
            for entry in skills_list:
                if 'name' in entry:
                    normalized_name = unicodedata.normalize('NFKC', str(entry['name']).strip())
                    martial_skills_dict[normalized_name] = entry
        logger.debug(f"装备武功请求 skill_name={skill_name}, martial_skills_dict keys={list(martial_skills_dict.keys())}")
        # 新增详细日志
        logger.error(f"[DEBUG] skill_name={repr(skill_name)}")
        for k in martial_skills_dict.keys():
            logger.error(f"[DEBUG] key={repr(k)}, skill_name==key? {skill_name == k}")
        if skill_name not in martial_skills_dict:
            logger.error(f"装备武功失败，skill_name={skill_name} 不在 martial_skills_dict keys={list(martial_skills_dict.keys())}")
            return {'type': 'use_martial_failed', 'data': {'message': '武学不存在'}}

        # 这里定义 skill_data 并输出日志，确保后续可用
        skill_data = martial_skills_dict[skill_name]
        logger.error(f"[DEBUG] skill_data for {skill_name}: {skill_data}")
        
        # 检查是否为生活技能或基础武学
        from enhanced_martial_system import is_life_skill, get_martial_category, can_use_martial

        is_life = is_life_skill(skill_name)
        is_basic = skill_name.startswith('基本')
        logger.error(f"武功检查: {skill_name}, is_life_skill={is_life}, startswith('基本')={is_basic}")

        if is_life or is_basic:
            logger.error(f"武功 {skill_name} 被识别为生活技能或基础武学，拒绝装备")
            return {'type': 'use_martial_failed', 'data': {'message': '生活技能和基础武学默认生效，无需装备'}}

        logger.error(f"武功 {skill_name} 通过生活技能和基础武学检查，继续装备流程")
        
        # 获取当前装备的武学
        equipped_martials = {}
        for skill, data in martial_skills_dict.items():
            if data.get('equipped', False):
                equipped_martials[skill] = True

        logger.error(f"当前已装备武学: {equipped_martials}")
        category = get_martial_category(skill_name)
        logger.error(f"要装备的武功 {skill_name} 类型: {category}")

        # 检查是否可以装备该武学
        can_equip = can_use_martial(skill_name, equipped_martials)
        logger.error(f"是否可以装备 {skill_name}: {can_equip}")
        if not can_equip:
            return {'type': 'use_martial_failed', 'data': {'message': f'该{category}已有装备的武学，请先卸下'}}

        # 修正：自动卸下同类型已装备武功
        category = get_martial_category(skill_name)
        for skill, data in martial_skills_dict.items():
            # 只卸载同类型且已装备的武学，且不是当前要装备的武学
            if get_martial_category(skill) == category and data.get('equipped', False) and skill != skill_name:
                data['equipped'] = False
                data['装备'] = False
        # 装备武学
        logger.error(f"准备装备武功: {skill_name}, 装备前状态: {skill_data}")
        skill_data['equipped'] = True
        skill_data['装备'] = True
        logger.error(f"装备后 martial_skills_dict[{skill_name}] = {martial_skills_dict[skill_name]}")

        # 同步更新原始list格式的数据
        for skill_entry in player['martial_skills']:
            if skill_entry.get('name') == skill_name:
                skill_entry['equipped'] = True
                skill_entry['装备'] = True
            elif get_martial_category(skill_entry.get('name', '')) == category and skill_entry.get('equipped', False):
                skill_entry['equipped'] = False
                skill_entry['装备'] = False
        
        # 更新玩家属性（应用武功增益）
        logger.error(f"装备武功前玩家属性: 攻击={player.get('attack', 0)}, 防御={player.get('defense', 0)}")
        player = self.bonus_system.apply_bonuses_to_player(player)
        logger.error(f"装备武功后玩家属性: 攻击={player.get('attack', 0)}, 防御={player.get('defense', 0)}")

        # 保存数据到内存和数据库
        self.player_data[str(user_id)] = player
        await self.save_player_data(str(user_id), player)

        # 通知前端更新玩家数据
        await self.notify_player_data_update(user_id, player)

        # 为前端准备list格式的martial_skills（直接使用已经是list格式的数据）
        skills_list = []
        for skill_entry in player['martial_skills']:
            # 直接使用skill_entry，它已经包含了所有必要的字段
            skill = skill_entry.copy()
            # 确保name和名称字段存在
            if 'name' in skill and '名称' not in skill:
                skill['名称'] = skill['name']
            elif '名称' in skill and 'name' not in skill:
                skill['name'] = skill['名称']
            skills_list.append(skill)

        return {
            'type': 'use_martial_success',
            'data': {
                'message': f'成功装备{skill_name}',
                'skill_name': skill_name,
                'martial_skills': skills_list,  # 返回转换后的 list 格式
                'player': player  # 新增，返回最新玩家数据
            }
        }

    async def handle_unequip_martial(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理武学卸下请求"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法卸下武学")
            return {'type': 'unequip_martial_failed', 'data': {'message': '未认证'}}
        
        player = self.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[{client_address}] 玩家数据不存在: {user_id}")
            return {'type': 'unequip_martial_failed', 'data': {'message': '玩家数据不存在'}}
        
        # 兼容 skill_name、名称、name 字段
        skill_name = data.get('skill_name') or data.get('名称') or data.get('name')
        if skill_name:
            # 规范化技能名称
            import unicodedata
            skill_name = unicodedata.normalize('NFKC', str(skill_name).strip())
        # 新增详细日志
        logger.error(f"[卸下武学] data={data}, skill_name={skill_name}")
        martial_skills = player.get('martial_skills', [])
        # 兼容 martial_skills 为 list 的情况，临时转为 dict 用于处理
        if isinstance(martial_skills, list):
            logger.info(f"martial_skills 为 list，临时转为 dict 用于处理")
            martial_skills_dict = {}
            for entry in martial_skills:
                if 'name' in entry:
                    # 规范化键名
                    normalized_name = unicodedata.normalize('NFKC', str(entry['name']).strip())
                    martial_skills_dict[normalized_name] = entry
        else:
            # 如果是dict，需要转换为list格式并保存
            logger.info(f"martial_skills 为 dict，转换为 list 格式")
            skills_list = []
            for name, info in martial_skills.items():
                skill = {'name': name, '名称': name}
                skill.update(info)
                skills_list.append(skill)
            player['martial_skills'] = skills_list
            await self.save_player_data(str(user_id), player)
            # 重新构建dict用于处理
            martial_skills_dict = {}
            for entry in skills_list:
                if 'name' in entry:
                    normalized_name = unicodedata.normalize('NFKC', str(entry['name']).strip())
                    martial_skills_dict[normalized_name] = entry

        logger.error(f"[卸下武学] martial_skills_dict keys={list(martial_skills_dict.keys())}")
        if skill_name not in martial_skills_dict:
            logger.error(f"[卸下武学] skill_name={skill_name} 不在 martial_skills_dict keys={list(martial_skills_dict.keys())}")
            return {'type': 'unequip_martial_failed', 'data': {'message': '武学不存在'}}
        
        skill_data = martial_skills_dict[skill_name]
        if not skill_data.get('equipped', False):
            return {'type': 'unequip_martial_failed', 'data': {'message': '该武学未装备'}}

        # 卸下武学
        skill_data['equipped'] = False
        skill_data['装备'] = False

        # 同步更新原始list格式的数据
        for skill_entry in player['martial_skills']:
            if skill_entry.get('name') == skill_name:
                skill_entry['equipped'] = False
                skill_entry['装备'] = False
                break

        # 更新玩家属性（移除武功增益）
        player = self.bonus_system.apply_bonuses_to_player(player)

        # 检查同类型是否还有已装备的武学
        from enhanced_martial_system import get_martial_category
        category = get_martial_category(skill_name)
        still_equipped = None
        for name, info in martial_skills_dict.items():
            if name != skill_name and info.get('equipped') and get_martial_category(name) == category:
                still_equipped = name
                break
        if still_equipped:
            player['equipped_martial'] = still_equipped
        else:
            # 没有同类已装备，回退为"基本拳法"
            player['equipped_martial'] = '基本拳法'

        # 保存数据到内存和数据库
        self.player_data[str(user_id)] = player
        await self.save_player_data(str(user_id), player)

        # 为前端准备list格式的martial_skills
        # 此时 player['martial_skills'] 已经是列表格式
        skills_list = []
        if isinstance(player['martial_skills'], list):
            # 如果已经是列表，直接使用
            for skill_entry in player['martial_skills']:
                skill = {'name': skill_entry.get('name', ''), '名称': skill_entry.get('name', '')}
                # 复制所有字段，确保中英文字段都存在
                for key, value in skill_entry.items():
                    skill[key] = value
                    # 添加中文字段映射
                    if key == 'level':
                        skill['等级'] = value
                    elif key == 'exp':
                        skill['经验'] = value
                    elif key == 'type':
                        skill['类型'] = value
                    elif key == 'quality':
                        skill['品质'] = value
                    elif key == 'unlocked':
                        skill['解锁'] = value
                    elif key == 'equipped':
                        skill['装备'] = value
                skills_list.append(skill)
        else:
            # 如果是字典格式，转换为列表
            for name, info in player['martial_skills'].items():
                skill = {'name': name, '名称': name}
                # 复制所有字段，确保中英文字段都存在
                for key, value in info.items():
                    skill[key] = value
                    # 添加中文字段映射
                    if key == 'level':
                        skill['等级'] = value
                    elif key == 'exp':
                        skill['经验'] = value
                    elif key == 'type':
                        skill['类型'] = value
                    elif key == 'quality':
                        skill['品质'] = value
                    elif key == 'unlocked':
                        skill['解锁'] = value
                    elif key == 'equipped':
                        skill['装备'] = value
                skills_list.append(skill)

        # 主动推送最新玩家数据
        await self.notify_player_data_update(user_id, player)

        return {
            'type': 'unequip_martial_success',
            'data': {
                'message': f'成功卸下{skill_name}',
                'skill_name': skill_name,
                'martial_skills': skills_list,  # 返回转换后的 list 格式
                'player': player  # 新增，返回最新玩家数据
            }
        }

    async def handle_get_martial_move(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理获取武功招式请求"""
        client_address = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            logger.warning(f"[{client_address}] 未认证，无法获取武功招式")
            return {'type': 'get_martial_move_failed', 'data': {'message': '未认证'}}
        
        player = self.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[{client_address}] 玩家数据不存在: {user_id}")
            return {'type': 'get_martial_move_failed', 'data': {'message': '玩家数据不存在'}}
        
        martial_name = data.get('martial_name')
        if not martial_name:
            return {'type': 'get_martial_move_failed', 'data': {'message': '请指定武功名称'}}
        
        # 获取武功招式
        move_name, move_description = get_martial_move(martial_name)
        
        # 格式化描述
        formatted_description = format_battle_description(
            move_description, player.get('name', '玩家'), '敌人'
        )
        
        return {
            'type': 'get_martial_move_success',
            'data': {
                'martial_name': martial_name,
                'move_name': move_name,
                'move_description': formatted_description
            }
        }

    async def handle_get_map_npcs(self, websocket: WebSocketServerProtocol) -> dict:
        """获取当前地图NPC列表，返回详细信息"""
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        current_map_id = player.get('current_map', 'changan')
        maps = self.game_data['maps']
        map_data = next((m for m in maps.values() if m['id'] == current_map_id), None)
        if not map_data:
            return {'type': 'error', 'data': {'message': '地图信息错误'}}
        # 兼容 NPC 字段为数组或字符串
        npc_field = map_data.get('NPC', [])
        if isinstance(npc_field, list):
            npc_names = [str(n).strip() for n in npc_field if str(n).strip()]
        elif isinstance(npc_field, str):
            npc_names = [n.strip() for n in npc_field.split(',') if n.strip()]
        else:
            npc_names = []
        # 使用NPC系统构造详细NPC信息
        from npc_system import npc_system
        npcs = npc_system.get_map_npcs(current_map_id, npc_names)
        return {'type': 'map_npcs', 'data': {'npcs': npcs}}

    async def handle_npc_function(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理NPC功能请求"""
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}

        player = self.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

        npc_name = data.get('npc_name')
        function_key = data.get('function')
        function_data = data.get('data', {})

        if not npc_name or not function_key:
            return {'type': 'error', 'data': {'message': '缺少必要参数'}}

        from npc_system import npc_system
        npc_system.server = self  # 设置服务器引用

        try:
            result = await npc_system.handle_npc_function(
                player, str(user_id), npc_name, function_key, function_data
            )

            # 重要：NPC功能处理后保存玩家数据
            self.player_data[str(user_id)] = player
            await self.save_player_data(str(user_id), player)

            return result
        except Exception as e:
            import logging
            logging.error(f"NPC功能处理错误: {e}")
            return {'type': 'error', 'data': {'message': '处理NPC功能时发生错误'}}

    async def handle_encounter_monster(self, player, monster, websocket):
        """
        玩家遇到怪物时，根据怪物类型显示不同的战斗弹窗。
        主动攻击型怪物应立即进入战斗。
        """
        is_active = monster.get('attack_mode') == 'active'
        msg = {
            'type': 'encounter_monster',
            'data': {
                'monster': monster,
                'is_active': is_active,
                'message': f"你遇到了{monster['name']}，它正盯着你。"
            }
        }
        await websocket.send(json.dumps(msg, ensure_ascii=False))
        if is_active:
            import copy, asyncio
            monster_copy = copy.deepcopy(monster)
            websocket._server = self
            asyncio.create_task(simulate_battle_stepwise(player, monster_copy, websocket))

    async def handle_start_battle_from_encounter(self, data, websocket):
        """
        前端点击攻击按钮后，开始与被动型怪物战斗。
        data: {monster_id: ...}
        """
        user_id = getattr(websocket, 'user_id', None)
        logger.info(f"[BATTLE] start_battle_from_encounter: user_id={user_id}, data={data}")
        if not user_id:
            logger.error(f"[BATTLE] 未认证，无法开始战斗 user_id={user_id}")
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.player_data.get(str(user_id))
        if not player:
            logger.error(f"[BATTLE] 玩家数据不存在 user_id={user_id}")
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        monster_id = data.get('monster_id')
        monsters = self.game_data.get('monsters')
        logger.info(f"[BATTLE] monsters={monsters}")
        if monsters is None:
            import json, os
            monsters_path = os.path.join(os.path.dirname(__file__), 'monsters.json')
            if not os.path.exists(monsters_path):
                logger.error(f"[BATTLE] 怪物数据文件不存在: {monsters_path}")
                return {'type': 'error', 'data': {'message': '怪物数据文件不存在'}}
            with open(monsters_path, encoding='utf-8') as f:
                monsters = json.load(f)
            self.game_data['monsters'] = monsters
        monster = next((m for m in monsters if m['id'] == monster_id), None)
        logger.info(f"[BATTLE] 找到怪物: monster_id={monster_id}, monster={monster}")
        if not monster:
            logger.error(f"[BATTLE] 怪物不存在 monster_id={monster_id}")
            return {'type': 'error', 'data': {'message': '怪物不存在'}}
        import copy, asyncio
        monster_copy = copy.deepcopy(monster)
        websocket._server = self
        logger.info(f"[BATTLE] 即将启动 simulate_battle_stepwise, player={player}, monster_copy={monster_copy}")
        asyncio.create_task(simulate_battle_stepwise(player, monster_copy, websocket))
        logger.info(f"[BATTLE] 已启动 simulate_battle_stepwise")
        return {
            'type': 'start_battle_from_encounter_success',
            'data': {
                'message': '战斗开始',
                'monster': monster_copy
            }
        }

    async def handle_escape_battle(self, data, websocket):
        """
        处理玩家逃跑请求，根据玩家和怪物身法判定逃跑概率，最低20%，最高60%。
        """
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            await websocket.send(json.dumps({
                'type': 'escape_battle_result',
                'data': {'success': False, 'message': '未认证，无法逃跑'}
            }, ensure_ascii=False))
            return
        player = self.player_data.get(str(user_id))
        if not player:
            await websocket.send(json.dumps({
                'type': 'escape_battle_result',
                'data': {'success': False, 'message': '玩家数据不存在'}
            }, ensure_ascii=False))
            return
        monster_id = data.get('monster_id')
        monsters = self.game_data.get('monsters')
        monster = next((m for m in monsters if m['id'] == monster_id), None)
        if not monster:
            await websocket.send(json.dumps({
                'type': 'escape_battle_result',
                'data': {'success': False, 'message': '怪物不存在'}
            }, ensure_ascii=False))
            return
        import copy
        monster_copy = copy.deepcopy(monster)
        player_agility = player.get('agility', 10)
        monster_agility = monster_copy.get('agility', 10)
        base_rate = 0.2
        agility_diff = player_agility - monster_agility
        bonus_rate = max(0, agility_diff * 0.02)
        escape_rate = min(0.6, base_rate + bonus_rate)
        import random
        success = random.random() < escape_rate
        if success:
            message = f"你成功逃离了战斗！(成功率: {escape_rate:.1%})"
            websocket._escaped = True
            await websocket.send(json.dumps({
                'type': 'escape_battle_result',
                'data': {
                    'success': True,
                    'message': message,
                    'escape_rate': escape_rate
                }
            }, ensure_ascii=False))
            await websocket.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
            await self.save_player_data(str(user_id), player)
        else:
            message = f"你试图逃跑，但被{monster_copy['name']}拦住了！(成功率: {escape_rate:.1%})"
            await websocket.send(json.dumps({
                'type': 'escape_battle_result',
                'data': {
                    'success': False,
                    'message': message,
                    'escape_rate': escape_rate
                }
            }, ensure_ascii=False))
            # 怪物主动攻击一次
            enemy_damage = max(1, monster_copy.get('attack', 15) - player.get('defense', 5))
            player['hp'] = max(0, player.get('hp', 100) - enemy_damage)
            attack_desc = f"{monster_copy['name']}趁你逃跑时发动攻击，造成{enemy_damage}点伤害！"
            await websocket.send(json.dumps({
                'type': 'battle_round',
                'data': {
                    'attacker': monster_copy['name'],
                    'defender': player.get('name', '你'),
                    'damage': enemy_damage,
                    'desc': attack_desc,
                    'player_hp': player['hp'],
                    'monster_name': monster_copy['name']
                }
            }, ensure_ascii=False))
            await websocket.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
            await self.save_player_data(str(user_id), player)

    def weighted_choice(self, monster_probs):
        import random
        total = sum(monster_probs.values())
        r = random.uniform(0, total)
        upto = 0
        for m, prob in monster_probs.items():
            if upto + prob >= r:
                return m
            upto += prob
        return None

    def get_random_monster_for_map(self, map_id):
        # 直接用内存中的地图数据
        maps = self.game_data['maps']  # 兼容 dict 或 list
        if isinstance(maps, dict):
            map_data = next((m for m in maps.values() if m['id'] == map_id), None)
        else:
            map_data = next((m for m in maps if m['id'] == map_id), None)
        if not map_data:
            print(f"[DEBUG] 未找到地图: {map_id}")
            return None
        monster_probs = map_data.get('怪物分布')
        print(f"[DEBUG] 怪物分布: {monster_probs}")
        if not monster_probs:
            monsters = map_data.get('怪物', [])
            if not monsters:
                print(f"[DEBUG] 地图无怪物字段: {map_id}")
                return None
            prob = 1.0 / len(monsters)
            monster_probs = {m: prob for m in monsters}
        monster_id = self.weighted_choice(monster_probs)
        print(f"[DEBUG] weighted_choice抽到怪物ID: {monster_id}")
        monsters_data = self.game_data.get('monsters')
        if monsters_data is None:
            import json, os
            monsters_path = os.path.join(os.path.dirname(__file__), 'monsters.json')
            if not os.path.exists(monsters_path):
                print(f"[DEBUG] 怪物数据文件不存在: {monsters_path}")
                return None
            with open(monsters_path, encoding='utf-8') as f:
                monsters_data = json.load(f)
            self.game_data['monsters'] = monsters_data
        print(f"[DEBUG] 所有怪物ID: {[m['id'] for m in monsters_data]}")
        monster = next((m for m in monsters_data if m['id'] == monster_id), None)
        print(f"[DEBUG] 匹配到怪物: {monster}")
        return monster

    async def handle_get_maps_config(self, websocket):
        logger.info(f"handle_get_maps_config called, maps_config keys: {list(self.maps_config.keys())}")
        return {
            "type": "maps_config",
            "data": self.maps_config
        }

    async def handle_get_items_config(self, websocket):
        """推送物品配置到前端"""
        return {
            "type": "items_config",
            "data": self.items_config
        }

    async def handle_get_martial_configs(self, websocket):
        """推送武功配置到前端"""
        logger.info("开始处理武功配置请求")
        try:
            configs = self.martial_handler.get_martial_configs()
            response = {
                "type": "martial_configs",
                "data": {
                    "configs": configs
                }
            }
            logger.info("武功配置响应准备完成")
            return response
        except Exception as e:
            logger.error(f"加载武功配置失败: {e}")
            return {
                "type": "error",
                "data": {
                    "message": f"加载武功配置失败: {str(e)}"
                }
            }

    async def notify_player_data_update(self, user_id, player):
        """
        主动推送最新玩家数据到前端，user_id为字符串，player为dict。
        """
        ws = self.clients.get(str(user_id))
        if ws:
            try:
                await ws.send(json.dumps({'type': 'player_data', 'data': player}, ensure_ascii=False))
            except Exception as e:
                logger.error(f"[notify_player_data_update] 推送player_data失败: {e}")

    async def train_martial(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """自动修炼到下一级，消耗武学点和内力，应用悟性加成，MUD风格逐步修炼"""
        user_id = getattr(websocket, 'user_id', None)
        player = self.player_data.get(str(user_id))
        skill_name = data.get('name') or data.get('名称')
        martial_skills = player.get('martial_skills', {})
        if isinstance(martial_skills, list):
            martial_skills = {entry['name']: entry for entry in martial_skills if 'name' in entry}
            player['martial_skills'] = martial_skills
        if skill_name not in martial_skills:
            return {'type': 'train_martial_failed', 'data': {'message': '武学不存在'}}
        skill_data = martial_skills[skill_name]
        skill_points = player.get('skill_points', 0)
        mp = player.get('mp', 0)
        mp_per_point = 1
        if skill_points <= 0:
            return {'type': 'train_martial_failed', 'data': {'message': '武学点不足'}}
        if mp < mp_per_point:
            return {'type': 'train_martial_failed', 'data': {'message': '内力不足'}}
        # 悟性加成
        intelligence = player.get('talent', {}).get('悟性', 15)
        add_percent = max(0, intelligence - 15) * 0.01
        quality = skill_data.get('quality', '普通')
        coefficient = {'普通': 50, '稀有': 80, '绝世': 120, '传说': 200}.get(quality, 50)
        exp = skill_data.get('exp', 0)
        level = skill_data.get('level', 0)
        used_points = 0
        is_basic = skill_name.startswith('基本')
        if not is_basic:
            type_ = skill_data.get('type', '')
            basic_name = '基本' + type_
            basic_skill = martial_skills.get(basic_name)
            max_level = basic_skill.get('level', 0) if basic_skill else 0
        else:
            max_level = float('inf')
        total_exp_gain = 0
        total_level_up = 0
        # MUD风格逐步修炼
        while skill_points > 0 and level < max_level and mp >= mp_per_point:
            need_exp = coefficient * (level + 1) ** 2 - exp
            exp_gain = min(1 * (1 + add_percent), need_exp)
            exp += exp_gain
            used_points += 1
            skill_points -= 1
            mp -= mp_per_point
            total_exp_gain += exp_gain
            level_up_before = level
            if exp >= coefficient * (level + 1) ** 2:
                exp -= coefficient * (level + 1) ** 2
                level += 1
                total_level_up += 1
                # ====== 基础武功每10级自动加天赋属性 ======
                # 拳脚类武学
                fist_skills = ['基本拳法', '基本掌法', '基本腿法', '基本爪法', '基本指法', '基本手法', '基本鞭法', '基本锤法']
                # 轻功
                agility_skills = ['基本轻功']
                # 内功
                constitution_skills = ['基本内功']
                # 读书写字
                wisdom_skills = ['读书写字']
                if skill_name in fist_skills + agility_skills + constitution_skills + wisdom_skills:
                    old_ten = level_up_before // 10
                    new_ten = level // 10
                    if new_ten > old_ten:
                        if 'talent' not in player:
                            player['talent'] = self.generate_talent_attributes()
                        if skill_name in fist_skills:
                            player['talent']['力量'] += 1
                        elif skill_name in agility_skills:
                            player['talent']['身法'] += 1
                        elif skill_name in constitution_skills:
                            player['talent']['根骨'] += 1
                        elif skill_name in wisdom_skills:
                            player['talent']['悟性'] += 1
                # ====== 基础武功每10级自动加天赋属性结束 ======
            skill_data['exp'] = int(exp)
            skill_data['level'] = int(level)
            player['skill_points'] = skill_points
            player['mp'] = mp
            self.player_data[str(user_id)] = player
            # 每次修炼后推送一次 player_data
            await self.notify_player_data_update(user_id, player)
            # 新增：推送修炼日志消息
            log_msg = f"修炼{skill_name}，消耗1武学点，消耗{mp_per_point}内力，获得{int(exp_gain)}经验，当前等级{level}，当前经验{skill_data['exp']}，剩余武学点{skill_points}，剩余内力{mp}"
            try:
                await websocket.send(json.dumps({'type': 'train_martial_log', 'data': {'message': log_msg}}, ensure_ascii=False))
            except Exception as e:
                logger.error(f"[train_martial_log] 推送失败: {e}")
            await asyncio.sleep(0.5)
            if level >= max_level:
                break
        # 修炼结束后，强制保存玩家数据到数据库，确保升级等数据不会丢失
        await self.save_player_data(str(user_id), player)
        msg = f"【本次修炼总结】修炼{skill_name}，消耗{used_points}武学点，消耗{used_points*mp_per_point}内力，获得{int(total_exp_gain)}经验，提升{total_level_up}级，当前等级{level}，当前经验{int(exp)}，剩余武学点{skill_points}，剩余内力{mp}"
        # 新增：修炼总结也通过日志推送
        try:
            await websocket.send(json.dumps({'type': 'train_martial_log', 'data': {'message': msg}}, ensure_ascii=False))
        except Exception as e:
            logger.error(f'[train_martial_log] 修炼总结推送失败: {e}')
        return {
            'type': 'train_martial_success',
            'data': {
                'message': msg,
                'skill_name': skill_name,
                'used_points': used_points,
                'exp_gain': int(total_exp_gain),
                'level_up': total_level_up,
                'new_level': int(level),
                'martial_skills': player['martial_skills'],
                'skill_points': skill_points,
                'mp': mp
            }
        }

    # ==================== 排行榜和兑换码相关方法 ====================

    async def handle_get_ranking(self, data, websocket):
        """处理获取排行榜请求"""
        try:
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                return {'type': 'error', 'data': {'message': '未认证'}}

            ranking_type = data.get('type', 'wealth')
            limit = data.get('limit', 30)

            logger.info(f"获取排行榜请求: user_id={user_id}, type={ranking_type}, limit={limit}")

            async with aiosqlite.connect(self.db_path) as db:
                # 根据排行榜类型构建查询
                if ranking_type == 'wealth':
                    # 富豪榜 - 按银两排序
                    query = """
                    SELECT u.character_name, p.data, u.id
                    FROM users u
                    JOIN players p ON u.id = p.user_id
                    WHERE u.status = 'active' AND p.data IS NOT NULL
                    ORDER BY CAST(COALESCE(json_extract(p.data, '$.money'), 0) AS INTEGER) DESC
                    LIMIT ?
                    """
                elif ranking_type == 'experience':
                    # 经验榜 - 按历练值排序
                    query = """
                    SELECT u.character_name, p.data, u.id
                    FROM users u
                    JOIN players p ON u.id = p.user_id
                    WHERE u.status = 'active' AND p.data IS NOT NULL
                    ORDER BY CAST(COALESCE(json_extract(p.data, '$.experience'), 0) AS INTEGER) DESC
                    LIMIT ?
                    """
                elif ranking_type == 'adventure':
                    # 肝帝榜 - 按闯江湖次数排序
                    query = """
                    SELECT u.character_name, p.data, u.id
                    FROM users u
                    JOIN players p ON u.id = p.user_id
                    WHERE u.status = 'active' AND p.data IS NOT NULL
                    ORDER BY CAST(COALESCE(json_extract(p.data, '$.adventure_count'), 0) AS INTEGER) DESC
                    LIMIT ?
                    """
                else:
                    return {'type': 'error', 'data': {'message': '无效的排行榜类型'}}

                logger.info(f"执行排行榜查询: {query}")
                async with db.execute(query, (limit,)) as cursor:
                    rows = await cursor.fetchall()

                logger.info(f"查询到 {len(rows)} 条排行榜数据")

                # 构建排行榜数据
                ranking_list = []
                for i, row in enumerate(rows):
                    character_name, player_data_str, player_user_id = row
                    try:
                        player_data = json.loads(player_data_str) if player_data_str else {}
                    except Exception as e:
                        logger.error(f"解析玩家数据失败: {e}, data: {player_data_str}")
                        player_data = {}

                    ranking_item = {
                        'id': player_user_id,
                        'name': character_name or '未知玩家',
                        'money': max(0, int(player_data.get('money', 0))),
                        'experience': max(0, int(player_data.get('experience', 0))),
                        'adventureCount': max(0, int(player_data.get('adventure_count', 0)))
                    }
                    ranking_list.append(ranking_item)

                    if i < 3:  # 只记录前3名的详细信息
                        logger.info(f"排行榜第{i+1}名: {ranking_item}")

                # 获取当前玩家的排名
                my_ranking = None
                current_player_data = self.player_data.get(str(user_id))
                if current_player_data:
                    # 查询当前玩家在排行榜中的位置
                    if ranking_type == 'wealth':
                        count_query = """
                        SELECT COUNT(*) + 1 as rank
                        FROM users u
                        JOIN players p ON u.id = p.user_id
                        WHERE u.status = 'active' AND p.data IS NOT NULL
                        AND CAST(COALESCE(json_extract(p.data, '$.money'), 0) AS INTEGER) > ?
                        """
                        value = max(0, int(current_player_data.get('money', 0)))
                    elif ranking_type == 'experience':
                        count_query = """
                        SELECT COUNT(*) + 1 as rank
                        FROM users u
                        JOIN players p ON u.id = p.user_id
                        WHERE u.status = 'active' AND p.data IS NOT NULL
                        AND CAST(COALESCE(json_extract(p.data, '$.experience'), 0) AS INTEGER) > ?
                        """
                        value = max(0, int(current_player_data.get('experience', 0)))
                    elif ranking_type == 'adventure':
                        count_query = """
                        SELECT COUNT(*) + 1 as rank
                        FROM users u
                        JOIN players p ON u.id = p.user_id
                        WHERE u.status = 'active' AND p.data IS NOT NULL
                        AND CAST(COALESCE(json_extract(p.data, '$.adventure_count'), 0) AS INTEGER) > ?
                        """
                        value = max(0, int(current_player_data.get('adventure_count', 0)))

                    async with db.execute(count_query, (value,)) as cursor:
                        rank_row = await cursor.fetchone()
                        rank = rank_row[0] if rank_row else 999

                    my_ranking = {
                        'rank': rank,
                        'money': current_player_data.get('money', 0),
                        'experience': current_player_data.get('experience', 0),
                        'adventureCount': current_player_data.get('adventure_count', 0)
                    }

                result = {
                    'type': 'success',
                    'data': {
                        'list': ranking_list,
                        'myRanking': my_ranking
                    }
                }
                logger.info(f"排行榜数据返回成功: 列表长度={len(ranking_list)}, 我的排名={my_ranking}")
                return result

        except Exception as e:
            logger.error(f"获取排行榜失败: {e}")
            return {'type': 'error', 'data': {'message': '获取排行榜失败'}}

    async def handle_redeem_code(self, data, websocket):
        """处理兑换码兑换请求"""
        try:
            from datetime import datetime
            logger.info(f"开始处理兑换码请求")

            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                logger.info(f"兑换码请求失败：用户未认证")
                return {'type': 'error', 'data': {'message': '未认证'}}

            code = data.get('code', '').strip()
            if not code:
                logger.info(f"兑换码请求失败：兑换码为空")
                return {'type': 'error', 'data': {'message': '请输入兑换码'}}

            logger.info(f"用户 {user_id} 尝试兑换码: {code}")

            logger.info(f"开始查询兑换码信息")
            async with aiosqlite.connect(self.db_path) as db:
                # 查询兑换码信息
                async with db.execute("""
                    SELECT id, name, description, rewards, max_uses, current_uses, expires_at, status
                    FROM redeem_codes
                    WHERE code = ?
                """, (code,)) as cursor:
                    code_row = await cursor.fetchone()

                if not code_row:
                    logger.info(f"兑换码不存在: {code}")
                    return {'type': 'error', 'data': {'message': '兑换码不存在'}}

                logger.info(f"找到兑换码: {code_row}")

                code_id, name, description, rewards_str, max_uses, current_uses, expires_at, status = code_row

                # 检查兑换码状态
                if status != 'active':
                    return {'type': 'error', 'data': {'message': '兑换码已失效'}}

                # 检查是否过期
                if expires_at:
                    expire_time = datetime.fromisoformat(expires_at)
                    if datetime.now() > expire_time:
                        return {'type': 'error', 'data': {'message': '兑换码已过期'}}

                # 检查使用次数
                if current_uses >= max_uses:
                    return {'type': 'error', 'data': {'message': '兑换码已达到使用上限'}}

                # 检查用户是否已经使用过
                async with db.execute("""
                    SELECT id FROM redeem_code_usage
                    WHERE user_id = ? AND code_id = ?
                """, (user_id, code_id)) as cursor:
                    usage_row = await cursor.fetchone()

                if usage_row:
                    return {'type': 'error', 'data': {'message': '您已经使用过这个兑换码'}}

                # 解析奖励
                try:
                    rewards = json.loads(rewards_str)
                except:
                    return {'type': 'error', 'data': {'message': '兑换码奖励配置错误'}}

                # 发放奖励
                logger.info(f"开始发放奖励")
                player = self.player_data.get(str(user_id))
                if not player:
                    logger.info(f"玩家数据不存在: {user_id}")
                    return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

                logger.info(f"玩家数据找到，开始处理奖励: {rewards}")

                reward_list = []
                for reward in rewards:
                    reward_type = reward.get('type')
                    reward_name = reward.get('name')
                    quantity = reward.get('quantity', 1)

                    if reward_type == 'money':
                        # 银两奖励
                        player['money'] = player.get('money', 0) + quantity
                        reward_list.append({'name': '银两', 'quantity': quantity})
                    elif reward_type == 'experience':
                        # 经验奖励
                        player['experience'] = player.get('experience', 0) + quantity
                        reward_list.append({'name': '历练值', 'quantity': quantity})
                    elif reward_type == 'item':
                        # 物品奖励
                        item_id = reward.get('item_id')
                        if item_id:
                            # 添加物品到背包
                            if 'inventory' not in player:
                                player['inventory'] = []

                            # 查找是否已有相同物品
                            found = False
                            for item in player['inventory']:
                                if item.get('id') == item_id:
                                    item['quantity'] = item.get('quantity', 1) + quantity
                                    found = True
                                    break

                            if not found:
                                # 从物品配置中获取物品信息
                                item_config = self.items_config.get(item_id)
                                if item_config:
                                    new_item = {
                                        'id': item_id,
                                        'name': item_config.get('name', reward_name),
                                        'quantity': quantity,
                                        'quality': item_config.get('quality', 'common'),
                                        'type': item_config.get('type', 'misc')
                                    }
                                    player['inventory'].append(new_item)

                            reward_list.append({'name': reward_name, 'quantity': quantity})

                # 记录兑换
                current_time = datetime.now().isoformat()
                await db.execute("""
                    INSERT INTO redeem_code_usage (user_id, code_id, code, rewards, redeemed_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (user_id, code_id, code, json.dumps(reward_list), current_time))

                # 更新兑换码使用次数
                await db.execute("""
                    UPDATE redeem_codes
                    SET current_uses = current_uses + 1
                    WHERE id = ?
                """, (code_id,))

                await db.commit()

                # 保存玩家数据
                logger.info(f"保存玩家数据")
                await self.save_player_data(str(user_id), player)

                logger.info(f"兑换码处理成功，返回结果: {reward_list}")
                return {
                    'type': 'success',
                    'data': {
                        'message': f'兑换成功！',
                        'rewards': reward_list
                    }
                }

        except Exception as e:
            logger.error(f"兑换码兑换失败: {e}")
            return {'type': 'error', 'data': {'message': '兑换失败，请重试'}}

    async def handle_get_redeem_history(self, websocket):
        """处理获取兑换历史请求"""
        try:
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                return {'type': 'error', 'data': {'message': '未认证'}}

            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute("""
                    SELECT code, rewards, redeemed_at
                    FROM redeem_code_usage
                    WHERE user_id = ?
                    ORDER BY redeemed_at DESC
                    LIMIT 20
                """, (user_id,)) as cursor:
                    rows = await cursor.fetchall()

                history = []
                for row in rows:
                    code, rewards_str, redeemed_at = row
                    try:
                        rewards = json.loads(rewards_str)
                    except:
                        rewards = []

                    history.append({
                        'code': code,
                        'rewards': rewards,
                        'redeemTime': redeemed_at
                    })

                return {
                    'type': 'success',
                    'data': {
                        'history': history
                    }
                }

        except Exception as e:
            logger.error(f"获取兑换历史失败: {e}")
            return {'type': 'error', 'data': {'message': '获取兑换历史失败'}}

    # ==================== 新增功能：地图玩家和聊天系统 ====================

    async def handle_get_map_players(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """获取当前地图的玩家列表"""
        try:
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                return {'type': 'error', 'data': {'message': '未认证'}}

            player = self.player_data.get(str(user_id))
            if not player:
                return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

            map_id = data.get('map_id') or player.get('current_map', 'changan')

            # 获取当前地图的所有在线玩家
            map_players = []
            for pid, pdata in self.player_data.items():
                if pdata.get('current_map') == map_id and pid != str(user_id):
                    # 构建玩家信息
                    player_info = {
                        'id': pid,
                        'name': pdata.get('name', ''),
                        'character_name': pdata.get('character_name', ''),
                        'level': pdata.get('level', 1),
                        'status': pdata.get('status', 'online'),
                        'avatar': '/static/npc/default.png'  # 默认头像
                    }
                    map_players.append(player_info)

            logger.info(f"地图 {map_id} 玩家列表: {len(map_players)} 人")

            return {
                'type': 'map_players_success',
                'data': {
                    'map_id': map_id,
                    'players': map_players
                }
            }

        except Exception as e:
            logger.error(f"获取地图玩家失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取地图玩家失败: {str(e)}'}}

    async def handle_player_action(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理玩家交互操作"""
        try:
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                return {'type': 'error', 'data': {'message': '未认证'}}

            player = self.player_data.get(str(user_id))
            if not player:
                return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

            action = data.get('action')
            target_player_id = data.get('target_player_id')
            target_player_name = data.get('target_player_name', '未知玩家')

            if not action or not target_player_id:
                return {'type': 'error', 'data': {'message': '参数错误'}}

            # 检查目标玩家是否存在
            target_player = self.player_data.get(str(target_player_id))
            if not target_player:
                return {'type': 'error', 'data': {'message': '目标玩家不在线'}}

            # 检查是否在同一地图
            if player.get('current_map') != target_player.get('current_map'):
                return {'type': 'error', 'data': {'message': '目标玩家不在同一地图'}}

            # 处理不同的交互操作
            if action == 'sneak_attack':
                # 偷袭操作 - 触发战斗
                import random
                success_rate = random.randint(1, 100)
                if success_rate <= 70:  # 70%成功率触发战斗
                    # 创建一个虚拟的敌人对象，使用目标玩家的属性
                    enemy = {
                        'name': target_player.get('character_name', target_player_name),
                        'hp': target_player.get('hp', 100),
                        'max_hp': target_player.get('max_hp', 100),
                        'attack': target_player.get('attack', 20),
                        'defense': target_player.get('defense', 10),
                        'agility': target_player.get('agility', 10),
                        'level': target_player.get('level', 1),
                        'is_player': True  # 标记为玩家战斗
                    }

                    # 导入战斗系统
                    from battle_system import start_battle

                    # 开始战斗
                    await start_battle(player, enemy, websocket, self)

                    return {
                        'type': 'player_action_success',
                        'data': {
                            'action': action,
                            'message': f"你成功偷袭了 {target_player_name}，战斗开始！",
                            'battle_started': True,
                            'player': player,
                            'enemy': enemy,
                            'stage': 'battle'
                        }
                    }
                else:
                    message = f"你试图偷袭 {target_player_name}，但是被发现了！"

            elif action == 'give':
                # 给与操作 - 这里可以扩展为物品交易系统
                message = f"你向 {target_player_name} 发起了交易请求！"
                # TODO: 实现物品交易逻辑

            elif action == 'steal':
                # 偷窃操作 - 这里可以扩展为偷窃系统
                success_rate = random.randint(1, 100)
                if success_rate <= 30:  # 30%成功率
                    message = f"你成功从 {target_player_name} 身上偷到了一些银两！"
                    # TODO: 实现偷窃奖励逻辑
                else:
                    message = f"你试图偷窃 {target_player_name}，但是失败了！"

            elif action == 'view':
                # 查看操作 - 显示目标玩家信息
                target_info = {
                    'name': target_player.get('character_name', ''),
                    'level': target_player.get('level', 1),
                    'hp': target_player.get('hp', 100),
                    'max_hp': target_player.get('max_hp', 100),
                    'status': target_player.get('status', 'normal')
                }
                return {
                    'type': 'player_action_success',
                    'data': {
                        'action': action,
                        'message': f"查看 {target_player_name} 的信息",
                        'target_info': target_info
                    }
                }
            else:
                return {'type': 'error', 'data': {'message': '未知操作类型'}}

            logger.info(f"玩家交互: {player.get('character_name')} {action} {target_player_name}")

            return {
                'type': 'player_action_success',
                'data': {
                    'action': action,
                    'message': message
                }
            }

        except Exception as e:
            logger.error(f"玩家交互失败: {e}")
            return {'type': 'error', 'data': {'message': f'操作失败: {str(e)}'}}




    async def broadcast_map_players_update(self, map_id: str):
        """广播地图玩家更新给该地图的所有玩家"""
        try:
            # 获取该地图的所有玩家
            map_players = []
            map_player_clients = []

            for pid, pdata in self.player_data.items():
                if pdata.get('current_map') == map_id:
                    player_info = {
                        'id': pid,
                        'name': pdata.get('name', ''),
                        'character_name': pdata.get('character_name', ''),
                        'level': pdata.get('level', 1),
                        'status': pdata.get('status', 'online'),
                        'avatar': '/static/npc/default.png'
                    }
                    map_players.append(player_info)

                    # 找到对应的WebSocket连接
                    for client_id, client_ws in self.clients.items():
                        if getattr(client_ws, 'user_id', None) == pid:
                            map_player_clients.append(client_ws)
                            break

            # 构建更新消息
            update_data = {
                'type': 'map_players_update',
                'data': {
                    'map_id': map_id,
                    'players': map_players
                }
            }

            # 发送给该地图的所有玩家
            for client_ws in map_player_clients:
                try:
                    await client_ws.send(json.dumps(update_data, ensure_ascii=False))
                except Exception as e:
                    logger.warning(f"发送地图玩家更新失败: {e}")

        except Exception as e:
            logger.error(f"广播地图玩家更新失败: {e}")

async def main():
    """主函数"""
    game_server = GameServer()
    
    logger.info("=" * 60)
    logger.info("🎮 仗剑江湖行 WebSocket 服务器启动中...")
    logger.info("=" * 60)
    
    # 启动WebSocket服务器
    server = await websockets.serve(
        game_server.handle_client,
        "0.0.0.0",
        8080,
        ping_interval=20,
        ping_timeout=20
    )
    
    logger.info("✅ 服务器启动成功!")
    logger.info("📍 监听地址: 0.0.0.0:8080")
    logger.info("🔗 WebSocket地址: ws://localhost:8080")
    logger.info("⏰ 等待客户端连接...")
    logger.info("=" * 60)
    
    await server.wait_closed()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 服务器已停止")
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        logger.error(f"❌ 异常详情: {traceback.format_exc()}") 