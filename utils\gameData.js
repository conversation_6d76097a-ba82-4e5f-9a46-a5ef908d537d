import wsManager from './websocket.js'
// 游戏数据模型
export default {
  // 玩家基础属性
  playerStats: {
    // 战斗属性
    hp: 100,           // 气血
    maxHp: 100,        // 最大气血
    mp: 50,            // 内力
    maxMp: 50,         // 最大内力
    attack: 10,        // 攻击
    defense: 5,        // 防御
    hitRate: 0.8,      // 命中率
    dodgeRate: 0.1,    // 闪避率
    critRate: 0.05,    // 暴击率
    critDamage: 1.5,   // 暴击伤害
    blockRate: 0.05,   // 招架率
    blockValue: 3,     // 格挡值
    
    // 武学属性
    agility: 10,       // 身法
    internalPower: 5,  // 内功修为
    meridians: 1,      // 经脉通畅度
    
    // 成长属性
    stamina: 100,      // 体力值
    maxStamina: 100,   // 最大体力值
    energy: 100,       // 精力值
    maxEnergy: 100,    // 最大精力值
    wisdom: 10,        // 悟性
    
    // 江湖属性
    reputation: 0,     // 声望
    karma: 0,          // 因果值
    bloodlust: 0,      // 杀气
    friendliness: 0,   // 友好度
  },

  // 装备类型
  equipmentTypes: {
    WEAPON: 'weapon',      // 武器
    ARMOR: 'armor',        // 护甲
    NECKLACE: 'necklace',  // 项链
    BRACELET: 'bracelet',  // 手镯
    MOUNT: 'mount',        // 坐骑
    TOOL: 'tool'           // 生活工具
  },

  // 装备品质
  equipmentQuality: {
    COMMON: { name: '普通', color: '#9e9e9e', multiplier: 1.0 },
    UNCOMMON: { name: '精品', color: '#4caf50', multiplier: 1.2 },
    RARE: { name: '稀有', color: '#2196f3', multiplier: 1.5 },
    EPIC: { name: '史诗', color: '#9c27b0', multiplier: 2.0 },
    LEGENDARY: { name: '传说', color: '#ff9800', multiplier: 3.0 },
    MYTHIC: { name: '仙品', color: '#f44336', multiplier: 5.0 }
  },

  // 武功类型
  skillTypes: {
    EXTERNAL: 'external',  // 外功
    INTERNAL: 'internal',  // 内功
    LIGHTNESS: 'lightness', // 轻功
    MIND: 'mind',          // 心法
    SPECIAL: 'special'     // 特技
  },

  // 武功品级
  skillGrades: {
    YELLOW: { name: '黄级', color: '#ffeb3b', multiplier: 1.0 },
    MYSTIC: { name: '玄级', color: '#9c27b0', multiplier: 1.5 },
    EARTH: { name: '地级', color: '#795548', multiplier: 2.0 },
    HEAVEN: { name: '天级', color: '#2196f3', multiplier: 3.0 },
    SUPREME: { name: '绝世', color: '#f44336', multiplier: 5.0 }
  },

  // 事件类型
  eventTypes: {
    GOOD_FORTUNE: 1,    // 好运事件
    NPC_ENCOUNTER: 2,   // 遭遇NPC
    GATHERING: 3,       // 采集事件
    EMPTY: 4,           // 普通事件
    ADVENTURE: 5,       // 奇遇事件
    ENMITY: 6,          // 恩怨事件
    TEAM: 7,            // 组队事件
    CARAVAN: 8,         // 商队事件
    RUMOR: 9,           // 江湖传闻
    WEATHER: 10,        // 天气事件
    MYSTERY: 11,        // 神秘事件
    FESTIVAL: 12        // 节日事件
  },

  // 资源类型
  resourceTypes: {
    WOOD: 'wood',       // 木材
    ORE: 'ore',         // 矿石
    LEATHER: 'leather', // 皮毛
    HERB: 'herb'        // 草药
  },

  // 状态类型
  statusTypes: {
    NORMAL: 'normal',     // 正常
    INJURED: 'injured',   // 重伤
    INTERNAL_INJURY: 'internal_injury', // 内伤
    POISONED: 'poisoned', // 中毒
    TIRED: 'tired'        // 疲劳
  }
}

// 游戏工具函数
export const gameUtils = {
  // 计算伤害
  calculateDamage(attacker, defender, skill = null) {
    let baseDamage = attacker.attack;
    
    // 技能加成
    if (skill) {
      baseDamage *= skill.damageMultiplier || 1.0;
    }
    
    // 暴击判定
    const isCrit = Math.random() < attacker.critRate;
    if (isCrit) {
      baseDamage *= attacker.critDamage;
    }
    
    // 防御减伤
    const finalDamage = Math.max(1, baseDamage - defender.defense);
    
    return {
      damage: Math.floor(finalDamage),
      isCrit: isCrit
    };
  },

  // 命中判定
  isHit(attacker, defender) {
    const hitChance = attacker.hitRate - defender.dodgeRate;
    return Math.random() < hitChance;
  },

  // 招架判定
  isBlocked(defender) {
    return Math.random() < defender.blockRate;
  },

  // 随机事件生成（前端不参与计算，仅用于显示）
  generateRandomEvent() {
    // 前端不参与事件计算，所有逻辑在后端处理
    // 这里仅保留基本的事件定义用于显示
    const events = [
      { type: 1, name: '好运事件', description: '你遇到了一个善良的商人，获得了一些奖励。' },
      { type: 2, name: '遭遇NPC', description: '你遇到了一个神秘的江湖人士。' },
      { type: 3, name: '采集事件', description: '你发现了一片资源丰富的区域。' },
      { type: 4, name: '普通事件', description: '你漫步在江湖中，感受着武侠世界的魅力。' },
      { type: 5, name: '奇遇事件', description: '你遇到了一个千载难逢的奇遇！' },
      { type: 6, name: '恩怨事件', description: '江湖恩怨，是非难辨。' },
      { type: 7, name: '组队事件', description: '你遇到了其他江湖人士，可以组队冒险。' }
    ];
    
    // 返回普通事件作为默认值（实际事件由后端生成）
    return events[3];
  },

  // 格式化数字
  formatNumber(num) {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万';
    }
    return num.toString();
  },

  // 获取品质颜色
  getQualityColor(quality) {
    const qualities = {
      'common': '#9e9e9e',
      'uncommon': '#4caf50',
      'rare': '#2196f3',
      'epic': '#9c27b0',
      'legendary': '#ff9800',
      'mythic': '#f44336'
    };
    return qualities[quality] || '#9e9e9e';
  },

  sendMessage(msg) {
    return new Promise((resolve, reject) => {
      // 兼容 type/data 结构和完整对象
      let type = msg.type;
      let data = msg.data || {};

      // 特殊处理NPC功能消息 - 需要传递完整的msg对象
      if (type === 'npc_function') {
        // 对于npc_function，需要传递除了type之外的所有参数
        data = {
          npc_name: msg.npc_name,
          function: msg.function,
          data: msg.data || {}
        };
      }
      
      // 特殊处理装备数据请求
      if (type === 'get_equipment_data') {
        // 使用get_player_data代替，因为后端可能不支持get_equipment_data
        type = 'get_player_data';
      }
      
      // 添加超时处理
      const timeoutDuration = type === 'escape_battle' ? 15000 :
                             type === 'sect_action' ? 20000 : 10000; // 逃跑请求15秒，门派操作20秒，其他10秒
      let isResolved = false; // 添加标志防止重复resolve
      const timeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          cleanup();
          resolve({
            type: type + '_timeout',
            data: { message: '请求超时，请检查网络连接' }
          });
        }
      }, timeoutDuration);
      
      // 清理函数
      const cleanup = () => {
        clearTimeout(timeout);
        wsManager.off(type + '_success', handler);
        wsManager.off(type + '_failed', handler);
        wsManager.off(type + '_error', handler);
        wsManager.off('error', errorHandler);
        // 兼容后端直接返回type为market_list等的事件
        if (type === 'market_action') {
          wsManager.off('market_list', handler);
          wsManager.off('success', handler);
          wsManager.off('error', handler);
        }
        // 兼容地图NPC请求
        if (type === 'get_map_npcs') {
          wsManager.off('map_npcs', handler);
        }

        // 兼容NPC功能请求
        if (type === 'npc_function') {
          wsManager.off('npc_talk', handler);
          wsManager.off('shop_items', handler);
          wsManager.off('buy_success', handler);
          wsManager.off('sell_success', handler);
          wsManager.off('transport_destinations', handler);
          wsManager.off('transport_success', handler);
          wsManager.off('heal_success', handler);
          wsManager.off('info_services', handler);
          wsManager.off('info_success', handler);
        }
        // 兼容天赋加成请求
        if (type === 'get_bonus_summary') {
          wsManager.off('bonus_summary', handler);
        }
        
        // 兼容背包数据请求
        if (type === 'get_inventory_data') {
          wsManager.off('inventory_data', handler);
        }
        
        // 兼容武功数据请求
        if (type === 'get_skills_data') {
          wsManager.off('skills_data', handler);
        }

        // 兼容武功配置请求
        if (type === 'get_martial_configs') {
          wsManager.off('martial_configs', handler);
        }
        
        // 兼容装备请求
        if (type === 'equip_item') {
          wsManager.off('equip_success', handler);
          wsManager.off('equip_failed', handler);
        }
        
        // 兼容player_data事件
        if (type === 'get_player_data') {
          wsManager.off('player_data', handler);
        }
        
        // 兼容get_player_data_success等事件
        if (type === 'get_player_data') {
          wsManager.off('get_player_data_success', handler);
          wsManager.off('get_player_data_failed', handler);
          wsManager.off('get_player_data_error', handler);
        }
        // 兼容打造请求
        if (type === 'crafting_action') {
          wsManager.off('get_craftable_success', handler);
          wsManager.off('get_craftable_failed', handler);
          wsManager.off('craft_success', handler);
          wsManager.off('craft_failed', handler);
        }
        
        // 兼容战斗开始请求
        if (type === 'start_battle_from_encounter') {
          wsManager.off('start_battle_from_encounter_success', handler);
          wsManager.off('start_battle_from_encounter_failed', handler);
        }
        
        // 兼容逃跑请求
        if (type === 'escape_battle') {
          wsManager.off('escape_battle_result', handler);
        }

        // 兼容排行榜请求
        if (type === 'get_ranking') {
          wsManager.off('success', handler);
          wsManager.off('error', handler);
        }

        // 兼容采集动作请求
        if (type === 'gather_action') {
          wsManager.off('gathering_result', handler);
        }
      };
      
      // 监听一次性响应
      const handler = (resp) => {
        if (isResolved) return; // 防止重复处理
        isResolved = true;
        cleanup();
        
        // 如果是get_player_data但原请求是get_equipment_data，提取装备数据
        if (type === 'get_player_data' && msg.type === 'get_equipment_data') {
          // 从玩家数据中提取装备数据
          if (resp && resp.equipment) {
            resolve({
              type: 'equipment_data',
              data: resp.equipment
            });
            return;
          }
        }
        
        // 特殊处理某些响应类型
        let responseType = type + (resp.success !== false ? '_success' : '_failed');
        
        // 对于背包数据，直接返回inventory_data类型
        if (type === 'get_inventory_data' && resp.inventory !== undefined) {
          responseType = 'inventory_data';
        }
        
        // 对于武功数据，直接返回skills_data类型
        if (type === 'get_skills_data' && resp.skills !== undefined) {
          responseType = 'skills_data';
        }
        
        // 对于装备请求，直接返回equip_success或equip_failed类型
        if (type === 'equip_item') {
          if (resp.success !== false) {
            responseType = 'equip_success';
          } else {
            responseType = 'equip_failed';
          }
        }
        
        // 对于玩家数据请求，直接返回player_data类型
        if (type === 'get_player_data' && resp.type === 'player_data') {
          responseType = 'player_data';
        }

        // 特殊处理市场操作
        if (type === 'market_action') {
          // 直接使用后端返回的type
          responseType = resp.type || responseType;
          // 对于市场操作，直接返回后端的响应结构
          resolve({
            type: responseType,
            data: resp.data || resp
          });
          return;
        }

        // 特殊处理打造请求
        if (type === 'crafting_action' && resp.type === 'get_craftable_success') {
          responseType = 'get_craftable_success';
        }
        if (type === 'crafting_action' && resp.type === 'craft_success') {
          responseType = 'craft_success';
        }
        if (type === 'crafting_action' && resp.type === 'craft_failed') {
          responseType = 'craft_failed';
        }

        // 特殊处理排行榜请求
        if (type === 'get_ranking') {
          // 如果后端返回success，使用标准的get_ranking_success格式
          if (resp.type === 'success') {
            responseType = 'get_ranking_success';
          } else if (resp.type === 'error') {
            responseType = 'get_ranking_failed';
          }
          resolve({
            type: responseType,
            data: resp.data || resp
          });
          return;
        }

        // 特殊处理兑换码请求
        if (type === 'redeem_code') {
          // 如果后端返回success，使用标准的redeem_code_success格式
          if (resp.type === 'success') {
            responseType = 'redeem_code_success';
          } else if (resp.type === 'error') {
            responseType = 'redeem_code_failed';
          }
          resolve({
            type: responseType,
            data: resp.data || resp
          });
          return;
        }

        // 特殊处理NPC功能请求
        if (type === 'npc_function') {
          // 直接使用后端返回的type和完整响应结构
          resolve({
            type: resp.type || responseType,
            data: resp.data || resp
          });
          return;
        }



        resolve({
          type: responseType,
          data: resp
        });
      };
      
      // 错误处理
      const errorHandler = (error) => {
        // 如果是装备数据请求，返回空对象
        if (msg.type === 'get_equipment_data') {
          resolve({
            type: 'equipment_data',
            data: {}
          });
          return;
        }
        
        resolve({
          type: type + '_failed',
          data: { message: error.message || '请求失败' }
        });
      };
      
      // 注册事件监听
      wsManager.on(type + '_success', handler);
      wsManager.on(type + '_failed', handler);
      wsManager.on(type + '_error', handler);
      wsManager.on('error', errorHandler);
      
              // 兼容打造请求
        if (type === 'crafting_action') {
          wsManager.on('get_craftable_success', handler);
          wsManager.on('get_craftable_failed', handler);
          wsManager.on('craft_success', handler);
          wsManager.on('craft_failed', handler);
        }
        
        // 兼容战斗开始请求
        if (type === 'start_battle_from_encounter') {
          wsManager.on('start_battle_from_encounter_success', handler);
          wsManager.on('start_battle_from_encounter_failed', handler);
        }
        
        // 兼容逃跑请求
        if (type === 'escape_battle') {
          wsManager.on('escape_battle_result', handler);
        }
      
      // 兼容player_data事件
        if (type === 'get_player_data') {
          wsManager.on('player_data', handler);
        }
        
        // 兼容get_player_data_success等事件
        if (type === 'get_player_data') {
          wsManager.on('get_player_data_success', handler);
          wsManager.on('get_player_data_failed', handler);
          wsManager.on('get_player_data_error', handler);
        }
      
      // 兼容后端直接返回type为market_list等的事件
      if (type === 'market_action') {
        wsManager.on('market_list', handler);
        wsManager.on('success', handler);
        wsManager.on('error', handler);
      }
      
      // 兼容地图NPC请求
      if (type === 'get_map_npcs') {
        wsManager.on('map_npcs', handler);
      }

      // 兼容NPC功能请求
      if (type === 'npc_function') {
        wsManager.on('npc_talk', handler);
        wsManager.on('shop_items', handler);
        wsManager.on('buy_success', handler);
        wsManager.on('sell_success', handler);
        wsManager.on('transport_destinations', handler);
        wsManager.on('transport_success', handler);
        wsManager.on('heal_success', handler);
        wsManager.on('info_services', handler);
        wsManager.on('info_success', handler);
        wsManager.on('error', handler);
      }
      
      // 兼容天赋加成请求
      if (type === 'get_bonus_summary') {
        wsManager.on('bonus_summary', handler);
      }
      
      // 兼容背包数据请求
      if (type === 'get_inventory_data') {
        wsManager.on('inventory_data', handler);
      }

      // 兼容采集动作请求
      if (type === 'gather_action') {
        wsManager.on('gathering_result', handler);
      }
      
      // 兼容武功数据请求
      if (type === 'get_skills_data') {
        wsManager.on('skills_data', handler);
      }

      // 兼容武功配置请求
      if (type === 'get_martial_configs') {
        wsManager.on('martial_configs', handler);
      }
      
      // 兼容装备请求
      if (type === 'equip_item') {
        wsManager.on('equip_success', handler);
        wsManager.on('equip_failed', handler);
      }

      // 兼容排行榜请求
      if (type === 'get_ranking') {
        wsManager.on('success', handler);
        wsManager.on('error', handler);
      }

      // 兼容兑换码请求
      if (type === 'redeem_code') {
        wsManager.on('success', handler);
        wsManager.on('error', handler);
      }


      
      // 确保WebSocket已连接
      if (!wsManager.isConnected) {
        wsManager.connect().then(() => {
          wsManager.sendMessage(type, data);
        }).catch(err => {
          resolve({
            type: type + '_failed',
            data: { message: '连接服务器失败，请检查网络' }
          });
        });
      } else {
        wsManager.sendMessage(type, data);
      }
    });
  }
}