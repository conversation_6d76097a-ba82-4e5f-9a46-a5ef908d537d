#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏管理工具
用于管理玩家数据库，包括增加物品、调整数值等
"""

import asyncio
import aiosqlite
import json
import os
from typing import Dict, List, Any, Optional

class GameAdminTool:
    def __init__(self, db_path: str = "game.db"):
        self.db_path = db_path
        self.items_data = {}
        self.load_items_data()
    
    def load_items_data(self):
        """加载所有物品数据"""
        backend_dir = os.path.dirname(__file__)
        item_files = [
            'items_weapon.json', 'items_armor.json', 'items_helmet.json',
            'items_accessory.json', 'items_consumable.json', 'items_book.json',
            'items_gather_tool.json', 'items_gather_item.json', 'items_sect_token.json'
        ]
        
        for file_name in item_files:
            file_path = os.path.join(backend_dir, file_name)
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        items = json.load(f)
                        for item in items:
                            self.items_data[item['id']] = item
                    print(f"✅ 加载 {file_name}: {len(items)} 个物品")
                except Exception as e:
                    print(f"❌ 加载 {file_name} 失败: {e}")
        
        print(f"📦 总共加载 {len(self.items_data)} 个物品")
    
    async def get_player_list(self) -> List[Dict]:
        """获取所有玩家列表"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT u.id, u.username, u.character_name, u.created_at, u.last_login,
                       COALESCE(json_extract(p.data, '$.level'), 1) as level,
                       COALESCE(json_extract(p.data, '$.money'), 1000) as money
                FROM users u
                LEFT JOIN players p ON u.id = p.user_id
                WHERE u.status = 'active'
                ORDER BY u.id
            """)
            rows = await cursor.fetchall()

            players = []
            for row in rows:
                players.append({
                    'user_id': row[0],
                    'username': row[1],
                    'character_name': row[2],
                    'created_at': row[3],
                    'last_login': row[4],
                    'level': row[5],
                    'money': row[6]
                })
            return players
    
    async def get_player_data(self, user_id: int) -> Optional[Dict]:
        """获取玩家详细数据"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                SELECT u.id, u.username, u.character_name, u.created_at, u.last_login,
                       p.data
                FROM users u
                LEFT JOIN players p ON u.id = p.user_id
                WHERE u.id = ? AND u.status = 'active'
            """, (user_id,))
            row = await cursor.fetchone()

            if not row:
                return None

            # 解析player_data JSON
            player_data = {}
            if row[5]:
                try:
                    player_data = json.loads(row[5])
                except:
                    pass

            # 从player_data中提取属性，如果没有则使用默认值
            return {
                'user_id': row[0],
                'username': row[1],
                'character_name': row[2],
                'created_at': row[3],
                'last_login': row[4],
                'level': player_data.get('level', 1),
                'money': player_data.get('money', 1000),
                'hp': player_data.get('hp', 100),
                'max_hp': player_data.get('max_hp', 100),
                'mp': player_data.get('mp', 50),
                'max_mp': player_data.get('max_mp', 50),
                'energy': player_data.get('energy', 100),
                'max_energy': player_data.get('max_energy', 100),
                'attack': player_data.get('attack', 10),
                'defense': player_data.get('defense', 5),
                'player_data': player_data
            }
    
    async def add_item_to_player(self, user_id: int, item_id: str, quantity: int = 1) -> bool:
        """给玩家添加物品"""
        if item_id not in self.items_data:
            print(f"❌ 物品 {item_id} 不存在")
            return False

        item_info = self.items_data[item_id]
        player_data = await self.get_player_data(user_id)

        if not player_data:
            print(f"❌ 玩家 {user_id} 不存在")
            return False

        # 获取背包数据（列表格式）
        inventory = player_data['player_data'].get('inventory', [])

        # 检查是否已有相同物品且可堆叠
        stackable = item_info.get('stackable', 1) == 1
        max_stack = item_info.get('max_stack', 999)

        existing_item = None
        if stackable:
            for item in inventory:
                if item.get('id') == item_id:
                    existing_item = item
                    break

        if existing_item:
            # 堆叠到现有物品
            current_quantity = existing_item.get('quantity', 1)
            max_add = min(quantity, max_stack - current_quantity)
            if max_add > 0:
                existing_item['quantity'] = current_quantity + max_add
                print(f"✅ 给玩家 {player_data['character_name']} 堆叠了 {max_add} 个 {item_info['name']} (总数: {existing_item['quantity']})")
            else:
                print(f"❌ 物品 {item_info['name']} 已达到堆叠上限")
                return False
        else:
            # 添加新物品
            import uuid
            new_item = {
                'id': item_id,
                'name': item_info['name'],
                'type': item_info['type'],
                'quality': item_info.get('quality', 'common'),
                'description': item_info.get('description', ''),
                'icon': item_info.get('icon', ''),
                'price': item_info.get('price', 0),
                'quantity': quantity,
                'stackable': stackable,
                'max_stack': max_stack,
                'attack': item_info.get('attack', 0),
                'defense': item_info.get('defense', 0),
                'hp': item_info.get('hp', 0),
                'mp': item_info.get('mp', 0),
                'energy': item_info.get('energy', 0),
                'energy_regen': item_info.get('energy_regen', 0.0),
                'unique_id': str(uuid.uuid4()),
                'sellable': item_info.get('sellable', 1) == 1
            }
            inventory.append(new_item)
            print(f"✅ 给玩家 {player_data['character_name']} 添加了 {quantity} 个 {item_info['name']}")

        # 更新数据库
        player_data['player_data']['inventory'] = inventory
        await self.update_player_data(user_id, player_data['player_data'])

        return True
    
    async def update_player_stats(self, user_id: int, stats: Dict[str, Any]) -> bool:
        """更新玩家基础属性"""
        player_data = await self.get_player_data(user_id)
        if not player_data:
            print(f"❌ 玩家 {user_id} 不存在")
            return False

        # 更新player_data中的属性
        current_data = player_data['player_data'].copy()

        for field, value in stats.items():
            if field in ['level', 'money', 'hp', 'max_hp', 'mp', 'max_mp',
                        'energy', 'max_energy', 'attack', 'defense']:
                current_data[field] = value

        # 更新数据库
        await self.update_player_data(user_id, current_data)

        print(f"✅ 更新玩家 {player_data['character_name']} 的属性: {stats}")
        return True
    
    async def update_player_data(self, user_id: int, player_data: Dict) -> bool:
        """更新玩家的player_data字段"""
        async with aiosqlite.connect(self.db_path) as db:
            # 检查players表中是否已有记录
            cursor = await db.execute("SELECT id FROM players WHERE user_id = ?", (user_id,))
            existing = await cursor.fetchone()

            data_json = json.dumps(player_data, ensure_ascii=False)

            if existing:
                # 更新现有记录
                await db.execute("""
                    UPDATE players SET data = ? WHERE user_id = ?
                """, (data_json, user_id))
            else:
                # 创建新记录
                from datetime import datetime
                created_at = datetime.now().isoformat()
                await db.execute("""
                    INSERT INTO players (user_id, data, created_at) VALUES (?, ?, ?)
                """, (user_id, data_json, created_at))

            await db.commit()
        return True
    
    async def add_martial_skill(self, user_id: int, skill_name: str, level: int = 1) -> bool:
        """给玩家添加武功"""
        player_data = await self.get_player_data(user_id)
        if not player_data:
            print(f"❌ 玩家 {user_id} 不存在")
            return False

        martial_skills = player_data['player_data'].get('martial_skills', [])

        # 兼容处理：支持字典和列表两种格式
        if isinstance(martial_skills, dict):
            # 字典格式
            martial_skills[skill_name] = {
                'name': skill_name,
                'level': level,
                'exp': 0,
                'equipped': True,  # 管理员添加的武功自动装备
                'unlocked': True
            }
        elif isinstance(martial_skills, list):
            # 列表格式：先检查是否已存在
            existing_skill = None
            for skill in martial_skills:
                if skill.get('name') == skill_name:
                    existing_skill = skill
                    break

            if existing_skill:
                # 更新现有武功
                existing_skill['level'] = level
                existing_skill['unlocked'] = True
                existing_skill['equipped'] = True  # 管理员添加的武功自动装备
            else:
                # 添加新武功
                martial_skills.append({
                    'name': skill_name,
                    'level': level,
                    'exp': 0,
                    'equipped': True,  # 管理员添加的武功自动装备
                    'unlocked': True
                })
        else:
            # 如果不是字典也不是列表，初始化为列表
            martial_skills = [{
                'name': skill_name,
                'level': level,
                'exp': 0,
                'equipped': True,  # 管理员添加的武功自动装备
                'unlocked': True
            }]

        player_data['player_data']['martial_skills'] = martial_skills
        await self.update_player_data(user_id, player_data['player_data'])

        # 卸下同类型的其他武功
        from enhanced_martial_system import get_martial_category
        skill_category = get_martial_category(skill_name)
        print(f"🔧 武功 {skill_name} 的类型: {skill_category}")

        # 卸下同类型的其他已装备武功
        if isinstance(martial_skills, list):
            for skill_entry in martial_skills:
                other_name = skill_entry.get('name')
                if other_name != skill_name and skill_entry.get('equipped', False):
                    other_category = get_martial_category(other_name)
                    if other_category == skill_category:
                        skill_entry['equipped'] = False
                        skill_entry['装备'] = False
                        print(f"🔧 卸下同类型武功: {other_name}")
        elif isinstance(martial_skills, dict):
            for other_name, skill_info in martial_skills.items():
                if other_name != skill_name and skill_info.get('equipped', False):
                    other_category = get_martial_category(other_name)
                    if other_category == skill_category:
                        skill_info['equipped'] = False
                        skill_info['装备'] = False
                        print(f"🔧 卸下同类型武功: {other_name}")

        # 重新计算玩家属性增益
        if hasattr(self.server, 'bonus_system'):
            updated_player = self.server.bonus_system.apply_bonuses_to_player(player_data['player_data'])
            self.server.player_data[str(user_id)] = updated_player
            await self.server.save_player_data(str(user_id), updated_player)
            print(f"✅ 已重新计算玩家属性增益")

        print(f"✅ 给玩家 {player_data['character_name']} 添加武功 {skill_name} (等级{level})")
        return True
    
    async def set_gather_skill(self, user_id: int, skill_type: str, level: int, exp: int = 0) -> bool:
        """设置玩家采集技能"""
        valid_types = ['herbalism', 'mining', 'logging', 'skinning']
        if skill_type not in valid_types:
            print(f"❌ 无效的采集技能类型: {skill_type}")
            return False
        
        player_data = await self.get_player_data(user_id)
        if not player_data:
            print(f"❌ 玩家 {user_id} 不存在")
            return False
        
        gather_skills = player_data['player_data'].get('gather_skills', {})
        gather_skills[skill_type] = {
            'level': level,
            'exp': exp
        }
        
        player_data['player_data']['gather_skills'] = gather_skills
        await self.update_player_data(user_id, player_data['player_data'])
        
        skill_names = {
            'herbalism': '采药',
            'mining': '挖矿', 
            'logging': '伐木',
            'skinning': '剥皮'
        }
        
        print(f"✅ 设置玩家 {player_data['character_name']} 的{skill_names[skill_type]}技能为等级{level}")
        return True
    
    def search_items(self, keyword: str) -> List[Dict]:
        """搜索物品"""
        results = []
        for item_id, item_data in self.items_data.items():
            if keyword.lower() in item_data['name'].lower() or keyword.lower() in item_id.lower():
                results.append({
                    'id': item_id,
                    'name': item_data['name'],
                    'type': item_data['type'],
                    'quality': item_data.get('quality', 'common'),
                    'description': item_data.get('description', '')
                })
        return results
    
    def print_help(self):
        """打印帮助信息"""
        help_text = """
🎮 游戏管理工具帮助

📋 基本命令:
  list_players          - 显示所有玩家列表
  player_info <user_id> - 显示玩家详细信息
  search_item <关键词>   - 搜索物品

🎁 物品管理:
  add_item <user_id> <item_id> [数量]    - 给玩家添加物品
  show_inventory <user_id>               - 显示玩家背包
  clear_inventory <user_id>              - 清空玩家背包

📊 属性管理:
  set_level <user_id> <等级>             - 设置玩家等级
  set_money <user_id> <金钱>             - 设置玩家金钱
  set_hp <user_id> <血量> [最大血量]      - 设置玩家血量
  set_mp <user_id> <内力> [最大内力]      - 设置玩家内力
  set_energy <user_id> <精力> [最大精力]  - 设置玩家精力

⚔️ 武功管理:
  add_martial <user_id> <武功名> [等级]   - 给玩家添加武功

🏛️ 门派管理:
  add_sect_token <user_id> <门派名称>    - 给玩家添加门派令牌
  可用门派: 少林, 武当, 峨眉, 华山

🔨 采集技能:
  set_gather <user_id> <类型> <等级> [经验] - 设置采集技能
  类型: herbalism(采药), mining(挖矿), logging(伐木), skinning(剥皮)

💡 使用示例:
  add_item 1 sickle_1 5              - 给玩家1添加5个简易镰刀
  set_level 1 50                     - 设置玩家1等级为50
  set_money 1 10000                  - 设置玩家1金钱为10000
  add_martial 1 基本剑法 20           - 给玩家1添加20级基本剑法
  set_gather 1 herbalism 10 500      - 设置玩家1采药技能10级500经验

❓ 其他:
  help  - 显示此帮助
  exit  - 退出程序
        """
        print(help_text)

async def main():
    """主程序"""
    admin = GameAdminTool()
    
    print("🎮 游戏管理工具启动")
    print("输入 'help' 查看帮助，输入 'exit' 退出")
    print("-" * 50)
    
    while True:
        try:
            command = input("\n管理员> ").strip()
            
            if not command:
                continue
            
            parts = command.split()
            cmd = parts[0].lower()
            
            if cmd == 'exit':
                print("👋 再见！")
                break
            elif cmd == 'help':
                admin.print_help()
            elif cmd == 'list_players':
                players = await admin.get_player_list()
                print(f"\n📋 玩家列表 (共{len(players)}人):")
                print("ID\t用户名\t\t角色名\t\t等级\t金钱")
                print("-" * 60)
                for p in players:
                    print(f"{p['user_id']}\t{p['username'][:10]}\t\t{p['character_name'][:10]}\t\t{p['level']}\t{p['money']}")
            
            elif cmd == 'player_info':
                if len(parts) < 2:
                    print("❌ 用法: player_info <user_id>")
                    continue
                
                try:
                    user_id = int(parts[1])
                    player = await admin.get_player_data(user_id)
                    if player:
                        print(f"\n👤 玩家信息:")
                        print(f"ID: {player['user_id']}")
                        print(f"用户名: {player['username']}")
                        print(f"角色名: {player['character_name']}")
                        print(f"等级: {player['level']}")
                        print(f"金钱: {player['money']}")
                        print(f"血量: {player['hp']}/{player['max_hp']}")
                        print(f"内力: {player['mp']}/{player['max_mp']}")
                        print(f"精力: {player['energy']}/{player['max_energy']}")
                        print(f"攻击: {player['attack']}")
                        print(f"防御: {player['defense']}")
                        
                        # 显示背包物品数量
                        inventory = player['player_data'].get('inventory', {})
                        print(f"背包物品数量: {len(inventory)}")
                        
                        # 显示武功数量
                        martial_skills = player['player_data'].get('martial_skills', {})
                        print(f"武功数量: {len(martial_skills)}")
                    else:
                        print(f"❌ 玩家 {user_id} 不存在")
                except ValueError:
                    print("❌ 用户ID必须是数字")
            
            elif cmd == 'search_item':
                if len(parts) < 2:
                    print("❌ 用法: search_item <关键词>")
                    continue
                
                keyword = ' '.join(parts[1:])
                results = admin.search_items(keyword)
                
                if results:
                    print(f"\n🔍 搜索结果 (共{len(results)}个):")
                    print("ID\t\t名称\t\t类型\t品质")
                    print("-" * 60)
                    for item in results[:20]:  # 最多显示20个
                        print(f"{item['id'][:15]}\t{item['name'][:10]}\t{item['type']}\t{item['quality']}")
                else:
                    print(f"❌ 没有找到包含 '{keyword}' 的物品")
            
            elif cmd == 'add_item':
                if len(parts) < 3:
                    print("❌ 用法: add_item <user_id> <item_id> [数量]")
                    continue
                
                try:
                    user_id = int(parts[1])
                    item_id = parts[2]
                    quantity = int(parts[3]) if len(parts) > 3 else 1
                    
                    await admin.add_item_to_player(user_id, item_id, quantity)
                except ValueError:
                    print("❌ 用户ID和数量必须是数字")
            
            elif cmd == 'set_level':
                if len(parts) < 3:
                    print("❌ 用法: set_level <user_id> <等级>")
                    continue
                
                try:
                    user_id = int(parts[1])
                    level = int(parts[2])
                    await admin.update_player_stats(user_id, {'level': level})
                except ValueError:
                    print("❌ 用户ID和等级必须是数字")
            
            elif cmd == 'set_money':
                if len(parts) < 3:
                    print("❌ 用法: set_money <user_id> <金钱>")
                    continue
                
                try:
                    user_id = int(parts[1])
                    money = int(parts[2])
                    await admin.update_player_stats(user_id, {'money': money})
                except ValueError:
                    print("❌ 用户ID和金钱必须是数字")
            
            elif cmd == 'add_martial':
                if len(parts) < 3:
                    print("❌ 用法: add_martial <user_id> <武功名> [等级]")
                    continue

                try:
                    user_id = int(parts[1])
                    skill_name = parts[2]
                    level = int(parts[3]) if len(parts) > 3 else 1
                    await admin.add_martial_skill(user_id, skill_name, level)
                except ValueError:
                    print("❌ 用户ID和等级必须是数字")

            elif cmd == 'add_sect_token':
                if len(parts) < 3:
                    print("❌ 用法: add_sect_token <user_id> <门派名称>")
                    print("   可用门派: 少林, 武当, 峨眉, 华山")
                    continue

                try:
                    user_id = int(parts[1])
                    sect_name = parts[2]
                    # 门派名称到令牌ID的映射
                    sect_token_map = {
                        '少林': 'shaolin_token',
                        '武当': 'wudang_token',
                        '峨眉': 'emei_token',
                        '华山': 'huashan_token'
                    }
                    token_name = sect_token_map.get(sect_name)
                    if token_name:
                        await admin.add_item_to_player(user_id, token_name, 1)
                    else:
                        print(f"❌ 未知门派: {sect_name}，可用门派: {', '.join(sect_token_map.keys())}")
                except ValueError:
                    print("❌ 用户ID必须是数字")

            elif cmd == 'set_gather':
                if len(parts) < 4:
                    print("❌ 用法: set_gather <user_id> <类型> <等级> [经验]")
                    continue

                try:
                    user_id = int(parts[1])
                    skill_type = parts[2]
                    level = int(parts[3])
                    exp = int(parts[4]) if len(parts) > 4 else 0
                    await admin.set_gather_skill(user_id, skill_type, level, exp)
                except ValueError:
                    print("❌ 用户ID、等级和经验必须是数字")

            elif cmd == 'set_hp':
                if len(parts) < 3:
                    print("❌ 用法: set_hp <user_id> <血量> [最大血量]")
                    continue

                try:
                    user_id = int(parts[1])
                    hp = int(parts[2])
                    max_hp = int(parts[3]) if len(parts) > 3 else hp
                    await admin.update_player_stats(user_id, {'hp': hp, 'max_hp': max_hp})
                except ValueError:
                    print("❌ 用户ID和血量必须是数字")

            elif cmd == 'set_mp':
                if len(parts) < 3:
                    print("❌ 用法: set_mp <user_id> <内力> [最大内力]")
                    continue

                try:
                    user_id = int(parts[1])
                    mp = int(parts[2])
                    max_mp = int(parts[3]) if len(parts) > 3 else mp
                    await admin.update_player_stats(user_id, {'mp': mp, 'max_mp': max_mp})
                except ValueError:
                    print("❌ 用户ID和内力必须是数字")

            elif cmd == 'set_energy':
                if len(parts) < 3:
                    print("❌ 用法: set_energy <user_id> <精力> [最大精力]")
                    continue

                try:
                    user_id = int(parts[1])
                    energy = int(parts[2])
                    max_energy = int(parts[3]) if len(parts) > 3 else energy
                    await admin.update_player_stats(user_id, {'energy': energy, 'max_energy': max_energy})
                except ValueError:
                    print("❌ 用户ID和精力必须是数字")

            elif cmd == 'clear_inventory':
                if len(parts) < 2:
                    print("❌ 用法: clear_inventory <user_id>")
                    continue

                try:
                    user_id = int(parts[1])
                    player_data = await admin.get_player_data(user_id)
                    if player_data:
                        player_data['player_data']['inventory'] = []
                        await admin.update_player_data(user_id, player_data['player_data'])
                        print(f"✅ 清空玩家 {player_data['character_name']} 的背包")
                    else:
                        print(f"❌ 玩家 {user_id} 不存在")
                except ValueError:
                    print("❌ 用户ID必须是数字")

            elif cmd == 'show_inventory':
                if len(parts) < 2:
                    print("❌ 用法: show_inventory <user_id>")
                    continue

                try:
                    user_id = int(parts[1])
                    player_data = await admin.get_player_data(user_id)
                    if player_data:
                        inventory = player_data['player_data'].get('inventory', [])
                        if inventory:
                            print(f"\n🎒 {player_data['character_name']} 的背包 ({len(inventory)} 个物品):")
                            print("物品ID\t\t名称\t\t数量\t品质\t类型")
                            print("-" * 70)
                            for item in inventory:
                                item_id = item.get('id', '未知')
                                item_name = item.get('name', '未知物品')
                                quantity = item.get('quantity', 1)
                                quality = item.get('quality', 'common')
                                item_type = item.get('type', '未知')
                                print(f"{item_id[:15]}\t{item_name[:10]}\t{quantity}\t{quality}\t{item_type}")
                        else:
                            print(f"📦 {player_data['character_name']} 的背包是空的")
                    else:
                        print(f"❌ 玩家 {user_id} 不存在")
                except ValueError:
                    print("❌ 用户ID必须是数字")

            else:
                print(f"❌ 未知命令: {cmd}")
                print("输入 'help' 查看帮助")
        
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == '__main__':
    asyncio.run(main())
