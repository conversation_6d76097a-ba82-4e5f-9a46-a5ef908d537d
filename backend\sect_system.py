#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
门派系统模块
负责处理门派相关功能，包括拜师、门派管理、贡献度等
"""

import json
import logging
import aiosqlite
import asyncio
import os
from datetime import datetime
from typing import Dict, Optional, List
from websockets.server import WebSocketServerProtocol

logger = logging.getLogger(__name__)


class SectSystem:
    def __init__(self, db_path: str, game_server):
        self.db_path = db_path
        self.game_server = game_server
        
        # 加载门派配置
        self.sects_config = self.load_sects_config()

    def load_sects_config(self) -> dict:
        """加载门派配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config', 'sects.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载门派配置失败: {e}")
            return {"sects": {}}

    async def init_sect_tables(self):
        """初始化门派相关数据表"""
        async with aiosqlite.connect(self.db_path) as db:
            # 创建门派成员表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS sect_members (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    sect_id TEXT NOT NULL,
                    rank INTEGER DEFAULT 1,
                    contribution INTEGER DEFAULT 0,
                    join_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_daily_reward TIMESTAMP,
                    UNIQUE(user_id)
                )
            ''')
            
            # 创建门派任务表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS sect_quests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    sect_id TEXT NOT NULL,
                    quest_type TEXT NOT NULL,
                    quest_data TEXT,
                    completed BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建门派战争表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS sect_wars (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    attacker_sect_id TEXT NOT NULL,
                    defender_sect_id TEXT NOT NULL,
                    status TEXT DEFAULT 'declared',  -- declared, active, ended
                    declare_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    winner_sect_id TEXT,
                    attacker_score INTEGER DEFAULT 0,
                    defender_score INTEGER DEFAULT 0,
                    war_reason TEXT
                )
            ''')

            # 创建门派战争参与记录表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS sect_war_participants (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    war_id INTEGER NOT NULL,
                    user_id TEXT NOT NULL,
                    sect_id TEXT NOT NULL,
                    kills INTEGER DEFAULT 0,
                    deaths INTEGER DEFAULT 0,
                    contribution_gained INTEGER DEFAULT 0,
                    FOREIGN KEY (war_id) REFERENCES sect_wars (id)
                )
            ''')

            # 创建门派建设表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS sect_buildings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sect_id TEXT NOT NULL,
                    building_type TEXT NOT NULL,  -- main_hall, training_ground, library, etc.
                    building_level INTEGER DEFAULT 1,
                    upgrade_progress INTEGER DEFAULT 0,
                    last_upgrade_time TIMESTAMP,
                    UNIQUE(sect_id, building_type)
                )
            ''')

            # 创建门派资源表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS sect_resources (
                    sect_id TEXT PRIMARY KEY,
                    wood INTEGER DEFAULT 0,
                    stone INTEGER DEFAULT 0,
                    iron INTEGER DEFAULT 0,
                    gold INTEGER DEFAULT 0,
                    last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建门派任务进度表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS sect_quest_progress (
                    user_id TEXT,
                    quest_id TEXT,
                    status TEXT DEFAULT 'accepted',
                    progress INTEGER DEFAULT 0,
                    accepted_time TEXT,
                    completed_time TEXT,
                    PRIMARY KEY (user_id, quest_id)
                )
            ''')

            await db.commit()

    async def handle_sect_action(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理门派相关操作"""
        try:
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                logger.error(f"[handle_sect_action] 未认证的请求")
                return {'type': 'error', 'data': {'message': '未认证'}}

            action = data.get('action')
            logger.error(f"[handle_sect_action] 处理门派操作: user_id={user_id}, action={action}")

            if action == 'get_sect_info':
                result = await self.get_sect_info(user_id)
                logger.error(f"[handle_sect_action] get_sect_info 返回结果: {result}")
                return result
            elif action == 'join_sect':
                return await self.join_sect(data, user_id)
            elif action == 'leave_sect':
                return await self.leave_sect(user_id)
            elif action == 'get_available_sects':
                return await self.get_available_sects(user_id)
            elif action == 'claim_daily_reward':
                return await self.claim_daily_reward(user_id)
            elif action == 'get_sect_quests':
                return await self.get_sect_quests(user_id)
            elif action == 'accept_sect_quest':
                return await self.accept_sect_quest(data, user_id)
            elif action == 'complete_sect_quest':
                return await self.complete_sect_quest(data, user_id)
            elif action == 'submit_sect_quest':
                return await self.submit_sect_quest(data, user_id)
            elif action == 'get_sect_skills':
                return await self.get_sect_skills(user_id)
            elif action == 'learn_sect_skill':
                return await self.learn_sect_skill(data, user_id)
            elif action == 'get_sect_members':
                return await self.get_sect_members(user_id)
            elif action == 'get_sect_shop':
                return await self.get_sect_shop(user_id)
            elif action == 'get_sect_rankings':
                return await self.get_sect_rankings(data, user_id)
            elif action == 'declare_war':
                return await self.declare_war(data, user_id)
            elif action == 'get_sect_wars':
                return await self.get_sect_wars(user_id)
            elif action == 'get_sect_buildings':
                return await self.get_sect_buildings(user_id)
            elif action == 'upgrade_building':
                return await self.upgrade_building(data, user_id)
            else:
                return {'type': 'error', 'data': {'message': '未知操作'}}

        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            logger.error(f"处理门派操作失败: {e}")
            logger.error(f"异常详情: {tb}")
            return {'type': 'error', 'data': {'message': f'操作失败: {str(e)}'}}

    async def get_sect_info(self, user_id: str) -> dict:
        """获取玩家门派信息"""
        try:
            logger.error(f"[get_sect_info] 查询玩家门派信息: user_id={user_id}")
            await self.init_sect_tables()

            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id, rank, contribution, join_time, last_daily_reward
                    FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    row = await cursor.fetchone()
                    logger.error(f"[get_sect_info] 数据库查询结果: row={row}")

                if not row:
                    logger.error(f"[get_sect_info] 玩家 {user_id} 未加入任何门派")
                    return {
                        'type': 'sect_action_success',
                        'data': {
                            'action': 'get_sect_info',
                            'has_sect': False,
                            'message': '尚未加入任何门派'
                        }
                    }
                
                sect_id, rank, contribution, join_time, last_daily_reward = row
                logger.error(f"[get_sect_info] 解析数据: sect_id={sect_id}, rank={rank}, contribution={contribution}")
                sect_config = self.sects_config['sects'].get(sect_id, {})
                logger.error(f"[get_sect_info] 门派配置: sect_config={sect_config}")

                # 获取当前等级信息
                current_rank_info = None
                next_rank_info = None

                for rank_info in sect_config.get('ranks', []):
                    if rank_info['rank'] == rank:
                        current_rank_info = rank_info
                    elif rank_info['rank'] == rank + 1:
                        next_rank_info = rank_info

                result = {
                    'type': 'sect_action_success',
                    'data': {
                        'action': 'get_sect_info',
                        'has_sect': True,
                        'sect_id': sect_id,
                        'sect_name': sect_config.get('name', '未知门派'),
                        'rank': rank,
                        'rank_name': current_rank_info.get('name', '弟子') if current_rank_info else '弟子',
                        'contribution': contribution,
                        'join_time': join_time,
                        'can_claim_daily': self.can_claim_daily_reward(last_daily_reward),
                        'next_rank': next_rank_info,
                        'sect_config': sect_config
                    }
                }
                logger.error(f"[get_sect_info] 返回结果: {result}")
                return result

        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            logger.error(f"获取门派信息失败: {e}")
            logger.error(f"异常详情: {tb}")
            return {'type': 'error', 'data': {'message': f'获取门派信息失败: {str(e)}'}}

    async def get_available_sects(self, user_id: str) -> dict:
        """获取可加入的门派列表"""
        try:
            player = self.game_server.player_data.get(str(user_id))
            if not player:
                return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

            logger.info(f"获取门派列表，玩家数据: {player}")
            logger.info(f"门派配置: {self.sects_config}")

            available_sects = []

            for sect_id, sect_config in self.sects_config['sects'].items():
                # 检查加入条件
                requirements = sect_config.get('requirements', {})
                can_join = True
                reasons = []

                # 检查性别要求
                if requirements.get('gender') and requirements['gender'] != player.get('gender'):
                    can_join = False
                    reasons.append(f"仅限{requirements['gender']}性")

                # 检查天赋属性要求
                required_attributes = requirements.get('attributes', {})
                for attr_name, required_value in required_attributes.items():
                    player_value = player.get(attr_name, 0)
                    if player_value < required_value:
                        can_join = False
                        reasons.append(f"{attr_name}需要{required_value}点(当前{player_value}点)")

                # 检查物品要求
                required_items = requirements.get('items', [])
                if required_items:
                    player_inventory = player.get('inventory', [])
                    for required_item in required_items:
                        has_item = any(item.get('name') == required_item for item in player_inventory)
                        if not has_item:
                            can_join = False
                            reasons.append(f"需要物品: {required_item}")
                
                available_sects.append({
                    'sect_id': sect_id,
                    'sect_name': sect_config.get('name'),
                    'description': sect_config.get('description'),
                    'master_npc': sect_config.get('master_npc'),
                    'location': sect_config.get('location'),
                    'can_join': can_join,
                    'reasons': reasons,
                    'requirements': requirements
                })
            
            return {
                'type': 'sect_action_success',
                'data': {
                    'action': 'get_available_sects',
                    'sects': available_sects
                }
            }

        except Exception as e:
            logger.error(f"获取可用门派失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取可用门派失败: {str(e)}'}}

    async def join_sect(self, data: dict, user_id: str) -> dict:
        """加入门派"""
        try:
            sect_id = data.get('sect_id')
            if not sect_id:
                return {'type': 'error', 'data': {'message': '请选择门派'}}

            sect_config = self.sects_config['sects'].get(sect_id)
            if not sect_config:
                return {'type': 'error', 'data': {'message': '门派不存在'}}

            player = self.game_server.player_data.get(str(user_id))
            if not player:
                return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

            await self.init_sect_tables()

            # 检查是否已经加入门派
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    existing = await cursor.fetchone()
                
                if existing:
                    return {'type': 'error', 'data': {'message': '你已经加入了门派，请先退出当前门派'}}

                # 检查加入条件
                requirements = sect_config.get('requirements', {})

                # 检查性别要求
                if requirements.get('gender') and requirements['gender'] != player.get('gender'):
                    return {'type': 'error', 'data': {'message': f"性别不符，该门派仅限{requirements['gender']}性"}}

                # 检查天赋属性要求
                required_attributes = requirements.get('attributes', {})
                for attr_name, required_value in required_attributes.items():
                    player_value = player.get(attr_name, 0)
                    if player_value < required_value:
                        return {'type': 'error', 'data': {'message': f"{attr_name}不足，需要{required_value}点(当前{player_value}点)"}}

                # 检查物品要求
                required_items = requirements.get('items', [])
                if required_items:
                    player_inventory = player.get('inventory', [])
                    for required_item in required_items:
                        has_item = any(item.get('name') == required_item for item in player_inventory)
                        if not has_item:
                            return {'type': 'error', 'data': {'message': f"缺少必需物品: {required_item}"}}

                # 加入门派
                await db.execute('''
                    INSERT INTO sect_members (user_id, sect_id, rank, contribution)
                    VALUES (?, ?, 1, 0)
                ''', (user_id, sect_id))
                await db.commit()

                logger.info(f"玩家 {player.get('character_name', user_id)} 加入门派 {sect_config['name']}")

                return {
                    'type': 'join_sect_success',
                    'data': {
                        'message': f'成功加入{sect_config["name"]}！',
                        'sect_name': sect_config['name']
                    }
                }

        except Exception as e:
            logger.error(f"加入门派失败: {e}")
            return {'type': 'error', 'data': {'message': f'加入门派失败: {str(e)}'}}

    async def leave_sect(self, user_id: str) -> dict:
        """退出门派"""
        try:
            await self.init_sect_tables()

            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    row = await cursor.fetchone()
                
                if not row:
                    return {'type': 'error', 'data': {'message': '你尚未加入任何门派'}}

                sect_id = row[0]
                sect_config = self.sects_config['sects'].get(sect_id, {})

                # 删除门派成员记录
                await db.execute('DELETE FROM sect_members WHERE user_id = ?', (user_id,))
                await db.commit()

                logger.info(f"玩家 {user_id} 退出门派 {sect_config.get('name', sect_id)}")

                return {
                    'type': 'leave_sect_success',
                    'data': {
                        'message': f'已退出{sect_config.get("name", "门派")}'
                    }
                }

        except Exception as e:
            logger.error(f"退出门派失败: {e}")
            return {'type': 'error', 'data': {'message': f'退出门派失败: {str(e)}'}}

    def can_claim_daily_reward(self, last_daily_reward: str) -> bool:
        """检查是否可以领取每日奖励"""
        try:
            logger.error(f"[can_claim_daily_reward] 检查每日奖励: last_daily_reward={last_daily_reward}")
            if not last_daily_reward:
                logger.error(f"[can_claim_daily_reward] 没有上次领取记录，可以领取")
                return True

            from datetime import datetime
            last_time = datetime.fromisoformat(last_daily_reward.replace('Z', '+00:00'))
            now = datetime.now()
            days_diff = (now - last_time).days
            can_claim = days_diff >= 1
            logger.error(f"[can_claim_daily_reward] 上次领取: {last_time}, 现在: {now}, 相差天数: {days_diff}, 可以领取: {can_claim}")
            return can_claim
        except Exception as e:
            logger.error(f"[can_claim_daily_reward] 检查每日奖励异常: {e}")
            return True

    async def claim_daily_reward(self, user_id: str) -> dict:
        """领取每日奖励"""
        try:
            await self.init_sect_tables()

            # 检查玩家门派信息
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id, rank, last_daily_reward FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    row = await cursor.fetchone()

                if not row:
                    return {'type': 'error', 'data': {'message': '你尚未加入任何门派'}}

                sect_id, rank, last_daily_reward = row

                if not self.can_claim_daily_reward(last_daily_reward):
                    return {'type': 'error', 'data': {'message': '今日已领取过奖励'}}

                sect_config = self.sects_config['sects'].get(sect_id, {})
                daily_rewards = sect_config.get('benefits', {}).get('daily_rewards', {})

                # 使用奖励系统发放奖励
                player = self.game_server.player_data.get(str(user_id))
                if player:
                    # 构造奖励数据
                    reward_data = {}
                    if daily_rewards.get('silver', 0) > 0:
                        reward_data['银两'] = daily_rewards['silver']
                    if daily_rewards.get('exp', 0) > 0:
                        reward_data['历练值'] = daily_rewards['exp']

                    # 使用现成的奖励系统
                    from reward_system import add_reward_to_player
                    add_reward_to_player(player, reward_data, user_id, self.game_server)

                    # 保存玩家数据
                    await self.game_server.save_player_data(str(user_id), player)

                    # 推送玩家数据更新
                    await self.game_server.notify_player_data_update(user_id, player)

                # 更新领取时间（简单的单次操作，避免锁定）
                current_time = datetime.now().isoformat()
                async with aiosqlite.connect(self.db_path) as db:
                    await db.execute('''
                        UPDATE sect_members SET last_daily_reward = ? WHERE user_id = ?
                    ''', (current_time, user_id))
                    await db.commit()

                logger.error(f"[claim_daily_reward] 成功领取每日奖励: {reward_data}")

                return {
                    'type': 'sect_action_success',
                    'data': {
                        'action': 'claim_daily_reward',
                        'message': '领取每日奖励成功！',
                        'rewards': daily_rewards
                    }
                }

        except Exception as e:
            logger.error(f"领取每日奖励失败: {e}")
            return {'type': 'error', 'data': {'message': f'领取奖励失败: {str(e)}'}}

    async def get_sect_quests(self, user_id: str) -> dict:
        """获取门派任务"""
        try:
            await self.init_sect_tables()

            # 检查玩家是否加入门派
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id, rank, contribution FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    member_info = await cursor.fetchone()

                if not member_info:
                    return {'type': 'error', 'data': {'message': '你还没有加入门派'}}

                sect_id, rank, contribution = member_info
                sect_config = self.sects_config['sects'].get(sect_id, {})

                # 从配置文件获取门派任务
                quest_templates = sect_config.get('benefits', {}).get('sect_quests', [])

                # 获取玩家已接受的任务
                async with db.execute('''
                    SELECT quest_type, quest_data, completed, created_at
                    FROM sect_quests
                    WHERE user_id = ? AND sect_id = ?
                    ORDER BY created_at DESC
                ''', (user_id, sect_id)) as cursor:
                    player_quests = await cursor.fetchall()

                # 构建任务列表
                available_quests = []
                for template in quest_templates:
                    # 检查等级要求
                    if rank < template['rank_requirement']:
                        continue

                    # 检查是否已有相同类型的未完成任务
                    has_active = False
                    for pq in player_quests:
                        quest_type, quest_data, completed, created_at = pq
                        if quest_type == template['id'] and not completed:
                            # 检查任务是否过期（每日任务24小时，每周任务7天）
                            from datetime import datetime, timedelta
                            created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                            now = datetime.now()

                            if template['type'] == 'daily' and (now - created_time).days >= 1:
                                # 每日任务过期，可以重新接取
                                continue
                            elif template['type'] == 'weekly' and (now - created_time).days >= 7:
                                # 每周任务过期，可以重新接取
                                continue
                            else:
                                has_active = True
                                break

                    if not has_active:
                        available_quests.append({
                            **template,
                            'status': 'available',
                            'progress': 0,
                            'max_progress': len(template.get('required_items', []))
                        })

                # 添加进行中的任务
                for pq in player_quests:
                    quest_type, quest_data, completed, created_at = pq
                    if not completed:
                        template = next((t for t in quest_templates if t['id'] == quest_type), None)
                        if template:
                            quest_progress = json.loads(quest_data) if quest_data else {}
                            progress = quest_progress.get('progress', 0)
                            max_progress = list(template['requirements'].values())[0] if template['requirements'] else 1

                            available_quests.append({
                                **template,
                                'status': 'in_progress',
                                'progress': progress,
                                'max_progress': max_progress,
                                'created_at': created_at
                            })

                return {
                    'type': 'sect_action_success',
                    'data': {
                        'action': 'get_sect_quests',
                        'sect_name': sect_config.get('name', '未知门派'),
                        'quests': available_quests
                    }
                }

        except Exception as e:
            logger.error(f"获取门派任务失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取门派任务失败: {str(e)}'}}

    async def accept_sect_quest(self, data: dict, user_id: str) -> dict:
        """接受门派任务"""
        try:
            quest_id = data.get('quest_id')
            if not quest_id:
                return {'type': 'error', 'data': {'message': '请指定任务ID'}}

            await self.init_sect_tables()

            # 检查玩家是否已经接受了这个任务
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT status FROM sect_quest_progress WHERE user_id = ? AND quest_id = ?
                ''', (user_id, quest_id)) as cursor:
                    existing = await cursor.fetchone()

                if existing:
                    return {'type': 'error', 'data': {'message': '你已经接受了这个任务'}}

                # 记录任务接受
                current_time = datetime.now().isoformat()
                await db.execute('''
                    INSERT INTO sect_quest_progress (user_id, quest_id, status, progress, accepted_time)
                    VALUES (?, ?, 'accepted', 0, ?)
                ''', (user_id, quest_id, current_time))
                await db.commit()

            return {
                'type': 'sect_action_success',
                'data': {
                    'action': 'accept_sect_quest',
                    'message': '接受任务成功！',
                    'quest_id': quest_id
                }
            }

        except Exception as e:
            logger.error(f"接受门派任务失败: {e}")
            return {'type': 'error', 'data': {'message': f'接受任务失败: {str(e)}'}}

    async def complete_sect_quest(self, data: dict, user_id: str) -> dict:
        """完成门派任务"""
        try:
            quest_id = data.get('quest_id')
            if not quest_id:
                return {'type': 'error', 'data': {'message': '请指定任务ID'}}

            await self.init_sect_tables()

            # 检查任务状态
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT status, progress FROM sect_quest_progress WHERE user_id = ? AND quest_id = ?
                ''', (user_id, quest_id)) as cursor:
                    quest_progress = await cursor.fetchone()

                if not quest_progress:
                    return {'type': 'error', 'data': {'message': '你还没有接受这个任务'}}

                status, progress = quest_progress
                if status == 'completed':
                    return {'type': 'error', 'data': {'message': '任务已经完成'}}

                # 检查任务完成条件（这里简化处理，实际应该根据任务类型检查）
                # TODO: 根据任务类型检查完成条件

                # 标记任务完成
                current_time = datetime.now().isoformat()
                await db.execute('''
                    UPDATE sect_quest_progress
                    SET status = 'completed', completed_time = ?
                    WHERE user_id = ? AND quest_id = ?
                ''', (current_time, user_id, quest_id))

                # 增加贡献度
                await db.execute('''
                    UPDATE sect_members SET contribution = contribution + 10
                    WHERE user_id = ?
                ''', (user_id,))

                await db.commit()

            # 发放任务奖励
            player = self.game_server.player_data.get(str(user_id))
            if player:
                reward_data = {'银两': 20, '历练值': 50}
                from reward_system import add_reward_to_player
                add_reward_to_player(player, reward_data, user_id, self.game_server)
                await self.game_server.save_player_data(str(user_id), player)
                await self.game_server.notify_player_data_update(user_id, player)

            return {
                'type': 'sect_action_success',
                'data': {
                    'action': 'complete_sect_quest',
                    'message': '任务完成！获得奖励：银两 +20，历练 +50，贡献 +10',
                    'quest_id': quest_id
                }
            }

        except Exception as e:
            logger.error(f"完成门派任务失败: {e}")
            return {'type': 'error', 'data': {'message': f'完成任务失败: {str(e)}'}}

    async def submit_sect_quest(self, data: dict, user_id: str) -> dict:
        """提交收集类门派任务"""
        try:
            quest_id = data.get('quest_id')
            if not quest_id:
                return {'type': 'error', 'data': {'message': '请指定任务ID'}}

            await self.init_sect_tables()

            # 获取门派信息和任务配置
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id, rank FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    row = await cursor.fetchone()

                if not row:
                    return {'type': 'error', 'data': {'message': '你尚未加入任何门派'}}

                sect_id, rank = row
                sect_config = self.sects_config['sects'].get(sect_id, {})

                # 查找任务配置
                quest_templates = sect_config.get('benefits', {}).get('sect_quests', [])
                quest_config = next((q for q in quest_templates if q['id'] == quest_id), None)

                if not quest_config:
                    return {'type': 'error', 'data': {'message': '任务不存在'}}

                # 检查任务状态
                async with db.execute('''
                    SELECT status, progress FROM sect_quest_progress WHERE user_id = ? AND quest_id = ?
                ''', (user_id, quest_id)) as cursor:
                    quest_progress = await cursor.fetchone()

                if not quest_progress:
                    return {'type': 'error', 'data': {'message': '你还没有接受这个任务'}}

                status, progress = quest_progress
                if status == 'completed':
                    return {'type': 'error', 'data': {'message': '任务已经完成'}}

                # 检查玩家背包中的物品
                player = self.game_server.player_data.get(str(user_id))
                if not player:
                    return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

                inventory = player.get('inventory', [])
                required_items = quest_config.get('required_items', [])

                # 检查是否有足够的物品
                missing_items = []
                for required_item in required_items:
                    item_id = required_item['item_id']
                    required_quantity = required_item['quantity']
                    item_name = required_item['name']

                    # 在背包中查找物品
                    found_quantity = 0
                    for inv_item in inventory:
                        if inv_item.get('id') == item_id:
                            found_quantity += inv_item.get('quantity', 0)

                    if found_quantity < required_quantity:
                        missing_items.append(f"{item_name} {found_quantity}/{required_quantity}")

                if missing_items:
                    return {'type': 'error', 'data': {'message': f'物品不足：{", ".join(missing_items)}'}}

                # 扣除物品
                for required_item in required_items:
                    item_id = required_item['item_id']
                    required_quantity = required_item['quantity']

                    remaining_quantity = required_quantity
                    for i, inv_item in enumerate(inventory):
                        if inv_item.get('id') == item_id and remaining_quantity > 0:
                            available_quantity = inv_item.get('quantity', 0)
                            if available_quantity <= remaining_quantity:
                                # 完全消耗这个物品
                                remaining_quantity -= available_quantity
                                inventory.pop(i)
                            else:
                                # 部分消耗
                                inv_item['quantity'] = available_quantity - remaining_quantity
                                remaining_quantity = 0

                            if remaining_quantity == 0:
                                break

                # 标记任务完成
                current_time = datetime.now().isoformat()
                await db.execute('''
                    UPDATE sect_quest_progress
                    SET status = 'completed', completed_time = ?
                    WHERE user_id = ? AND quest_id = ?
                ''', (current_time, user_id, quest_id))

                # 增加贡献度
                rewards = quest_config.get('rewards', {})
                contribution_reward = rewards.get('contribution', 0)
                await db.execute('''
                    UPDATE sect_members SET contribution = contribution + ?
                    WHERE user_id = ?
                ''', (contribution_reward, user_id))

                await db.commit()

                # 发放任务奖励
                reward_data = {}
                if rewards.get('silver', 0) > 0:
                    reward_data['银两'] = rewards['silver']
                if rewards.get('exp', 0) > 0:
                    reward_data['历练值'] = rewards['exp']

                if reward_data:
                    from reward_system import add_reward_to_player
                    add_reward_to_player(player, reward_data, user_id, self.game_server)

                # 保存玩家数据
                await self.game_server.save_player_data(str(user_id), player)
                await self.game_server.notify_player_data_update(user_id, player)

                reward_text = []
                if contribution_reward > 0:
                    reward_text.append(f'贡献 +{contribution_reward}')
                if rewards.get('silver', 0) > 0:
                    reward_text.append(f'银两 +{rewards["silver"]}')
                if rewards.get('exp', 0) > 0:
                    reward_text.append(f'历练 +{rewards["exp"]}')

                return {
                    'type': 'sect_action_success',
                    'data': {
                        'action': 'submit_sect_quest',
                        'message': f'任务完成！获得奖励：{", ".join(reward_text)}',
                        'quest_id': quest_id
                    }
                }

        except Exception as e:
            logger.error(f"提交门派任务失败: {e}")
            return {'type': 'error', 'data': {'message': f'提交任务失败: {str(e)}'}}

    async def get_sect_members(self, user_id: str) -> dict:
        """获取门派成员列表"""
        try:
            await self.init_sect_tables()

            # 检查玩家是否加入门派
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    member_info = await cursor.fetchone()

                if not member_info:
                    return {'type': 'error', 'data': {'message': '你还没有加入门派'}}

                sect_id = member_info[0]

                # 获取门派所有成员
                async with db.execute('''
                    SELECT sm.user_id, sm.rank, sm.contribution, sm.join_time
                    FROM sect_members sm
                    WHERE sm.sect_id = ?
                    ORDER BY sm.rank DESC, sm.contribution DESC
                ''', (sect_id,)) as cursor:
                    members_data = await cursor.fetchall()

                members = []
                rank_names = {1: "记名弟子", 2: "外门弟子", 3: "内门弟子"}

                for member_data in members_data:
                    member_user_id, rank, contribution, join_time = member_data

                    # 获取玩家信息
                    player_data = self.game_server.player_data.get(str(member_user_id))
                    if player_data:
                        member_name = player_data.get('character_name') or player_data.get('name') or f'玩家{member_user_id}'
                        member_level = player_data.get('level', 1)
                    else:
                        member_name = f'玩家{member_user_id}'
                        member_level = 1

                    members.append({
                        'user_id': member_user_id,
                        'name': member_name,
                        'level': member_level,
                        'rank': rank,
                        'rank_name': rank_names.get(rank, '记名弟子'),
                        'contribution': contribution,
                        'join_time': join_time,
                        'is_self': member_user_id == user_id
                    })

                sect_config = self.sects_config['sects'].get(sect_id, {})

                return {
                    'type': 'sect_action_success',
                    'data': {
                        'action': 'get_sect_members',
                        'sect_name': sect_config.get('name', '未知门派'),
                        'members': members,
                        'total_members': len(members)
                    }
                }

        except Exception as e:
            logger.error(f"获取门派成员失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取门派成员失败: {str(e)}'}}

    async def get_sect_shop(self, user_id: str) -> dict:
        """获取门派商店"""
        try:
            await self.init_sect_tables()

            # 检查玩家是否加入门派
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id, rank, contribution FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    member_info = await cursor.fetchone()

                if not member_info:
                    return {'type': 'error', 'data': {'message': '你还没有加入门派'}}

                sect_id, rank, contribution = member_info
                sect_config = self.sects_config['sects'].get(sect_id, {})

                # 门派商店物品配置
                shop_items = [
                    {
                        'id': 'sect_pill_1',
                        'name': '门派丹药',
                        'description': '门派特制丹药，可恢复气血',
                        'type': 'consumable',
                        'price': 100,
                        'currency': 'contribution',
                        'rank_requirement': 1,
                        'effects': {'hp': 200}
                    },
                    {
                        'id': 'sect_pill_2',
                        'name': '门派内丹',
                        'description': '门派特制内丹，可恢复内力',
                        'type': 'consumable',
                        'price': 150,
                        'currency': 'contribution',
                        'rank_requirement': 2,
                        'effects': {'mp': 300}
                    },
                    {
                        'id': 'sect_weapon',
                        'name': f'{sect_config.get("name", "门派")}宝剑',
                        'description': f'{sect_config.get("name", "门派")}传承宝剑',
                        'type': 'weapon',
                        'price': 500,
                        'currency': 'contribution',
                        'rank_requirement': 3,
                        'effects': {'attack': 50}
                    }
                ]

                # 检查每个物品的购买条件
                available_items = []
                for item in shop_items:
                    can_buy = True
                    reasons = []

                    # 检查等级要求
                    if rank < item['rank_requirement']:
                        can_buy = False
                        rank_names = {1: "记名弟子", 2: "外门弟子", 3: "内门弟子"}
                        reasons.append(f"需要{rank_names.get(item['rank_requirement'], '更高')}等级")

                    # 检查贡献要求
                    if contribution < item['price']:
                        can_buy = False
                        reasons.append(f"贡献不足(需要{item['price']}，当前{contribution})")

                    available_items.append({
                        **item,
                        'can_buy': can_buy,
                        'reasons': reasons
                    })

                return {
                    'type': 'sect_action_success',
                    'data': {
                        'action': 'get_sect_shop',
                        'sect_name': sect_config.get('name', '未知门派'),
                        'items': available_items,
                        'player_contribution': contribution
                    }
                }

        except Exception as e:
            logger.error(f"获取门派商店失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取门派商店失败: {str(e)}'}}

    async def get_sect_skills(self, user_id: str) -> dict:
        """获取门派武功列表"""
        try:
            # 检查玩家是否加入门派
            await self.init_sect_tables()

            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id, rank, contribution FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    member_info = await cursor.fetchone()

            if not member_info:
                return {'type': 'error', 'data': {'message': '你还没有加入门派'}}

            sect_id, rank, contribution = member_info
            sect_config = self.sects_config['sects'].get(sect_id)
            if not sect_config:
                return {'type': 'error', 'data': {'message': '门派配置不存在'}}

            # 加载该门派的武功配置
            sect_skills = self.load_sect_skills(sect_id)

            # 获取当前等级可学习的武功
            rank_names = {1: "记名弟子", 2: "外门弟子", 3: "内门弟子"}
            current_rank_name = rank_names.get(rank, "记名弟子")

            available_skills = []
            for skill in sect_skills:
                    skill_rank_req = skill.get('等级要求', '记名弟子')
                    skill_contribution_req = skill.get('贡献要求', 0)

                    # 检查等级要求
                    can_learn = False
                    if skill_rank_req == "记名弟子" and rank >= 1:
                        can_learn = True
                    elif skill_rank_req == "外门弟子" and rank >= 2:
                        can_learn = True
                    elif skill_rank_req == "内门弟子" and rank >= 3:
                        can_learn = True

                    # 检查贡献要求
                    has_contribution = contribution >= skill_contribution_req

                    available_skills.append({
                        'skill_name': skill.get('武功名'),
                        'skill_type': skill.get('类型'),
                        'quality': skill.get('品质'),
                        'weapon': skill.get('武器'),
                        'rank_requirement': skill_rank_req,
                        'contribution_requirement': skill_contribution_req,
                        'can_learn': can_learn and has_contribution,
                        'reason': [] if can_learn and has_contribution else
                                 (['等级不足'] if not can_learn else []) +
                                 (['贡献不足'] if not has_contribution else []),
                        'attack': skill.get('攻击', 0),
                        'defense': skill.get('防御', 0),
                        'internal_power': skill.get('内力', 0),
                        'hp': skill.get('血量', 0),
                        'energy': skill.get('精力', 0)
                    })

            return {
                'type': 'sect_action_success',
                'data': {
                    'action': 'get_sect_skills',
                    'sect_name': sect_config['name'],
                    'current_rank': current_rank_name,
                    'contribution': contribution,
                    'skills': available_skills
                }
            }

        except Exception as e:
            logger.error(f"获取门派武功失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取门派武功失败: {str(e)}'}}

    def load_sect_skills(self, sect_id: str = None) -> list:
        """加载门派武功配置"""
        try:
            all_skills = []

            # 如果指定了门派，只加载该门派的武功
            if sect_id:
                sect_name_map = {
                    'wudang': 'wudang',
                    'shaolin': 'shaolin',
                    'emei': 'emei',
                    'huashan': 'huashan'
                }

                if sect_id in sect_name_map:
                    config_path = os.path.join(os.path.dirname(__file__), 'config', f'wugong_{sect_name_map[sect_id]}.json')
                    logger.error(f"[load_sect_skills] 加载武功配置文件: {config_path}")
                    with open(config_path, 'r', encoding='utf-8') as f:
                        skills_data = json.load(f)
                        logger.error(f"[load_sect_skills] 加载的武功数据类型: {type(skills_data)}")
                        logger.error(f"[load_sect_skills] 武功数据长度: {len(skills_data) if isinstance(skills_data, list) else 'N/A'}")
                        return skills_data
            else:
                # 加载所有门派武功
                sect_files = ['wugong_wudang.json', 'wugong_shaolin.json', 'wugong_emei.json', 'wugong_huashan.json']
                for sect_file in sect_files:
                    config_path = os.path.join(os.path.dirname(__file__), 'config', sect_file)
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            sect_skills = json.load(f)
                            all_skills.extend(sect_skills)
                    except Exception as e:
                        logger.error(f"加载{sect_file}失败: {e}")
                        continue

                return all_skills

        except Exception as e:
            logger.error(f"加载门派武功配置失败: {e}")
            return []

    async def learn_sect_skill(self, data: dict, user_id: str) -> dict:
        """学习门派武功"""
        try:
            skill_name = data.get('skill_name')
            logger.error(f"[learn_sect_skill] 开始学习武功: {skill_name}, 用户: {user_id}")

            if not skill_name:
                return {'type': 'error', 'data': {'message': '请指定武功名称'}}

            # 检查玩家是否加入门派
            await self.init_sect_tables()

            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id, rank, contribution FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    member_info = await cursor.fetchone()

            if not member_info:
                return {'type': 'error', 'data': {'message': '你还没有加入门派'}}

            sect_id, rank, contribution = member_info
            logger.error(f"[learn_sect_skill] 门派信息: sect_id={sect_id}, rank={rank}, contribution={contribution}")

            sect_config = self.sects_config['sects'].get(sect_id)
            if not sect_config:
                return {'type': 'error', 'data': {'message': '门派配置不存在'}}

            # 查找武功配置
            sect_skills = self.load_sect_skills(sect_id)
            logger.error(f"[learn_sect_skill] 门派武功数据类型: {type(sect_skills)}")
            logger.error(f"[learn_sect_skill] 门派武功数据: {sect_skills}")

            skill_config = None
            if isinstance(sect_skills, list):
                for skill in sect_skills:
                    logger.error(f"[learn_sect_skill] 检查武功: {skill}")
                    if isinstance(skill, dict) and skill.get('武功名') == skill_name:
                        skill_config = skill
                        break
            else:
                logger.error(f"[learn_sect_skill] sect_skills 不是列表，而是: {type(sect_skills)}")

            if not skill_config:
                return {'type': 'error', 'data': {'message': '武功不存在或不属于你的门派'}}

            # 检查学习条件
            skill_rank_req = skill_config.get('等级要求', '记名弟子')
            skill_contribution_req = skill_config.get('贡献要求', 0)

            # 检查等级要求
            can_learn = False
            if skill_rank_req == "记名弟子" and rank >= 1:
                can_learn = True
            elif skill_rank_req == "外门弟子" and rank >= 2:
                can_learn = True
            elif skill_rank_req == "内门弟子" and rank >= 3:
                can_learn = True

            if not can_learn:
                return {'type': 'error', 'data': {'message': f'等级不足，需要{skill_rank_req}'}}

            if contribution < skill_contribution_req:
                return {'type': 'error', 'data': {'message': f'贡献不足，需要{skill_contribution_req}点贡献'}}

            # 检查是否已经学会
            player = self.game_server.player_data.get(str(user_id))
            if player:
                # 检查martial_skills字段
                learned_skills = player.get('martial_skills', {})
                if isinstance(learned_skills, dict) and skill_name in learned_skills:
                    return {'type': 'error', 'data': {'message': '你已经学会了这门武功'}}
                elif isinstance(learned_skills, list):
                    # 如果是列表，检查是否包含该武功
                    for skill in learned_skills:
                        if isinstance(skill, dict) and skill.get('name') == skill_name:
                            return {'type': 'error', 'data': {'message': '你已经学会了这门武功'}}

                # 检查skills字段（兼容性）
                skills = player.get('skills', {})
                if isinstance(skills, dict) and skill_name in skills:
                    return {'type': 'error', 'data': {'message': '你已经学会了这门武功'}}
                elif isinstance(skills, list):
                    # 如果是列表，检查是否包含该武功
                    for skill in skills:
                        if isinstance(skill, dict) and skill.get('name') == skill_name:
                            return {'type': 'error', 'data': {'message': '你已经学会了这门武功'}}

            # 扣除贡献，学习武功
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    UPDATE sect_members SET contribution = contribution - ? WHERE user_id = ?
                ''', (skill_contribution_req, user_id))
                await db.commit()

            # 添加武功到玩家数据
            if player:
                # 确保两个字段都存在且为字典类型
                if 'martial_skills' not in player or not isinstance(player['martial_skills'], dict):
                    player['martial_skills'] = {}
                if 'skills' not in player or not isinstance(player['skills'], dict):
                    player['skills'] = {}

                skill_data = {
                    'level': 1,
                    'experience': 0,
                    'type': skill_config.get('类型'),
                    'quality': skill_config.get('品质'),
                    'weapon': skill_config.get('武器'),
                    'attack': skill_config.get('攻击', 0),
                    'defense': skill_config.get('防御', 0),
                    'internal_power': skill_config.get('内力', 0),
                    'hp': skill_config.get('血量', 0),
                    'energy': skill_config.get('精力', 0)
                }

                logger.error(f"[learn_sect_skill] martial_skills类型: {type(player['martial_skills'])}")
                logger.error(f"[learn_sect_skill] skills类型: {type(player['skills'])}")

                # 同时添加到两个字段（兼容性）
                player['martial_skills'][skill_name] = skill_data
                player['skills'][skill_name] = skill_data

                # 保存玩家数据
                await self.game_server.save_player_data(user_id, player)

                # 推送数据更新
                await self.game_server.notify_player_data_update(user_id, player)

            logger.info(f"玩家 {player.get('character_name', user_id)} 学习门派武功 {skill_name}")

            return {
                'type': 'learn_skill_success',
                'data': {
                    'message': f'成功学习武功：{skill_name}',
                    'skill_name': skill_name,
                    'contribution_cost': skill_contribution_req
                }
            }

        except Exception as e:
            import traceback
            tb = traceback.format_exc()
            logger.error(f"学习门派武功失败: {e}")
            logger.error(f"异常详情: {tb}")
            return {'type': 'error', 'data': {'message': f'学习门派武功失败: {str(e)}'}}

    async def get_sect_rankings(self, data: dict, user_id: str) -> dict:
        """获取门派内玩家排行榜"""
        try:
            ranking_type = data.get('ranking_type', 'power')  # power, martial, contribution

            await self.init_sect_tables()

            # 首先获取玩家所在门派
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    row = await cursor.fetchone()

                if not row:
                    return {'type': 'error', 'data': {'message': '你尚未加入任何门派'}}

                sect_id = row[0]
                sect_config = self.sects_config['sects'].get(sect_id, {})

            rankings = []

            if ranking_type == 'power':
                # 门派内实力排行榜（基于玩家历练值）
                async with aiosqlite.connect(self.db_path) as db:
                    async with db.execute('''
                        SELECT sm.user_id, sm.contribution
                        FROM sect_members sm
                        WHERE sm.sect_id = ?
                        ORDER BY sm.contribution DESC
                        LIMIT 20
                    ''', (sect_id,)) as cursor:
                        rows = await cursor.fetchall()

                        for rank, (member_user_id, contribution) in enumerate(rows, 1):
                            # 获取玩家数据
                            player_data = self.game_server.player_data.get(str(member_user_id), {})
                            experience = player_data.get('experience', 0)

                            rankings.append({
                                'rank': rank,
                                'player_name': player_data.get('character_name', f'玩家{member_user_id}'),
                                'experience': experience,
                                'contribution': contribution
                            })

            elif ranking_type == 'martial':
                # 门派内武功排行榜（基于武功等级综合）
                async with aiosqlite.connect(self.db_path) as db:
                    async with db.execute('''
                        SELECT sm.user_id, sm.contribution
                        FROM sect_members sm
                        WHERE sm.sect_id = ?
                        ORDER BY sm.contribution DESC
                        LIMIT 20
                    ''', (sect_id,)) as cursor:
                        rows = await cursor.fetchall()

                        for rank, (member_user_id, contribution) in enumerate(rows, 1):
                            # 获取玩家数据
                            player_data = self.game_server.player_data.get(str(member_user_id), {})

                            # 计算武功等级综合
                            martial_score = 0
                            total_skills = {}

                            # 检查多个可能的武功字段
                            skills_fields = ['skills', 'martial_skills', 'wugong']
                            for field in skills_fields:
                                skills = player_data.get(field, {})
                                if isinstance(skills, dict):
                                    total_skills.update(skills)

                            # 计算武功总分
                            for skill_name, skill_data in total_skills.items():
                                if isinstance(skill_data, dict):
                                    level = skill_data.get('level', 0)
                                    martial_score += level
                                elif isinstance(skill_data, (int, float)):
                                    # 如果直接存储等级数字
                                    martial_score += skill_data

                            rankings.append({
                                'rank': rank,
                                'player_name': player_data.get('character_name', f'玩家{member_user_id}'),
                                'martial_score': martial_score,
                                'skill_count': len(total_skills)
                            })

            elif ranking_type == 'contribution':
                # 门派内贡献度排行榜
                async with aiosqlite.connect(self.db_path) as db:
                    async with db.execute('''
                        SELECT sm.user_id, sm.contribution, sm.rank
                        FROM sect_members sm
                        WHERE sm.sect_id = ?
                        ORDER BY sm.contribution DESC
                        LIMIT 20
                    ''', (sect_id,)) as cursor:
                        rows = await cursor.fetchall()

                        rank_names = {1: "记名弟子", 2: "外门弟子", 3: "内门弟子", 4: "核心弟子", 5: "长老", 6: "掌门"}

                        for rank, (member_user_id, contribution, member_rank) in enumerate(rows, 1):
                            # 获取玩家数据
                            player_data = self.game_server.player_data.get(str(member_user_id), {})

                            rankings.append({
                                'rank': rank,
                                'player_name': player_data.get('character_name', f'玩家{member_user_id}'),
                                'contribution': contribution,
                                'sect_rank': rank_names.get(member_rank, "记名弟子")
                            })

            return {
                'type': 'sect_action_success',
                'data': {
                    'action': 'get_sect_rankings',
                    'ranking_type': ranking_type,
                    'sect_name': sect_config.get('name', ''),
                    'rankings': rankings
                }
            }

        except Exception as e:
            logger.error(f"获取门派排行榜失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取门派排行榜失败: {str(e)}'}}

    async def declare_war(self, data: dict, user_id: str) -> dict:
        """宣战"""
        try:
            target_sect_id = data.get('target_sect_id')
            war_reason = data.get('war_reason', '门派争端')

            if not target_sect_id:
                return {'type': 'error', 'data': {'message': '请选择宣战目标'}}

            await self.init_sect_tables()

            # 检查玩家是否加入门派且有权限宣战
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id, rank FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    member_info = await cursor.fetchone()

                if not member_info:
                    return {'type': 'error', 'data': {'message': '你还没有加入门派'}}

                attacker_sect_id, rank = member_info

                # 检查权限（只有掌门和长老可以宣战，假设rank >= 3）
                if rank < 3:
                    return {'type': 'error', 'data': {'message': '只有门派高层才能宣战'}}

                # 检查是否对同一门派宣战
                if attacker_sect_id == target_sect_id:
                    return {'type': 'error', 'data': {'message': '不能对自己的门派宣战'}}

                # 检查目标门派是否存在
                target_sect_config = self.sects_config['sects'].get(target_sect_id)
                if not target_sect_config:
                    return {'type': 'error', 'data': {'message': '目标门派不存在'}}

                # 检查是否已经有进行中的战争
                async with db.execute('''
                    SELECT id FROM sect_wars
                    WHERE (attacker_sect_id = ? AND defender_sect_id = ?)
                       OR (attacker_sect_id = ? AND defender_sect_id = ?)
                    AND status IN ('declared', 'active')
                ''', (attacker_sect_id, target_sect_id, target_sect_id, attacker_sect_id)) as cursor:
                    existing_war = await cursor.fetchone()

                if existing_war:
                    return {'type': 'error', 'data': {'message': '与该门派已有进行中的战争'}}

                # 创建战争记录
                await db.execute('''
                    INSERT INTO sect_wars (attacker_sect_id, defender_sect_id, war_reason)
                    VALUES (?, ?, ?)
                ''', (attacker_sect_id, target_sect_id, war_reason))
                await db.commit()

                attacker_sect_config = self.sects_config['sects'].get(attacker_sect_id, {})

                return {
                    'type': 'declare_war_success',
                    'data': {
                        'message': f'{attacker_sect_config.get("name", "未知门派")} 向 {target_sect_config.get("name", "未知门派")} 宣战！',
                        'attacker_sect': attacker_sect_config.get('name', '未知门派'),
                        'defender_sect': target_sect_config.get('name', '未知门派'),
                        'war_reason': war_reason
                    }
                }

        except Exception as e:
            logger.error(f"宣战失败: {e}")
            return {'type': 'error', 'data': {'message': f'宣战失败: {str(e)}'}}

    async def get_sect_wars(self, user_id: str) -> dict:
        """获取门派战争列表"""
        try:
            await self.init_sect_tables()

            # 检查玩家门派
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    member_info = await cursor.fetchone()

                if not member_info:
                    return {'type': 'error', 'data': {'message': '你还没有加入门派'}}

                player_sect_id = member_info[0]

                # 获取相关的战争
                async with db.execute('''
                    SELECT id, attacker_sect_id, defender_sect_id, status,
                           declare_time, start_time, end_time, winner_sect_id,
                           attacker_score, defender_score, war_reason
                    FROM sect_wars
                    WHERE attacker_sect_id = ? OR defender_sect_id = ?
                    ORDER BY declare_time DESC
                    LIMIT 20
                ''', (player_sect_id, player_sect_id)) as cursor:
                    rows = await cursor.fetchall()

                wars = []
                for row in rows:
                    war_id, attacker_sect_id, defender_sect_id, status, declare_time, start_time, end_time, winner_sect_id, attacker_score, defender_score, war_reason = row

                    attacker_config = self.sects_config['sects'].get(attacker_sect_id, {})
                    defender_config = self.sects_config['sects'].get(defender_sect_id, {})
                    winner_config = self.sects_config['sects'].get(winner_sect_id, {}) if winner_sect_id else None

                    wars.append({
                        'war_id': war_id,
                        'attacker_sect_id': attacker_sect_id,
                        'attacker_sect_name': attacker_config.get('name', '未知门派'),
                        'defender_sect_id': defender_sect_id,
                        'defender_sect_name': defender_config.get('name', '未知门派'),
                        'status': status,
                        'declare_time': declare_time,
                        'start_time': start_time,
                        'end_time': end_time,
                        'winner_sect_id': winner_sect_id,
                        'winner_sect_name': winner_config.get('name', '未知门派') if winner_config else None,
                        'attacker_score': attacker_score,
                        'defender_score': defender_score,
                        'war_reason': war_reason,
                        'is_attacker': attacker_sect_id == player_sect_id
                    })

                return {
                    'type': 'sect_action_success',
                    'data': {
                        'action': 'get_sect_wars',
                        'wars': wars,
                        'player_sect_id': player_sect_id
                    }
                }

        except Exception as e:
            logger.error(f"获取门派战争失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取门派战争失败: {str(e)}'}}

    async def get_sect_buildings(self, user_id: str) -> dict:
        """获取门派建筑信息"""
        try:
            await self.init_sect_tables()

            # 检查玩家门派
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    member_info = await cursor.fetchone()

                if not member_info:
                    return {'type': 'error', 'data': {'message': '你还没有加入门派'}}

                sect_id = member_info[0]

                # 获取门派建筑
                async with db.execute('''
                    SELECT building_type, building_level, upgrade_progress, last_upgrade_time
                    FROM sect_buildings WHERE sect_id = ?
                ''', (sect_id,)) as cursor:
                    building_rows = await cursor.fetchall()

                # 获取门派资源
                async with db.execute('''
                    SELECT wood, stone, iron, gold FROM sect_resources WHERE sect_id = ?
                ''', (sect_id,)) as cursor:
                    resource_row = await cursor.fetchone()

                # 初始化默认建筑
                default_buildings = {
                    'main_hall': {'name': '主殿', 'level': 1, 'progress': 0, 'last_upgrade': None},
                    'training_ground': {'name': '练功场', 'level': 1, 'progress': 0, 'last_upgrade': None},
                    'library': {'name': '藏书阁', 'level': 1, 'progress': 0, 'last_upgrade': None},
                    'warehouse': {'name': '仓库', 'level': 1, 'progress': 0, 'last_upgrade': None}
                }

                # 更新建筑信息
                buildings = {}
                for building_type, building_info in default_buildings.items():
                    buildings[building_type] = building_info.copy()

                for building_type, level, progress, last_upgrade in building_rows:
                    if building_type in buildings:
                        buildings[building_type]['level'] = level
                        buildings[building_type]['progress'] = progress
                        buildings[building_type]['last_upgrade'] = last_upgrade

                # 资源信息
                resources = {
                    'wood': 0,
                    'stone': 0,
                    'iron': 0,
                    'gold': 0
                }

                if resource_row:
                    resources['wood'], resources['stone'], resources['iron'], resources['gold'] = resource_row

                return {
                    'type': 'sect_action_success',
                    'data': {
                        'action': 'get_sect_buildings',
                        'buildings': buildings,
                        'resources': resources,
                        'sect_id': sect_id
                    }
                }

        except Exception as e:
            logger.error(f"获取门派建筑失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取门派建筑失败: {str(e)}'}}

    async def upgrade_building(self, data: dict, user_id: str) -> dict:
        """升级建筑"""
        try:
            building_type = data.get('building_type')
            if not building_type:
                return {'type': 'error', 'data': {'message': '请选择要升级的建筑'}}

            await self.init_sect_tables()

            # 检查玩家门派和权限
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sect_id, rank FROM sect_members WHERE user_id = ?
                ''', (user_id,)) as cursor:
                    member_info = await cursor.fetchone()

                if not member_info:
                    return {'type': 'error', 'data': {'message': '你还没有加入门派'}}

                sect_id, rank = member_info

                # 检查权限（只有掌门和长老可以升级建筑）
                if rank < 3:
                    return {'type': 'error', 'data': {'message': '只有门派高层才能升级建筑'}}

                # 获取当前建筑等级
                async with db.execute('''
                    SELECT building_level FROM sect_buildings
                    WHERE sect_id = ? AND building_type = ?
                ''', (sect_id, building_type)) as cursor:
                    building_row = await cursor.fetchone()

                current_level = building_row[0] if building_row else 1
                next_level = current_level + 1

                # 计算升级所需资源
                upgrade_cost = self.calculate_upgrade_cost(building_type, next_level)

                # 获取门派资源
                async with db.execute('''
                    SELECT wood, stone, iron, gold FROM sect_resources WHERE sect_id = ?
                ''', (sect_id,)) as cursor:
                    resource_row = await cursor.fetchone()

                if not resource_row:
                    # 初始化资源
                    await db.execute('''
                        INSERT INTO sect_resources (sect_id, wood, stone, iron, gold)
                        VALUES (?, 0, 0, 0, 0)
                    ''', (sect_id,))
                    current_resources = {'wood': 0, 'stone': 0, 'iron': 0, 'gold': 0}
                else:
                    current_resources = {
                        'wood': resource_row[0],
                        'stone': resource_row[1],
                        'iron': resource_row[2],
                        'gold': resource_row[3]
                    }

                # 检查资源是否足够
                for resource_type, cost in upgrade_cost.items():
                    if current_resources[resource_type] < cost:
                        return {
                            'type': 'error',
                            'data': {
                                'message': f'资源不足！需要{resource_type}: {cost}，当前: {current_resources[resource_type]}'
                            }
                        }

                # 扣除资源
                new_resources = {}
                for resource_type, cost in upgrade_cost.items():
                    new_resources[resource_type] = current_resources[resource_type] - cost

                await db.execute('''
                    UPDATE sect_resources
                    SET wood = ?, stone = ?, iron = ?, gold = ?, last_update_time = CURRENT_TIMESTAMP
                    WHERE sect_id = ?
                ''', (new_resources['wood'], new_resources['stone'], new_resources['iron'], new_resources['gold'], sect_id))

                # 更新建筑等级
                if building_row:
                    await db.execute('''
                        UPDATE sect_buildings
                        SET building_level = ?, last_upgrade_time = CURRENT_TIMESTAMP
                        WHERE sect_id = ? AND building_type = ?
                    ''', (next_level, sect_id, building_type))
                else:
                    await db.execute('''
                        INSERT INTO sect_buildings (sect_id, building_type, building_level, last_upgrade_time)
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (sect_id, building_type, next_level))

                await db.commit()

                building_names = {
                    'main_hall': '主殿',
                    'training_ground': '练功场',
                    'library': '藏书阁',
                    'warehouse': '仓库'
                }

                return {
                    'type': 'upgrade_building_success',
                    'data': {
                        'message': f'{building_names.get(building_type, building_type)} 升级到 {next_level} 级成功！',
                        'building_type': building_type,
                        'new_level': next_level,
                        'cost': upgrade_cost,
                        'remaining_resources': new_resources
                    }
                }

        except Exception as e:
            logger.error(f"升级建筑失败: {e}")
            return {'type': 'error', 'data': {'message': f'升级建筑失败: {str(e)}'}}

    def calculate_upgrade_cost(self, building_type: str, level: int) -> dict:
        """计算升级成本"""
        base_costs = {
            'main_hall': {'wood': 100, 'stone': 150, 'iron': 50, 'gold': 200},
            'training_ground': {'wood': 80, 'stone': 100, 'iron': 120, 'gold': 150},
            'library': {'wood': 120, 'stone': 80, 'iron': 60, 'gold': 180},
            'warehouse': {'wood': 60, 'stone': 120, 'iron': 80, 'gold': 100}
        }

        base_cost = base_costs.get(building_type, {'wood': 100, 'stone': 100, 'iron': 100, 'gold': 100})

        # 每级成本递增50%
        multiplier = 1.5 ** (level - 1)

        return {
            resource: int(cost * multiplier)
            for resource, cost in base_cost.items()
        }
