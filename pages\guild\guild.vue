<template>
	<view class="container">
		<!-- 门派信息 -->
		<view class="guild-info" v-if="playerGuild">
			<view class="guild-header">
				<text class="guild-name">{{ playerGuild.name }}</text>
				<text class="guild-level">等级 {{ playerGuild.level }}</text>
			</view>
			<view class="guild-stats">
				<view class="stat-item">
					<text class="stat-label">声望:</text>
					<text class="stat-value">{{ player.reputation || 0 }}</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">贡献:</text>
					<text class="stat-value">{{ playerGuild.contribution }}</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">职位:</text>
					<text class="stat-value">{{ getPositionName(playerGuild.position) }}</text>
				</view>
			</view>
			<view class="guild-actions" v-if="sectInfo">
				<button
					class="daily-reward-btn"
					@click="claimDailyReward"
					:disabled="!sectInfo.can_claim_daily"
				>
					{{ sectInfo.can_claim_daily ? '领取每日奖励' : '今日已领取' }}
				</button>
			</view>
		</view>

		<!-- 未加入门派 -->
		<view class="no-guild" v-else>
			<text class="no-guild-title">尚未加入门派</text>
			<text class="no-guild-desc">加入门派可以获得专属武功和任务</text>
			<button class="join-guild-btn" @click="showGuildList">加入门派</button>
		</view>

		<!-- 门派功能 -->
		<view class="guild-functions" v-if="playerGuild">
			<view class="function-grid">
				<view class="function-item" @click="showTasks">
					<text class="function-icon">📋</text>
					<text class="function-name">门派任务</text>
				</view>
				<view class="function-item" @click="showSkills">
					<text class="function-icon">⚔️</text>
					<text class="function-name">门派武功</text>
				</view>
				<view class="function-item" @click="showMembers">
					<text class="function-icon">👥</text>
					<text class="function-name">门派成员</text>
				</view>
				<view class="function-item" @click="showShop">
					<text class="function-icon">🏪</text>
					<text class="function-name">门派商店</text>
				</view>
				<view class="function-item" @click="showRankings">
					<text class="function-icon">🏆</text>
					<text class="function-name">门派排行</text>
				</view>
				<view class="function-item" @click="showWars">
					<text class="function-icon">⚔️</text>
					<text class="function-name">门派战争</text>
				</view>
				<view class="function-item" @click="showBuildings">
					<text class="function-icon">🏗️</text>
					<text class="function-name">门派建设</text>
				</view>
			</view>
		</view>

		<!-- 门派任务 -->
		<view class="tasks-section" v-if="showTasksSection">
			<view class="section-header">
				<text class="section-title">门派任务</text>
				<text class="section-close" @click="hideTasks">×</text>
			</view>
			<scroll-view class="tasks-list" scroll-y="true">
				<view 
					class="task-item" 
					v-for="(task, index) in availableTasks" 
					:key="index"
					@click="showTaskDetail(task)"
				>
					<view class="task-info">
						<text class="task-name">{{ task.name }}</text>
						<text class="task-desc">{{ task.description }}</text>
						<text class="task-reward">奖励: {{ task.reward }}</text>
					</view>
					<view class="task-status">
						<text class="task-difficulty" :class="getDifficultyClass(task.difficulty)">
							{{ getDifficultyName(task.difficulty) }}
						</text>
						<button 
							class="accept-task-btn" 
							@click.stop="acceptTask(task)"
							:disabled="!canAcceptTask(task)"
						>
							接受
						</button>
					</view>
				</view>
				<view class="empty-tasks" v-if="availableTasks.length === 0">
					<text>暂无可接任务</text>
				</view>
			</scroll-view>
		</view>

		<!-- 门派武功 -->
		<view class="skills-section" v-if="showSkillsSection">
			<view class="section-header">
				<text class="section-title">门派武功</text>
				<text class="section-close" @click="hideSkills">×</text>
			</view>
			<scroll-view class="skills-list" scroll-y="true">
				<view 
					class="skill-item" 
					v-for="(skill, index) in guildSkills" 
					:key="index"
					@click="showSkillDetail(skill)"
				>
					<view class="skill-info">
						<text class="skill-name">{{ skill.name }}</text>
						<text class="skill-type">{{ getSkillTypeName(skill.type) }}</text>
						<text class="skill-desc">{{ skill.description }}</text>
					</view>
					<view class="skill-status">
						<text class="skill-level" v-if="skill.level">等级 {{ skill.level }}</text>
						<button 
							class="learn-skill-btn" 
							@click.stop="learnSkill(skill)"
							:disabled="!canLearnSkill(skill)"
						>
							{{ skill.learned ? '修炼' : '学习' }}
						</button>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 门派成员 -->
		<view class="members-section" v-if="showMembersSection">
			<view class="section-header">
				<text class="section-title">门派成员</text>
				<text class="section-close" @click="hideMembers">×</text>
			</view>
			<scroll-view class="members-list" scroll-y="true">
				<view 
					class="member-item" 
					v-for="(member, index) in guildMembers" 
					:key="index"
				>
					<view class="member-info">
						<text class="member-name">{{ member.name }}</text>
						<text class="member-position">{{ getPositionName(member.position) }}</text>
						<text class="member-level">等级 {{ member.level }}</text>
					</view>
					<view class="member-contribution">
						<text class="contribution-label">贡献:</text>
						<text class="contribution-value">{{ member.contribution }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 门派商店 -->
		<view class="shop-section" v-if="showShopSection">
			<view class="section-header">
				<text class="section-title">门派商店</text>
				<text class="section-close" @click="hideShop">×</text>
			</view>
			<scroll-view class="shop-list" scroll-y="true">
				<view 
					class="shop-item" 
					v-for="(item, index) in guildShopItems" 
					:key="index"
					@click="showShopItemDetail(item)"
				>
					<view class="item-info">
						<text class="item-name">{{ item.name }}</text>
						<text class="item-desc">{{ item.description }}</text>
					</view>
					<view class="item-price">
						<text class="price-value">{{ item.price }}</text>
						<text class="price-unit">贡献</text>
					</view>
					<button 
						class="buy-item-btn" 
						@click.stop="buyShopItem(item)"
						:disabled="!canBuyItem(item)"
					>
						购买
					</button>
				</view>
			</scroll-view>
		</view>

		<!-- 门派排行榜 -->
		<view class="rankings-section" v-if="showRankingsSection">
			<view class="section-header">
				<text class="section-title">门派排行榜</text>
				<text class="section-close" @click="hideRankings">×</text>
			</view>
			<view class="ranking-tabs">
				<view
					class="ranking-tab"
					:class="{ active: currentRankingType === 'power' }"
					@click="switchRankingType('power')"
				>
					实力排行
				</view>
				<view
					class="ranking-tab"
					:class="{ active: currentRankingType === 'members' }"
					@click="switchRankingType('members')"
				>
					成员排行
				</view>
				<view
					class="ranking-tab"
					:class="{ active: currentRankingType === 'contribution' }"
					@click="switchRankingType('contribution')"
				>
					贡献排行
				</view>
			</view>
			<scroll-view class="rankings-list" scroll-y="true">
				<view
					class="ranking-item"
					v-for="(sect, index) in sectRankings"
					:key="index"
				>
					<view class="ranking-rank">
						<text class="rank-number">{{ sect.rank }}</text>
					</view>
					<view class="ranking-info">
						<text class="sect-name">{{ sect.sect_name }}</text>
						<view class="sect-stats">
							<text v-if="currentRankingType === 'power'" class="stat-text">
								实力: {{ sect.power_score }} | 成员: {{ sect.member_count }}人
							</text>
							<text v-else-if="currentRankingType === 'members'" class="stat-text">
								成员: {{ sect.member_count }}人 | 平均等级: {{ sect.avg_level }}
							</text>
							<text v-else-if="currentRankingType === 'contribution'" class="stat-text">
								总贡献: {{ sect.total_contribution }} | 平均贡献: {{ sect.avg_contribution }}
							</text>
						</view>
					</view>
				</view>
				<view v-if="sectRankings.length === 0" class="no-rankings">
					<text>暂无排行数据</text>
				</view>
			</scroll-view>
		</view>

		<!-- 门派战争 -->
		<view class="wars-section" v-if="showWarsSection">
			<view class="section-header">
				<text class="section-title">门派战争</text>
				<text class="section-close" @click="hideWars">×</text>
			</view>
			<view class="war-actions">
				<button class="declare-war-btn" @click="showDeclareWarModal">宣战</button>
			</view>
			<scroll-view class="wars-list" scroll-y="true">
				<view
					class="war-item"
					v-for="(war, index) in sectWars"
					:key="index"
				>
					<view class="war-header">
						<view class="war-sides">
							<text class="sect-name attacker" :class="{ 'own-sect': war.is_attacker }">
								{{ war.attacker_sect_name }}
							</text>
							<text class="vs-text">VS</text>
							<text class="sect-name defender" :class="{ 'own-sect': !war.is_attacker }">
								{{ war.defender_sect_name }}
							</text>
						</view>
						<view class="war-status" :class="'status-' + war.status">
							{{ getWarStatusText(war.status) }}
						</view>
					</view>
					<view class="war-info">
						<text class="war-reason">战争原因: {{ war.war_reason }}</text>
						<text class="war-time">宣战时间: {{ formatTime(war.declare_time) }}</text>
						<view v-if="war.status === 'ended'" class="war-result">
							<text class="winner">胜利者: {{ war.winner_sect_name || '平局' }}</text>
							<text class="score">比分: {{ war.attacker_score }} : {{ war.defender_score }}</text>
						</view>
					</view>
				</view>
				<view v-if="sectWars.length === 0" class="no-wars">
					<text>暂无战争记录</text>
				</view>
			</scroll-view>
		</view>

		<!-- 宣战弹窗 -->
		<view class="modal-overlay" v-if="showDeclareWarModal" @click="closeDeclareWar">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">宣战</text>
					<text class="modal-close" @click="closeDeclareWar">×</text>
				</view>
				<view class="modal-body">
					<view class="form-group">
						<text class="form-label">选择目标门派:</text>
						<picker
							:value="selectedTargetSectIndex"
							:range="availableTargetSects"
							range-key="name"
							@change="onTargetSectChange"
						>
							<view class="picker-display">
								{{ selectedTargetSect ? selectedTargetSect.name : '请选择门派' }}
							</view>
						</picker>
					</view>
					<view class="form-group">
						<text class="form-label">战争原因:</text>
						<textarea
							v-model="warReason"
							placeholder="请输入宣战理由..."
							maxlength="100"
							class="war-reason-input"
						/>
					</view>
				</view>
				<view class="modal-footer">
					<button class="cancel-btn" @click="closeDeclareWar">取消</button>
					<button
						class="confirm-btn"
						@click="confirmDeclareWar"
						:disabled="!selectedTargetSect || !warReason.trim()"
					>
						宣战
					</button>
				</view>
			</view>
		</view>

		<!-- 门派建设 -->
		<view class="buildings-section" v-if="showBuildingsSection">
			<view class="section-header">
				<text class="section-title">门派建设</text>
				<text class="section-close" @click="hideBuildings">×</text>
			</view>
			<view class="resources-display">
				<text class="resources-title">门派资源</text>
				<view class="resources-grid">
					<view class="resource-item">
						<text class="resource-icon">🪵</text>
						<text class="resource-name">木材</text>
						<text class="resource-value">{{ sectResources.wood || 0 }}</text>
					</view>
					<view class="resource-item">
						<text class="resource-icon">🪨</text>
						<text class="resource-name">石料</text>
						<text class="resource-value">{{ sectResources.stone || 0 }}</text>
					</view>
					<view class="resource-item">
						<text class="resource-icon">⚒️</text>
						<text class="resource-name">铁矿</text>
						<text class="resource-value">{{ sectResources.iron || 0 }}</text>
					</view>
					<view class="resource-item">
						<text class="resource-icon">💰</text>
						<text class="resource-name">金币</text>
						<text class="resource-value">{{ sectResources.gold || 0 }}</text>
					</view>
				</view>
			</view>
			<scroll-view class="buildings-list" scroll-y="true">
				<view
					class="building-item"
					v-for="(building, type) in sectBuildings"
					:key="type"
				>
					<view class="building-header">
						<view class="building-info">
							<text class="building-name">{{ building.name }}</text>
							<text class="building-level">等级 {{ building.level }}</text>
						</view>
						<button
							class="upgrade-btn"
							@click="upgradeBuilding(type)"
							:disabled="!canUpgradeBuilding(type)"
						>
							升级
						</button>
					</view>
					<view class="building-cost" v-if="getBuildingUpgradeCost(type, building.level + 1)">
						<text class="cost-title">升级消耗:</text>
						<view class="cost-items">
							<text
								v-for="(cost, resource) in getBuildingUpgradeCost(type, building.level + 1)"
								:key="resource"
								class="cost-item"
								:class="{ 'insufficient': (sectResources[resource] || 0) < cost }"
							>
								{{ getResourceName(resource) }}: {{ cost }}
							</text>
						</view>
					</view>
				</view>
				<view v-if="Object.keys(sectBuildings).length === 0" class="no-buildings">
					<text>暂无建筑数据</text>
				</view>
			</scroll-view>
		</view>

		<!-- 加入门派说明弹窗 -->
		<view class="modal-overlay" v-if="showGuildListModal" @click="closeGuildList">
			<view class="modal-content join-sect-guide" @click.stop>
				<view class="modal-header">
					<text class="modal-title">如何加入门派</text>
					<text class="modal-close" @click="closeGuildList">×</text>
				</view>
				<view class="modal-body">
					<view class="guide-section">
						<text class="guide-title">📜 门派令牌</text>
						<text class="guide-text">每个门派都需要对应的令牌才能加入，令牌可通过以下方式获得：</text>
					</view>

					<view class="guide-section">
						<text class="guide-subtitle">🎯 获取方式</text>
						<view class="guide-list">
							<text class="guide-item">• 完成特定任务获得门派令牌</text>
							<text class="guide-item">• 击败特定NPC有几率掉落令牌</text>
							<text class="guide-item">• 在商店购买门派令牌</text>
							<text class="guide-item">• 参与活动获得令牌奖励</text>
						</view>
					</view>

					<view class="guide-section">
						<text class="guide-subtitle">⚔️ 主要门派</text>
						<view class="sect-list">
							<view class="sect-item">
								<text class="sect-name">少林派</text>
								<text class="sect-desc">以内功和拳法闻名，需要少林令牌</text>
							</view>
							<view class="sect-item">
								<text class="sect-name">武当派</text>
								<text class="sect-desc">太极剑法和内功修为，需要武当令牌</text>
							</view>
							<view class="sect-item">
								<text class="sect-name">峨眉派</text>
								<text class="sect-desc">剑法精妙，医术高超，需要峨眉令牌</text>
							</view>
							<view class="sect-item">
								<text class="sect-name">华山派</text>
								<text class="sect-desc">剑法独步天下，需要华山令牌</text>
							</view>
						</view>
					</view>

					<view class="guide-section">
						<text class="guide-subtitle">💡 使用方法</text>
						<text class="guide-text">获得门派令牌后，在背包中使用即可自动加入对应门派。加入成功后将有全服公告。</text>
					</view>
				</view>
				<view class="modal-footer">
					<button class="modal-btn confirm-btn" @click="closeGuildList">我知道了</button>
				</view>
			</view>
		</view>

		<!-- 任务详情弹窗 -->
		<view class="modal-overlay" v-if="showTaskDetail" @click="closeTaskDetail">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">任务详情</text>
					<text class="modal-close" @click="closeTaskDetail">×</text>
				</view>
				<view class="modal-body" v-if="selectedTask">
					<text class="detail-name">{{ selectedTask.name }}</text>
					<text class="detail-desc">{{ selectedTask.description }}</text>
					<text class="detail-requirement">要求: {{ selectedTask.requirement }}</text>
					<text class="detail-reward">奖励: {{ selectedTask.reward }}</text>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeTaskDetail">关闭</button>
					<button 
						class="modal-btn confirm-btn" 
						@click="acceptTask(selectedTask)"
						:disabled="!canAcceptTask(selectedTask)"
					>
						接受任务
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import gameState from '../../utils/gameState.js'
import { gameUtils } from '../../utils/gameData.js'

export default {
	data() {
		return {
			player: {},
			playerGuild: null,
			sectInfo: null,
			showTasksSection: false,
			showSkillsSection: false,
			showMembersSection: false,
			showShopSection: false,
			showRankingsSection: false,
			showWarsSection: false,
			showBuildingsSection: false,
			showGuildListModal: false,
			showDeclareWarModal: false,
			showTaskDetail: false,
			selectedTask: null,
			availableGuilds: [],
			loading: false,
			availableTasks: [],
			guildSkills: [],
			guildMembers: [],
			guildShopItems: [],
			sectRankings: [],
			currentRankingType: 'power',
			sectWars: [],
			availableTargetSects: [],
			selectedTargetSectIndex: 0,
			selectedTargetSect: null,
			warReason: '',
			sectBuildings: {},
			sectResources: {}
		}
	},
	
	onLoad() {
		this.updateData()
		this.loadSectInfo()
	},

	onShow() {
		this.updateData()
		this.loadSectInfo()
	},

	onReady() {
		// 页面准备完毕后也刷新一次
		setTimeout(() => {
			this.loadSectInfo()
		}, 500)
	},

	methods: {
		updateData() {
			this.player = { ...gameState.player }
		},

		// 加载门派信息
		async loadSectInfo() {
			if (!gameState.isAuthed) {
				return
			}

			try {
				this.loading = true

				// 获取玩家门派信息
				const sectInfoResponse = await gameUtils.sendMessage({
					type: 'sect_action',
					data: { action: 'get_sect_info' }
				})

				console.log('[门派页面] 收到门派信息响应:', sectInfoResponse)

				if (sectInfoResponse.type === 'sect_info_success') {
					console.log('[门派页面] 门派信息成功，has_sect:', sectInfoResponse.data.has_sect)
					if (sectInfoResponse.data.has_sect) {
						this.sectInfo = sectInfoResponse.data
						this.playerGuild = {
							id: sectInfoResponse.data.sect_id,
							name: sectInfoResponse.data.sect_name,
							level: sectInfoResponse.data.rank,
							contribution: sectInfoResponse.data.contribution,
							position: sectInfoResponse.data.rank_name
						}
						console.log('[门派页面] 设置playerGuild:', this.playerGuild)
					} else {
						this.sectInfo = null
						this.playerGuild = null
						console.log('[门派页面] 玩家未加入门派')
					}
				} else {
					console.log('[门派页面] 门派信息响应类型错误:', sectInfoResponse.type)
				}

				// 如果没有门派，获取可用门派列表
				if (!this.playerGuild) {
					const availableResponse = await gameUtils.sendMessage({
						type: 'sect_action',
						data: { action: 'get_available_sects' }
					})

					if (availableResponse.type === 'available_sects_success') {
						this.availableGuilds = availableResponse.data.sects.map(sect => ({
							id: sect.sect_id,
							name: sect.sect_name,
							description: sect.description,
							requirement: sect.requirements.level || 1,
							can_join: sect.can_join,
							reasons: sect.reasons
						}))
					}
				}

			} catch (error) {
				console.error('加载门派信息失败:', error)
				uni.showToast({
					title: '加载门派信息失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		// 领取每日奖励
		async claimDailyReward() {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}

			try {
				const response = await gameUtils.sendMessage({
					type: 'sect_action',
					data: { action: 'claim_daily_reward' }
				})

				if (response.type === 'daily_reward_success') {
					uni.showToast({
						title: response.data.message,
						icon: 'success'
					})
					// 重新加载门派信息
					await this.loadSectInfo()
					// 更新玩家数据
					this.updateData()
				} else {
					uni.showToast({
						title: response.data?.message || '领取失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('领取每日奖励失败:', error)
				uni.showToast({
					title: '领取失败',
					icon: 'none'
				})
			}
		},

		// 加载门派武功
		async loadSectSkills() {
			if (!gameState.isAuthed || !this.playerGuild) {
				return
			}

			try {
				const response = await gameUtils.sendMessage({
					type: 'sect_action',
					data: { action: 'get_sect_skills' }
				})

				if (response.type === 'sect_skills_success') {
					this.guildSkills = response.data.skills.map(skill => ({
						id: skill.skill_name,
						name: skill.skill_name,
						type: skill.skill_type,
						quality: skill.quality,
						weapon: skill.weapon,
						description: `攻击+${skill.attack} 防御+${skill.defense} 内力+${skill.internal_power}`,
						rank_requirement: skill.rank_requirement,
						contribution_requirement: skill.contribution_requirement,
						can_learn: skill.can_learn,
						reason: skill.reason.join('，'),
						learned: false // TODO: 检查是否已学会
					}))
				} else {
					console.error('加载门派武功失败:', response.data?.message)
				}
			} catch (error) {
				console.error('加载门派武功失败:', error)
			}
		},

		// 加载门派任务
		async loadSectQuests() {
			if (!gameState.isAuthed || !this.playerGuild) {
				return
			}

			try {
				const response = await gameUtils.sendMessage({
					type: 'sect_action',
					data: { action: 'get_sect_quests' }
				})

				if (response.type === 'sect_quests_success') {
					this.guildTasks = response.data.quests.map(quest => ({
						id: quest.id,
						name: quest.name,
						description: quest.description,
						difficulty: quest.difficulty,
						status: quest.status,
						progress: quest.progress || 0,
						maxProgress: quest.max_progress || 1,
						rewards: quest.rewards,
						canAccept: quest.status === 'available'
					}))
				} else {
					console.error('加载门派任务失败:', response.data?.message)
				}
			} catch (error) {
				console.error('加载门派任务失败:', error)
			}
		},

		// 加载门派成员
		async loadSectMembers() {
			if (!gameState.isAuthed || !this.playerGuild) {
				return
			}

			try {
				const response = await gameUtils.sendMessage({
					type: 'sect_action',
					data: { action: 'get_sect_members' }
				})

				if (response.type === 'sect_members_success') {
					this.guildMembers = response.data.members.map(member => ({
						id: member.user_id,
						name: member.name,
						level: member.level,
						position: member.rank_name,
						contribution: member.contribution,
						joinTime: member.join_time,
						isSelf: member.is_self
					}))
				} else {
					console.error('加载门派成员失败:', response.data?.message)
				}
			} catch (error) {
				console.error('加载门派成员失败:', error)
			}
		},

		// 加载门派商店
		async loadSectShop() {
			if (!gameState.isAuthed || !this.playerGuild) {
				return
			}

			try {
				const response = await gameUtils.sendMessage({
					type: 'sect_action',
					data: { action: 'get_sect_shop' }
				})

				if (response.type === 'sect_shop_success') {
					this.guildShopItems = response.data.items.map(item => ({
						id: item.id,
						name: item.name,
						description: item.description,
						price: item.price,
						currency: item.currency,
						canBuy: item.can_buy,
						reasons: item.reasons || []
					}))
				} else {
					console.error('加载门派商店失败:', response.data?.message)
				}
			} catch (error) {
				console.error('加载门派商店失败:', error)
			}
		},

		getPositionName(position) {
			const positions = {
				'master': '掌门',
				'elder': '长老',
				'disciple': '弟子',
				'outer': '外门弟子'
			}
			return positions[position] || '弟子'
		},
		
		showGuildList() {
			this.showGuildListModal = true
		},
		
		closeGuildList() {
			this.showGuildListModal = false
		},
		
		async selectGuild(guild) {
			if (!guild.can_join) {
				const reasons = guild.reasons.join('，')
				uni.showToast({
					title: `无法加入：${reasons}`,
					icon: 'none'
				})
				return
			}

			uni.showModal({
				title: '确认加入',
				content: `确定要加入 ${guild.name} 吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const response = await gameUtils.sendMessage({
								type: 'sect_action',
								data: {
									action: 'join_sect',
									sect_id: guild.id
								}
							})

							if (response.type === 'join_sect_success') {
								this.closeGuildList()
								uni.showToast({
									title: response.data.message,
									icon: 'success'
								})
								// 重新加载门派信息
								await this.loadSectInfo()
							} else {
								uni.showToast({
									title: response.data?.message || '加入门派失败',
									icon: 'none'
								})
							}
						} catch (error) {
							console.error('加入门派失败:', error)
							uni.showToast({
								title: '加入门派失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		async showTasks() {
			this.showTasksSection = true
			this.hideOtherSections()
			await this.loadSectQuests()
		},
		
		hideTasks() {
			this.showTasksSection = false
		},
		
		async showSkills() {
			this.showSkillsSection = true
			this.hideOtherSections()
			await this.loadSectSkills()
		},
		
		hideSkills() {
			this.showSkillsSection = false
		},
		
		async showMembers() {
			this.showMembersSection = true
			this.hideOtherSections()
			await this.loadSectMembers()
		},
		
		hideMembers() {
			this.showMembersSection = false
		},
		
		async showShop() {
			this.showShopSection = true
			this.hideOtherSections()
			await this.loadSectShop()
		},
		
		hideShop() {
			this.showShopSection = false
		},
		
		hideOtherSections() {
			this.showTasksSection = false
			this.showSkillsSection = false
			this.showMembersSection = false
			this.showShopSection = false
		},
		
		getDifficultyClass(difficulty) {
			const classes = {
				'easy': 'difficulty-easy',
				'medium': 'difficulty-medium',
				'hard': 'difficulty-hard'
			}
			return classes[difficulty] || 'difficulty-easy'
		},
		
		getDifficultyName(difficulty) {
			const names = {
				'easy': '简单',
				'medium': '中等',
				'hard': '困难'
			}
			return names[difficulty] || '简单'
		},
		
		getSkillTypeName(type) {
			const types = {
				'external': '外功',
				'internal': '内功',
				'light': '轻功',
				'heart': '心法',
				'special': '特技'
			}
			return types[type] || '武功'
		},
		
		canAcceptTask(task) {
			// 检查其他要求（如果有的话）
			return true
		},
		
		canLearnSkill(skill) {
			return skill.can_learn
		},
		
		canBuyItem(item) {
			return this.playerGuild.contribution >= item.price
		},
		
		showTaskDetail(task) {
			this.selectedTask = task
			this.showTaskDetail = true
		},
		
		closeTaskDetail() {
			this.showTaskDetail = false
			this.selectedTask = null
		},
		
		acceptTask(task) {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}
			if (!this.canAcceptTask(task)) {
				uni.showToast({
					title: '不满足任务要求',
					icon: 'none'
				})
				return
			}
			
			uni.showModal({
				title: '接受任务',
				content: `确定要接受任务 "${task.name}" 吗？`,
				success: (res) => {
					if (res.confirm) {
						// 这里应该将任务添加到玩家的任务列表中
						uni.showToast({
							title: '任务已接受',
							icon: 'success'
						})
						this.closeTaskDetail()
					}
				}
			})
		},
		
		showSkillDetail(skill) {
			// 显示武功详情
		},
		
		async learnSkill(skill) {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}
			if (!skill.can_learn) {
				uni.showToast({
					title: skill.reason || '无法学习',
					icon: 'none'
				})
				return
			}

			if (skill.learned) {
				// 修炼武功
				uni.showToast({
					title: '开始修炼',
					icon: 'success'
				})
			} else {
				// 学习武功
				uni.showModal({
					title: '学习武功',
					content: `确定要学习 ${skill.name} 吗？\n消耗贡献: ${skill.contribution_requirement}`,
					success: async (res) => {
						if (res.confirm) {
							try {
								const response = await gameUtils.sendMessage({
									type: 'sect_action',
									data: {
										action: 'learn_sect_skill',
										skill_name: skill.name
									}
								})

								if (response.type === 'learn_skill_success') {
									uni.showToast({
										title: response.data.message,
										icon: 'success'
									})
									// 重新加载门派信息和武功列表
									await this.loadSectInfo()
									await this.loadSectSkills()
								} else {
									uni.showToast({
										title: response.data?.message || '学习失败',
										icon: 'none'
									})
								}
							} catch (error) {
								console.error('学习武功失败:', error)
								uni.showToast({
									title: '学习失败',
									icon: 'none'
								})
							}
						}
					}
				})
			}
		},
		
		showShopItemDetail(item) {
			// 显示商店物品详情
		},
		
		buyShopItem(item) {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}
			if (!this.canBuyItem(item)) {
				uni.showToast({
					title: '贡献不足',
					icon: 'none'
				})
				return
			}
			uni.showModal({
				title: '购买物品',
				content: `确定要购买 ${item.name} 吗？\n消耗贡献: ${item.price}`,
				success: (res) => {
					if (res.confirm) {
						this.playerGuild.contribution -= item.price
						// 适配字段
						const type = item.type || '';
						const sellable = (typeof item.sellable !== 'undefined' ? item.sellable : true) ? true : false;
						const unique_id = item.unique_id || `${item.id}_${Date.now()}_${Math.floor(Math.random()*10000)}`;
						gameState.addItem({ ...item, type, sellable, unique_id })
						gameState.save()
						uni.showToast({
							title: '购买成功！',
							icon: 'success'
						})
					}
				}
			})
		},

			// 显示排行榜
			async showRankings() {
				this.showRankingsSection = true
				await this.loadSectRankings()
			},

			// 隐藏排行榜
			hideRankings() {
				this.showRankingsSection = false
			},

			// 切换排行榜类型
			async switchRankingType(type) {
				this.currentRankingType = type
				await this.loadSectRankings()
			},

			// 加载门派排行榜
			async loadSectRankings() {
				if (!gameState.isAuthed) {
					return
				}

				try {
					const response = await gameUtils.sendMessage({
						type: 'sect_action',
						data: {
							action: 'get_sect_rankings',
							ranking_type: this.currentRankingType
						}
					})

					if (response.type === 'sect_rankings_success') {
						this.sectRankings = response.data.rankings || []
					} else {
						console.error('加载门派排行榜失败:', response.data?.message)
						uni.showToast({
							title: response.data?.message || '加载排行榜失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('加载门派排行榜失败:', error)
					uni.showToast({
						title: '加载排行榜失败',
						icon: 'none'
					})
				}
			},

			// 显示战争
			async showWars() {
				this.showWarsSection = true
				await this.loadSectWars()
			},

			// 隐藏战争
			hideWars() {
				this.showWarsSection = false
			},

			// 加载门派战争
			async loadSectWars() {
				if (!gameState.isAuthed) {
					return
				}

				try {
					const response = await gameUtils.sendMessage({
						type: 'sect_action',
						data: { action: 'get_sect_wars' }
					})

					if (response.type === 'sect_wars_success') {
						this.sectWars = response.data.wars || []
					} else {
						console.error('加载门派战争失败:', response.data?.message)
					}
				} catch (error) {
					console.error('加载门派战争失败:', error)
				}
			},

			// 显示宣战弹窗
			showDeclareWarModal() {
				this.showDeclareWarModal = true
				this.loadAvailableTargetSects()
			},

			// 关闭宣战弹窗
			closeDeclareWar() {
				this.showDeclareWarModal = false
				this.selectedTargetSect = null
				this.selectedTargetSectIndex = 0
				this.warReason = ''
			},

			// 加载可宣战的门派
			async loadAvailableTargetSects() {
				// 获取所有门派，排除自己的门派
				const allSects = Object.values(this.sectInfo?.sect_config?.sects || {})
				this.availableTargetSects = allSects.filter(sect =>
					sect.id !== this.sectInfo?.sect_id
				).map(sect => ({
					id: sect.id,
					name: sect.name
				}))
			},

			// 选择目标门派
			onTargetSectChange(e) {
				const index = e.detail.value
				this.selectedTargetSectIndex = index
				this.selectedTargetSect = this.availableTargetSects[index]
			},

			// 确认宣战
			async confirmDeclareWar() {
				if (!this.selectedTargetSect || !this.warReason.trim()) {
					return
				}

				try {
					const response = await gameUtils.sendMessage({
						type: 'sect_action',
						data: {
							action: 'declare_war',
							target_sect_id: this.selectedTargetSect.id,
							war_reason: this.warReason.trim()
						}
					})

					if (response.type === 'declare_war_success') {
						uni.showToast({
							title: response.data.message,
							icon: 'success'
						})
						this.closeDeclareWar()
						await this.loadSectWars()
					} else {
						uni.showToast({
							title: response.data?.message || '宣战失败',
							icon: 'none'
						})
					}
				} catch (error) {
					uni.showToast({
						title: '宣战失败: ' + error.message,
						icon: 'none'
					})
				}
			},

			// 获取战争状态文本
			getWarStatusText(status) {
				const statusMap = {
					'declared': '已宣战',
					'active': '战争中',
					'ended': '已结束'
				}
				return statusMap[status] || '未知'
			},

			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return ''
				const date = new Date(timeStr)
				return date.toLocaleString('zh-CN')
			},

			// 显示建设
			async showBuildings() {
				this.showBuildingsSection = true
				await this.loadSectBuildings()
			},

			// 隐藏建设
			hideBuildings() {
				this.showBuildingsSection = false
			},

			// 加载门派建筑
			async loadSectBuildings() {
				if (!gameState.isAuthed) {
					return
				}

				try {
					const response = await gameUtils.sendMessage({
						type: 'sect_action',
						data: { action: 'get_sect_buildings' }
					})

					if (response.type === 'sect_buildings_success') {
						this.sectBuildings = response.data.buildings || {}
						this.sectResources = response.data.resources || {}
					} else {
						console.error('加载门派建筑失败:', response.data?.message)
					}
				} catch (error) {
					console.error('加载门派建筑失败:', error)
				}
			},

			// 升级建筑
			async upgradeBuilding(buildingType) {
				if (!this.canUpgradeBuilding(buildingType)) {
					return
				}

				try {
					const response = await gameUtils.sendMessage({
						type: 'sect_action',
						data: {
							action: 'upgrade_building',
							building_type: buildingType
						}
					})

					if (response.type === 'upgrade_building_success') {
						uni.showToast({
							title: response.data.message,
							icon: 'success'
						})
						await this.loadSectBuildings()
					} else {
						uni.showToast({
							title: response.data?.message || '升级失败',
							icon: 'none'
						})
					}
				} catch (error) {
					uni.showToast({
						title: '升级失败: ' + error.message,
						icon: 'none'
					})
				}
			},

			// 检查是否可以升级建筑
			canUpgradeBuilding(buildingType) {
				const building = this.sectBuildings[buildingType]
				if (!building) return false

				const upgradeCost = this.getBuildingUpgradeCost(buildingType, building.level + 1)
				if (!upgradeCost) return false

				// 检查资源是否足够
				for (const [resource, cost] of Object.entries(upgradeCost)) {
					if ((this.sectResources[resource] || 0) < cost) {
						return false
					}
				}

				return true
			},

			// 获取建筑升级成本
			getBuildingUpgradeCost(buildingType, level) {
				const baseCosts = {
					'main_hall': { wood: 100, stone: 150, iron: 50, gold: 200 },
					'training_ground': { wood: 80, stone: 100, iron: 120, gold: 150 },
					'library': { wood: 120, stone: 80, iron: 60, gold: 180 },
					'warehouse': { wood: 60, stone: 120, iron: 80, gold: 100 }
				}

				const baseCost = baseCosts[buildingType]
				if (!baseCost) return null

				// 每级成本递增50%
				const multiplier = Math.pow(1.5, level - 1)

				const cost = {}
				for (const [resource, baseCostValue] of Object.entries(baseCost)) {
					cost[resource] = Math.floor(baseCostValue * multiplier)
				}

				return cost
			},

			// 获取资源名称
			getResourceName(resource) {
				const names = {
					'wood': '木材',
					'stone': '石料',
					'iron': '铁矿',
					'gold': '金币'
				}
				return names[resource] || resource
			}
		}
	}
</script>

<style scoped>
.container {
	padding: 20rpx;
	padding-bottom: 140rpx; /* 为tabBar留出空间 */
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}

.guild-info {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.guild-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.guild-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.guild-level {
	font-size: 28rpx;
	color: #667eea;
	background: #f0f4ff;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.guild-stats {
	display: flex;
	gap: 30rpx;
}

.stat-item {
	display: flex;
	align-items: center;
}

.stat-label {
	font-size: 26rpx;
	color: #666;
	margin-right: 10rpx;
}

.stat-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.guild-actions {
	margin-top: 20rpx;
	text-align: center;
}

.daily-reward-btn {
	background: linear-gradient(135deg, #f39c12, #e67e22);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 15rpx 30rpx;
	font-size: 26rpx;
}

.daily-reward-btn[disabled] {
	background: #ccc;
	color: #666;
}

.no-guild {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 60rpx 30rpx;
	text-align: center;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.no-guild-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.no-guild-desc {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 40rpx;
}

.join-guild-btn {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.guild-functions {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.function-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.function-item {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
	text-align: center;
	transition: all 0.3s;
}

.function-item:active {
	background: #e9ecef;
	transform: scale(0.98);
}

.function-icon {
	font-size: 48rpx;
	display: block;
	margin-bottom: 15rpx;
}

.function-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.tasks-section,
.skills-section,
.members-section,
.shop-section,
.rankings-section,
.wars-section,
.buildings-section {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.98);
	z-index: 1000;
	padding: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section-close {
	font-size: 40rpx;
	color: #999;
	line-height: 1;
}

.tasks-list,
.skills-list,
.members-list,
.shop-list {
	height: calc(100vh - 120rpx);
}

.task-item,
.skill-item,
.member-item,
.shop-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: white;
	border-radius: 15rpx;
	margin-bottom: 15rpx;
}

.task-info,
.skill-info,
.member-info,
.item-info {
	flex: 1;
}

.task-name,
.skill-name,
.member-name,
.item-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.task-desc,
.skill-desc,
.item-desc {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}

.task-reward {
	font-size: 24rpx;
	color: #f39c12;
	display: block;
}

.skill-type {
	font-size: 24rpx;
	color: #667eea;
	display: block;
	margin-bottom: 8rpx;
}

.member-position,
.member-level {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.task-status,
.skill-status,
.member-contribution {
	text-align: center;
}

.task-difficulty {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	display: block;
	margin-bottom: 10rpx;
}

.difficulty-easy {
	background: #d4edda;
	color: #155724;
}

.difficulty-medium {
	background: #fff3cd;
	color: #856404;
}

.difficulty-hard {
	background: #f8d7da;
	color: #721c24;
}

.skill-level {
	font-size: 24rpx;
	color: #667eea;
	display: block;
	margin-bottom: 10rpx;
}

.contribution-label {
	font-size: 24rpx;
	color: #666;
	margin-right: 10rpx;
}

.contribution-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #f39c12;
}

.accept-task-btn,
.learn-skill-btn,
.buy-item-btn {
	background: #27ae60;
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 12rpx 24rpx;
	font-size: 26rpx;
}

.accept-task-btn[disabled],
.learn-skill-btn[disabled],
.buy-item-btn[disabled] {
	opacity: 0.5;
	background: #ccc;
}

.empty-tasks {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}

.modal-content {
	background: white;
	border-radius: 20rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 40rpx;
	color: #999;
	line-height: 1;
}

.modal-body {
	padding: 30rpx;
	max-height: 400rpx;
	overflow-y: auto;
}

.guild-option {
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.guild-option:last-child {
	border-bottom: none;
}

.guild-option-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.guild-option-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.guild-option-requirement {
	font-size: 24rpx;
	color: #e74c3c;
	display: block;
}

.guild-option-available {
	font-size: 24rpx;
	color: #27ae60;
	display: block;
}

.guild-option-disabled {
	opacity: 0.6;
	background: #f8f9fa;
}

.detail-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.detail-desc,
.detail-requirement,
.detail-reward {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.modal-footer {
	display: flex;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
	gap: 20rpx;
}

.modal-btn {
	flex: 1;
	padding: 20rpx;
	border: none;
	border-radius: 15rpx;
	font-size: 28rpx;
}

.cancel-btn {
	background: #f0f0f0;
	color: #666;
}

.confirm-btn {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}

.confirm-btn[disabled] {
	opacity: 0.5;
	background: #ccc;
}

/* 排行榜样式 */
.ranking-tabs {
	display: flex;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 6rpx;
}

.ranking-tab {
	flex: 1;
	text-align: center;
	padding: 16rpx 12rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s;
}

.ranking-tab.active {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	font-weight: bold;
}

.rankings-list {
	height: calc(100vh - 200rpx);
}

.ranking-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
}

.ranking-rank {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #ffd700, #ffb347);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.rank-number {
	font-size: 32rpx;
	font-weight: bold;
	color: white;
}

.ranking-info {
	flex: 1;
}

.sect-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.sect-stats {
	display: flex;
	flex-direction: column;
}

.stat-text {
	font-size: 26rpx;
	color: #666;
}

.no-rankings {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
	font-size: 28rpx;
}

/* 战争样式 */
.war-actions {
	margin-bottom: 20rpx;
	text-align: center;
}

.declare-war-btn {
	background: linear-gradient(135deg, #e74c3c, #c0392b);
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
	font-weight: bold;
}

.wars-list {
	height: calc(100vh - 240rpx);
}

.war-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.war-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.war-sides {
	display: flex;
	align-items: center;
	flex: 1;
}

.sect-name {
	font-size: 28rpx;
	font-weight: bold;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	background: #f0f0f0;
	color: #333;
}

.sect-name.own-sect {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}

.vs-text {
	margin: 0 16rpx;
	font-size: 24rpx;
	color: #e74c3c;
	font-weight: bold;
}

.war-status {
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	font-weight: bold;
}

.status-declared {
	background: #ffeaa7;
	color: #d63031;
}

.status-active {
	background: #fab1a0;
	color: #e17055;
}

.status-ended {
	background: #ddd;
	color: #636e72;
}

.war-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.war-reason,
.war-time {
	font-size: 26rpx;
	color: #666;
}

.war-result {
	display: flex;
	justify-content: space-between;
	margin-top: 8rpx;
}

.winner,
.score {
	font-size: 26rpx;
	font-weight: bold;
	color: #e74c3c;
}

.no-wars {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
	font-size: 28rpx;
}

.war-reason-input {
	width: 100%;
	min-height: 120rpx;
	border: 2rpx solid #ddd;
	border-radius: 8rpx;
	padding: 16rpx;
	font-size: 28rpx;
	resize: none;
}

.picker-display {
	padding: 16rpx;
	border: 2rpx solid #ddd;
	border-radius: 8rpx;
	background: white;
	font-size: 28rpx;
}

/* 建设样式 */
.resources-display {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.resources-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
}

.resources-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 16rpx;
}

.resource-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.resource-icon {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

.resource-name {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 4rpx;
}

.resource-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.buildings-list {
	height: calc(100vh - 300rpx);
}

.building-item {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.building-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.building-info {
	flex: 1;
}

.building-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 4rpx;
}

.building-level {
	font-size: 26rpx;
	color: #666;
}

.upgrade-btn {
	background: linear-gradient(135deg, #52c41a, #389e0d);
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 12rpx 24rpx;
	font-size: 26rpx;
	font-weight: bold;
}

.upgrade-btn[disabled] {
	background: #ccc;
	color: #999;
}

.building-cost {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 16rpx;
}

.cost-title {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.cost-items {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.cost-item {
	font-size: 24rpx;
	color: #333;
	background: #f0f0f0;
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
}

.cost-item.insufficient {
	background: #ffebee;
	color: #e74c3c;
}

.no-buildings {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
	font-size: 28rpx;
}

/* 加入门派指南样式 */
.join-sect-guide {
	max-width: 600rpx;
	max-height: 80vh;
}

.guide-section {
	margin-bottom: 32rpx;
}

.guide-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.guide-subtitle {
	font-size: 28rpx;
	font-weight: bold;
	color: #666;
	margin-bottom: 12rpx;
	display: block;
}

.guide-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	display: block;
}

.guide-list {
	padding-left: 20rpx;
}

.guide-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.8;
	margin-bottom: 8rpx;
	display: block;
}

.sect-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.sect-item {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	border-left: 4rpx solid #667eea;
}

.sect-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.sect-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
	display: block;
}
</style> 