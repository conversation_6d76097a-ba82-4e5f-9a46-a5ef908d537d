#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聊天系统模块
负责处理所有聊天相关功能，包括消息发送、接收、限制等
"""

import time
import json
import logging
import aiosqlite
from datetime import datetime
from typing import Dict, Optional
from websockets.server import WebSocketServerProtocol

logger = logging.getLogger(__name__)


class ChatSystem:
    def __init__(self, db_path: str, game_server):
        self.db_path = db_path
        self.game_server = game_server
        
        # 聊天限制管理
        self.chat_cooldowns = {}  # 用户ID -> 最后发言时间
        self.chat_cooldown_time = 5  # 聊天冷却时间（秒）
        
        # 聊天费用配置
        self.chat_costs = {
            'world': 0,      # 世界聊天免费
            'private': 10,   # 私聊费用10银两
            'rumor': 100,    # 谣言费用100银两
            'sect': 0        # 门派聊天免费
        }

    async def init_chat_tables(self):
        """初始化聊天相关数据表"""
        async with aiosqlite.connect(self.db_path) as db:
            # 创建聊天消息表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS chat_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_id TEXT NOT NULL,
                    sender_name TEXT NOT NULL,
                    content TEXT NOT NULL,
                    chat_type TEXT DEFAULT 'world',
                    target_id TEXT,
                    target_name TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            await db.commit()

    async def get_player_sect(self, user_id: str) -> dict:
        """获取玩家门派信息"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT sm.sect_id, sm.rank, sm.contribution
                    FROM sect_members sm
                    WHERE sm.user_id = ?
                ''', (user_id,)) as cursor:
                    row = await cursor.fetchone()

                if not row:
                    return None

                sect_id, rank, contribution = row

                # 从门派配置中获取门派名称
                sect_config = self.game_server.sect_system.sects_config['sects'].get(sect_id, {})

                return {
                    'sect_id': sect_id,
                    'sect_name': sect_config.get('name', '未知门派'),
                    'rank': rank,
                    'contribution': contribution
                }
        except Exception as e:
            logger.error(f"获取玩家门派信息失败: {e}")
            return None

    async def handle_get_chat_messages(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """获取聊天消息历史"""
        try:
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                return {'type': 'error', 'data': {'message': '未认证'}}

            # 确保聊天表存在
            await self.init_chat_tables()

            # 从数据库获取最近的聊天消息
            async with aiosqlite.connect(self.db_path) as db:
                # 获取最近50条消息
                async with db.execute('''
                    SELECT sender_name, content, chat_type, target_name, created_at
                    FROM chat_messages
                    ORDER BY created_at DESC
                    LIMIT 50
                ''') as cursor:
                    rows = await cursor.fetchall()

                messages = []
                for row in reversed(rows):  # 反转顺序，最新的在最后
                    sender_name, content, chat_type, target_name, created_at = row
                    # 转换时间戳
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    timestamp = int(dt.timestamp() * 1000)  # 转换为毫秒

                    messages.append({
                        'sender': sender_name,
                        'content': content,
                        'chat_type': chat_type or 'world',
                        'target_name': target_name,
                        'time': timestamp
                    })

                return {
                    'type': 'chat_messages_success',
                    'data': {
                        'messages': messages
                    }
                }

        except Exception as e:
            logger.error(f"获取聊天消息失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取聊天消息失败: {str(e)}'}}

    async def handle_send_chat_message(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """发送聊天消息"""
        try:
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                return {'type': 'error', 'data': {'message': '未认证'}}

            player = self.game_server.player_data.get(str(user_id))
            if not player:
                return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

            # 检查聊天冷却时间
            current_time = time.time()
            last_chat_time = self.chat_cooldowns.get(str(user_id), 0)
            time_diff = current_time - last_chat_time
            
            if time_diff < self.chat_cooldown_time:
                remaining_time = self.chat_cooldown_time - time_diff
                return {
                    'type': 'error', 
                    'data': {
                        'message': f'你说话太快了，请等待{remaining_time:.1f}秒！'
                    }
                }

            content = data.get('content', '').strip()
            chat_type = data.get('chat_type', 'world')  # 默认为世界聊天
            target_name = data.get('target_name', '')  # 私聊目标
            
            if not content:
                return {'type': 'error', 'data': {'message': '消息内容不能为空'}}

            if len(content) > 100:
                return {'type': 'error', 'data': {'message': '消息内容过长'}}

            sender_name = player.get('character_name', player.get('name', '匿名'))
            
            # 检查聊天费用
            chat_cost = self.chat_costs.get(chat_type, 0)
            
            # 检查玩家银两是否足够
            if chat_cost > 0:
                player_silver = player.get('silver', 0)
                if player_silver < chat_cost:
                    chat_type_name = '私聊' if chat_type == 'private' else '谣言'
                    return {
                        'type': 'error', 
                        'data': {
                            'message': f'银两不足！发送{chat_type_name}需要{chat_cost}银两'
                        }
                    }

            # 处理不同类型的聊天
            if chat_type == 'rumor':
                # 谣言聊天，发送者显示为"某人"
                display_sender = '某人'
            elif chat_type == 'private':
                # 私聊，需要验证目标玩家
                if not target_name:
                    return {'type': 'error', 'data': {'message': '私聊需要指定目标玩家'}}
                display_sender = sender_name
            elif chat_type == 'sect':
                # 门派聊天，需要验证玩家是否加入门派
                player_sect = await self.get_player_sect(str(user_id))
                if not player_sect:
                    return {'type': 'error', 'data': {'message': '你还没有加入门派，无法使用门派聊天'}}
                display_sender = f"[{player_sect['sect_name']}]{sender_name}"
            else:
                # 世界聊天
                display_sender = sender_name

            # 扣除聊天费用并更新玩家数据
            if chat_cost > 0:
                player['silver'] -= chat_cost
                # 更新数据库中的银两
                async with aiosqlite.connect(self.db_path) as db:
                    await db.execute('''
                        UPDATE players SET silver = ? WHERE user_id = ?
                    ''', (player['silver'], str(user_id)))
                    await db.commit()
                
                # 聊天扣费不记录日志，避免日志膨胀

            # 更新聊天冷却时间
            self.chat_cooldowns[str(user_id)] = current_time

            # 确保聊天表存在
            await self.init_chat_tables()

            # 保存消息到数据库
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT INTO chat_messages (sender_id, sender_name, content, chat_type, target_name)
                    VALUES (?, ?, ?, ?, ?)
                ''', (str(user_id), sender_name, content, chat_type, target_name))
                await db.commit()

            # 构建消息对象
            message_obj = {
                'sender': display_sender,
                'content': content,
                'chat_type': chat_type,
                'target_name': target_name,
                'time': int(datetime.now().timestamp() * 1000)
            }

            # 根据聊天类型决定广播范围
            if chat_type == 'private':
                await self.send_private_message(message_obj, sender_name, target_name)
            elif chat_type == 'sect':
                # 门派聊天只发送给同门派成员
                player_sect = await self.get_player_sect(str(user_id))
                if player_sect:
                    await self.broadcast_sect_message(message_obj, player_sect['sect_id'])
            else:
                await self.broadcast_chat_message(message_obj)

            # 聊天消息不记录到日志，避免日志膨胀

            return {
                'type': 'chat_message_success',
                'data': {
                    'message': '消息发送成功'
                }
            }

        except Exception as e:
            logger.error(f"发送聊天消息失败: {e}")
            return {'type': 'error', 'data': {'message': f'发送失败: {str(e)}'}}

    async def send_private_message(self, message_obj: dict, sender_name: str, target_name: str):
        """发送私聊消息"""
        try:
            # 找到目标玩家
            target_user_id = None
            for uid, player in self.game_server.player_data.items():
                if player.get('character_name') == target_name or player.get('name') == target_name:
                    target_user_id = uid
                    break
            
            if not target_user_id:
                # 目标玩家不存在，只发送给发送者
                sender_ws = None
                for client_ws in self.game_server.clients.values():
                    if getattr(client_ws, 'user_id', None) == getattr(message_obj, 'sender_id', None):
                        sender_ws = client_ws
                        break
                
                if sender_ws:
                    error_msg = {
                        'type': 'chat_message',
                        'data': {
                            'sender': '系统',
                            'content': f'玩家 {target_name} 不存在或不在线',
                            'chat_type': 'system',
                            'time': int(datetime.now().timestamp() * 1000)
                        }
                    }
                    await sender_ws.send(json.dumps(error_msg, ensure_ascii=False))
                return
            
            # 构建私聊消息格式
            private_msg_to_target = {
                'type': 'chat_message',
                'data': {
                    'sender': sender_name,
                    'content': f'{sender_name}悄悄对你说：{message_obj["content"]}',
                    'chat_type': 'private',
                    'target_name': target_name,
                    'time': message_obj['time']
                }
            }
            
            private_msg_to_sender = {
                'type': 'chat_message',
                'data': {
                    'sender': '你',
                    'content': f'你悄悄对{target_name}说：{message_obj["content"]}',
                    'chat_type': 'private',
                    'target_name': target_name,
                    'time': message_obj['time']
                }
            }
            
            # 发送给目标玩家和发送者
            for client_ws in self.game_server.clients.values():
                user_id = getattr(client_ws, 'user_id', None)
                if user_id == target_user_id:
                    await client_ws.send(json.dumps(private_msg_to_target, ensure_ascii=False))
                elif str(user_id) in [uid for uid, player in self.game_server.player_data.items() 
                                     if player.get('character_name') == sender_name or player.get('name') == sender_name]:
                    await client_ws.send(json.dumps(private_msg_to_sender, ensure_ascii=False))
                    
        except Exception as e:
            logger.error(f"发送私聊消息失败: {e}")

    async def broadcast_chat_message(self, message_obj: dict):
        """广播聊天消息给所有在线玩家"""
        try:
            broadcast_data = {
                'type': 'chat_message',
                'data': message_obj
            }

            # 发送给所有连接的客户端
            disconnected_clients = []
            for client_id, client_ws in self.game_server.clients.items():
                try:
                    await client_ws.send(json.dumps(broadcast_data, ensure_ascii=False))
                except Exception as e:
                    logger.warning(f"向客户端 {client_id} 发送聊天消息失败: {e}")
                    disconnected_clients.append(client_id)

            # 清理断开的连接
            for client_id in disconnected_clients:
                if client_id in self.game_server.clients:
                    del self.game_server.clients[client_id]

        except Exception as e:
            logger.error(f"广播聊天消息失败: {e}")

    async def broadcast_sect_message(self, message_obj: dict, sect_id: str):
        """广播门派聊天消息给同门派成员"""
        try:
            broadcast_data = {
                'type': 'chat_message',
                'data': message_obj
            }

            # 获取同门派的所有在线玩家
            sect_members = []
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT user_id FROM sect_members WHERE sect_id = ?
                ''', (sect_id,)) as cursor:
                    rows = await cursor.fetchall()
                    sect_members = [row[0] for row in rows]

            # 发送给同门派的在线玩家
            disconnected_clients = []
            for client_id, client_ws in self.game_server.clients.items():
                try:
                    # 检查客户端是否是门派成员
                    if hasattr(client_ws, 'user_id') and str(client_ws.user_id) in sect_members:
                        await client_ws.send(json.dumps(broadcast_data, ensure_ascii=False))
                except Exception as e:
                    logger.warning(f"向门派成员 {client_id} 发送聊天消息失败: {e}")
                    disconnected_clients.append(client_id)

            # 清理断开的连接
            for client_id in disconnected_clients:
                if client_id in self.game_server.clients:
                    del self.game_server.clients[client_id]

        except Exception as e:
            logger.error(f"广播门派聊天消息失败: {e}")
