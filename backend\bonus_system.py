#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增益计算系统
统一处理装备、武功和境界带来的增益
"""

import logging
import math
from typing import Dict, Any

logger = logging.getLogger(__name__)

class BonusSystem:
    """增益计算系统"""
    
    def __init__(self, game_data: Dict[str, Any]):
        """
        初始化增益系统
        
        Args:
            game_data: 游戏数据，包含物品、武功、地图等配置
        """
        self.game_data = game_data
    
    def calculate_equipment_bonus(self, player: Dict[str, Any]) -> Dict[str, int]:
        """
        计算装备加成
        
        Args:
            player: 玩家数据
            
        Returns:
            装备加成字典
        """
        equipment_bonus = {
            'attack': 0,
            'defense': 0,
            'hp': 0,
            'mp': 0,
            'energy': 0,
            'spirit': 0,
            'energy_regen': 0.0
        }
        
        equipment = player.get('equipment', {})
        for slot, item in equipment.items():
            if item and isinstance(item, dict):
                # 1. 计算直接属性字段
                equipment_bonus['attack'] += item.get('attack', 0)
                equipment_bonus['defense'] += item.get('defense', 0)
                equipment_bonus['hp'] += item.get('hp', 0)
                equipment_bonus['mp'] += item.get('mp', 0)
                equipment_bonus['energy'] += item.get('energy', 0)
                equipment_bonus['spirit'] += item.get('spirit', 0)
                equipment_bonus['energy_regen'] += item.get('energy_regen', 0.0)

                # 2. 解析effects字段（新增）
                effects_string = item.get('effects', '')
                if effects_string and isinstance(effects_string, str):
                    effect_pairs = effects_string.split(',')
                    for pair in effect_pairs:
                        if ':' in pair:
                            attr, val = pair.split(':')
                            attr_name = attr.strip()
                            try:
                                attr_value = float(val.strip())
                                if attr_name in equipment_bonus:
                                    equipment_bonus[attr_name] += attr_value
                            except ValueError:
                                continue
        
        logger.debug(f"装备加成: {equipment_bonus}")
        return equipment_bonus
    
    def calculate_martial_bonus(self, player: Dict[str, Any]) -> Dict[str, int]:
        """
        计算武功被动增益（wugong.txt基础值，等级平方加成）
        Args:
            player: 玩家数据
        Returns:
            武功加成字典
        """
        def nonlinear_bonus(base, level, growth=0):
            # 平方型：base × (1 + level^2 / 100) + level × growth
            return int(base * (1 + (level ** 2) / 100) + level * growth)

        martial_bonus = {
            'attack': 0,
            'defense': 0,
            'hp': 0,
            'mp': 0,
            'energy': 0,
            'strength': 0,      # 力量
            'intelligence': 0,  # 悟性
            'agility': 0,       # 身法
            'constitution': 0,  # 根骨
            'dodge': 0          # 闪避
        }
        wugong_config = self.game_data.get('wugong', {})
        logger.debug(f"wugong_config keys: {list(wugong_config.keys())}")
        martial_skills = player.get('martial_skills', {})
        # 兼容dict和list
        if isinstance(martial_skills, dict):
            skills_iter = martial_skills.values()
        else:
            skills_iter = martial_skills
        for skill in skills_iter:
            martial_name = skill['name']
            info = skill
            if info.get('equipped', False):
                level = info.get('level', 0)
                wcfg = wugong_config.get(martial_name)
                logger.debug(f"当前装备武功: {martial_name}, wcfg: {wcfg}")
                if not wcfg:
                    continue
                for key, bonus_key in [
                    ('攻击', 'attack'),
                    ('防御', 'defense'),
                    ('血量', 'hp'),
                    ('内力', 'mp'),
                    ('精力', 'energy'),
                    ('力量', 'strength'),
                    ('悟性', 'intelligence'),
                    ('身法', 'agility'),
                    ('根骨', 'constitution'),
                    ('闪避', 'dodge')
                ]:
                    base = int(wcfg.get(key, 0))
                    grow = int(wcfg.get(f'{key}成长', 0))
                    bonus_value = nonlinear_bonus(base, level, grow)
                    martial_bonus[bonus_key] += bonus_value
                    logger.debug(f"武功 {martial_name} 等级 {level}: {key}基础{base}, 成长{grow}, 增益{bonus_value}")
                # 类型加成逻辑（已调整：天赋属性不在此处加成）
                category = wcfg.get('类型') or wcfg.get('type')
                if category == '拳法':
                    martial_bonus['attack'] += int(level * 1.5)
                elif category == '剑法':
                    martial_bonus['attack'] += int(level * 2)
                elif category == '轻功':
                    martial_bonus['defense'] += int(level * 1.5)
                    martial_bonus['dodge'] += int(level * 1.2)
                elif category == '内功':
                    martial_bonus['hp'] += int(level * 3)
                    martial_bonus['mp'] += int(level * 2)
                    martial_bonus['energy'] += int(level * 1.5)  # 精力
                    martial_bonus['spirit'] = martial_bonus.get('spirit', 0) + int(level * 1.5)  # 体力
        logger.debug(f"武功加成: {martial_bonus}")
        return martial_bonus
    
    def calculate_base_stats(self, player: Dict[str, Any], equipment_bonus: Dict[str, int], martial_bonus: Dict[str, int]) -> Dict[str, int]:
        """
        计算基础属性（装备+武功，不含境界）
        
        Args:
            player: 玩家数据
            equipment_bonus: 装备加成
            martial_bonus: 武功加成
            
        Returns:
            基础属性字典
        """
        base_stats = {
            'attack': 10 + equipment_bonus['attack'] + martial_bonus['attack'],
            'defense': 5 + equipment_bonus['defense'] + martial_bonus['defense'],
            'max_hp': 100 + equipment_bonus['hp'] + martial_bonus['hp'],
            'max_mp': 50 + equipment_bonus['mp'] + martial_bonus['mp'],
            'max_energy': 100 + equipment_bonus['energy'] + martial_bonus['energy']
        }
        
        logger.debug(f"基础属性: {base_stats}")
        return base_stats
    
    def apply_realm_bonuses(self, player: Dict[str, Any], base_stats: Dict[str, int]) -> Dict[str, Any]:
        """
        应用境界增益
        
        Args:
            player: 玩家数据
            base_stats: 基础属性
            
        Returns:
            更新后的玩家数据
        """
        try:
            from realm_system import RealmSystem
            
            # 保存基础属性（用于境界增益计算）
            player['base_attack'] = base_stats['attack']
            player['base_defense'] = base_stats['defense']
            player['base_max_hp'] = base_stats['max_hp']
            player['base_max_mp'] = base_stats['max_mp']
            player['base_max_energy'] = base_stats['max_energy']
            
            # 应用境界增益
            player = RealmSystem.apply_realm_bonuses(player)
            
            logger.debug(f"境界增益后属性: attack={player.get('attack')}, defense={player.get('defense')}, max_hp={player.get('max_hp')}")
            return player
        
        except ImportError as e:
            logger.error(f"无法导入境界系统: {e}")
            # 如果境界系统不可用，直接使用基础属性
            player['attack'] = base_stats['attack']
            player['defense'] = base_stats['defense']
            player['max_hp'] = base_stats['max_hp']
            player['max_mp'] = base_stats['max_mp']
            player['max_energy'] = base_stats['max_energy']
            return player

    def apply_talent_bonuses(self, player: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用天赋属性增益到战斗属性
        """
        import math
        
        # 获取天赋属性值
        talent = player.get('talent', {})
        strength = talent.get('力量', 15)  # 基础力量15点
        intelligence = talent.get('悟性', 15)  # 基础悟性15点
        agility = talent.get('身法', 15)  # 基础身法15点
        constitution = talent.get('根骨', 15)  # 基础根骨15点
        
        # 1. 力量增益攻击力
        strength_bonus = max(0, strength - 15) * 0.003  # 每点力量+0.3%攻击力
        strength_multiplier = 1.0 + strength_bonus
        
        # 2. 悟性增益武功经验获取
        intelligence_bonus = max(0, intelligence - 15) * 0.005  # 每点悟性+0.5%经验获取
        intelligence_multiplier = 1.0 + intelligence_bonus
        
        # 3. 身法增益防御和闪避
        agility_defense_bonus = max(0, agility - 15) * 0.005  # 每点身法+0.5%防御力
        agility_dodge_bonus = max(0, agility - 15) * 0.003  # 每点身法+0.3%闪避
        agility_defense_multiplier = 1.0 + agility_defense_bonus
        agility_dodge_multiplier = 1.0 + agility_dodge_bonus
        
        # 4. 根骨增益气血和体力恢复
        constitution_hp_bonus = max(0, constitution - 15) * 0.005  # 每点根骨+0.5%最大气血
        constitution_hp_multiplier = 1.0 + constitution_hp_bonus
        
        # 应用增益到玩家属性（保留1位小数）
        current_attack = player.get('attack', 10)
        current_defense = player.get('defense', 5)
        current_dodge = player.get('dodge', 1)
        current_max_hp = player.get('max_hp', 100)
        
        # 应用力量增益到攻击力
        player['attack'] = round(current_attack * strength_multiplier, 1)
        
        # 应用身法增益到防御力和闪避
        player['defense'] = round(current_defense * agility_defense_multiplier, 1)
        # 闪避增益已经在 apply_bonuses_to_player 中处理，这里不再重复应用
        # player['dodge'] = round(current_dodge * agility_dodge_multiplier, 1)
        
        # 应用根骨增益到最大气血
        player['max_hp'] = round(current_max_hp * constitution_hp_multiplier, 1)
        
        # 保存天赋增益信息（用于前端显示，保留1位小数）
        player['talent_bonuses'] = {
            'strength': {
                'value': strength,
                'bonus_percentage': round(strength_bonus * 100, 1),
                'attack_bonus': round(player['attack'] - current_attack, 1)
            },
            'intelligence': {
                'value': intelligence,
                'bonus_percentage': round(intelligence_bonus * 100, 1),
                'exp_multiplier': round(intelligence_multiplier, 2)
            },
            'agility': {
                'value': agility,
                'defense_bonus_percentage': round(agility_defense_bonus * 100, 1),
                'dodge_bonus_percentage': round(agility_dodge_bonus * 100, 1),
                'defense_bonus': round(player['defense'] - current_defense, 1),
                'dodge_bonus': round(agility_dodge_bonus * 100, 1)  # 只显示百分比增益
            },
            'constitution': {
                'value': constitution,
                'hp_bonus_percentage': round(constitution_hp_bonus * 100, 1),
                'hp_bonus': round(player['max_hp'] - current_max_hp, 1)
                # 根骨增益详情在体力恢复系统中计算
            }
        }
        
        return player
    
    def apply_bonuses_to_player(self, player: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用所有增益到玩家数据
        """
        logger.info("开始计算玩家增益...")

        # 1. 计算装备加成
        equipment_bonus = self.calculate_equipment_bonus(player)

        # 2. 计算武功加成
        martial_bonus = self.calculate_martial_bonus(player)

        # 3. 计算基础属性（装备+武功，不含境界和天赋）
        base_stats = {
            'attack': 10 + equipment_bonus['attack'] + martial_bonus['attack'],
            'defense': 5 + equipment_bonus['defense'] + martial_bonus['defense'],
            'max_hp': 100 + equipment_bonus['hp'] + martial_bonus['hp'],
            'max_mp': 50 + equipment_bonus['mp'] + martial_bonus['mp'],
            'max_energy': 100 + equipment_bonus['energy'] + martial_bonus['energy']
        }

        # 4. 应用境界加成（只用base_stats作为基数）
        player['attack'] = base_stats['attack']
        player['defense'] = base_stats['defense']
        player['max_hp'] = base_stats['max_hp']
        player['max_mp'] = base_stats['max_mp']
        player['max_energy'] = base_stats['max_energy']
        # 精力值受装备影响
        player['max_spirit'] = 100 + equipment_bonus['spirit']

        # 保存基础属性（用于境界增益计算）
        player['base_attack'] = base_stats['attack']
        player['base_defense'] = base_stats['defense']
        player['base_max_hp'] = base_stats['max_hp']
        player['base_max_mp'] = base_stats['max_mp']
        player['base_max_energy'] = base_stats['max_energy']

        # 应用境界增益
        player = self.apply_realm_bonuses(player, base_stats)

        # ====== 动态天赋加点逻辑 ======
        # 1. 取原始天赋
        base_talent = player.get('talent', {}).copy() if player.get('talent') else {'力量': 15, '悟性': 15, '身法': 15, '根骨': 15}
        # 2. 计算空手类基础武功最高等级
        fist_skills = ['基本拳法', '基本掌法', '基本腿法', '基本爪法', '基本指法', '基本手法']
        martial_skills = player.get('martial_skills', {})
        if isinstance(martial_skills, list):
            martial_skills = {entry['name']: entry for entry in martial_skills if 'name' in entry}
        max_fist_level = 0
        for name in fist_skills:
            skill = martial_skills.get(name)
            if skill:
                max_fist_level = max(max_fist_level, skill.get('level', 0))
        add_strength = max_fist_level // 10
        # 3. 基本轻功
        add_agility = martial_skills.get('基本轻功', {}).get('level', 0) // 10
        # 4. 基本内功
        add_constitution = martial_skills.get('基本内功', {}).get('level', 0) // 10
        # 5. 读书写字
        add_intelligence = martial_skills.get('读书写字', {}).get('level', 0) // 10
        # 6. 叠加到天赋
        player['talent']['力量'] = base_talent.get('力量', 15) + add_strength
        player['talent']['身法'] = base_talent.get('身法', 15) + add_agility
        player['talent']['根骨'] = base_talent.get('根骨', 15) + add_constitution
        player['talent']['悟性'] = base_talent.get('悟性', 15) + add_intelligence
        # ====== 动态天赋加点逻辑结束 ======

        # 5. 应用天赋属性增益（只用“境界加成后”的属性作为基数）
        player = self.apply_talent_bonuses(player)

        # 6. 对数型身法闪避成长
        import math
        base_dodge = 1.0  # 15点身法时为1%
        talent = player.get('talent', {})
        agility = talent.get('身法', 15)
        k = 1.5  # 成长系数，可调整
        if agility > 15:
            dodge_bonus = math.log2(agility - 14) * k
        else:
            dodge_bonus = 0.0
        player['dodge'] = base_dodge + dodge_bonus + martial_bonus['dodge']
        player['base_dodge'] = player['dodge']
        # 限制闪避最大值（不超过40%）
        player['dodge'] = min(player['dodge'], 40.0)

        # 7. 能量恢复率
        player['energy_regen_rate'] = 0.1 + equipment_bonus['energy_regen']

        # 8. 血量/蓝量不超过最大值（战斗中不修改当前血量，避免异常回复）
        player_status = player.get('status', 'normal')
        if player_status not in ['battle', 'fighting']:
            if player.get('hp', 0) > player['max_hp']:
                player['hp'] = player['max_hp']
            if player.get('mp', 0) > player['max_mp']:
                player['mp'] = player['max_mp']
        else:
            logger.info(f"玩家处于战斗状态({player_status})，跳过血量/蓝量调整")

        logger.info(f"增益计算完成 - 最终属性: attack={player.get('attack')}, defense={player.get('defense')}, max_hp={player.get('max_hp')}, dodge={player.get('dodge')}")
        return player
    
    def get_bonus_summary(self, player: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取增益摘要信息
        
        Args:
            player: 玩家数据
            
        Returns:
            增益摘要字典
        """
        equipment_bonus = self.calculate_equipment_bonus(player)
        martial_bonus = self.calculate_martial_bonus(player)
        
        return {
            'equipment': equipment_bonus,
            'martial': martial_bonus,
            'total': {
                'attack': equipment_bonus['attack'] + martial_bonus['attack'],
                'defense': equipment_bonus['defense'] + martial_bonus['defense'],
                'hp': equipment_bonus['hp'] + martial_bonus['hp'],
                'mp': equipment_bonus['mp'] + martial_bonus['mp'],
                'energy': equipment_bonus['energy'] + martial_bonus['energy'],
                'spirit': equipment_bonus['spirit'] + martial_bonus.get('spirit', 0)
            }
        } 