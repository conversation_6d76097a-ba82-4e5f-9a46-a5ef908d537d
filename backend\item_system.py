import json
import random
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
from realm_system import RealmSystem
import uuid
import os
import glob
import logging

# 中英文 type/quality 映射表
TYPE_MAP = {
    '防具': 'armor', '头盔': 'helmet', '项链': 'necklace', '衣服': 'armor', '披风': 'cloak', '裤子': 'pants', '鞋子': 'shoes',
    '手镯': 'bracelet', '戒指': 'ring', '武器': 'weapon', '盾牌': 'shield',
    '消耗品': 'consumable', '药品': 'medicine', '丹药': 'pill',
    '材料': 'material', '矿石': 'ore', '木材': 'wood', '草药': 'herb', '兽皮': 'fur',
    '工具': 'tool', '矿镐': 'pickaxe', '斧头': 'axe', '镰刀': 'sickle', '小刀': 'knife',
    '特殊': 'special', '任务': 'quest', '货币': 'currency',
    '采集工具': 'gather_tool', '饰品': 'accessory', '书籍': 'book',
    '采集品': 'gather_item',
}
QUALITY_MAP = {
    '普通': 'common', '精品': 'uncommon', '稀有': 'rare', '史诗': 'epic', '传说': 'legendary', '仙品': 'mythic',
    'common': 'common', 'uncommon': 'uncommon', 'rare': 'rare', 'epic': 'epic', 'legendary': 'legendary', 'mythic': 'mythic'
}

class ItemType(Enum):
    """物品类型枚举"""
    # 装备类
    WEAPON = "weapon"           # 武器
    HELMET = "helmet"           # 头盔
    NECKLACE = "necklace"       # 项链
    ARMOR = "armor"             # 衣服
    CLOAK = "cloak"             # 披风
    PANTS = "pants"             # 裤子
    SHOES = "shoes"             # 鞋子
    BRACELET = "bracelet"       # 手镯
    RING = "ring"               # 戒指
    SHIELD = "shield"           # 盾牌
    MEDAL = "medal"             # 勋章
    OFF_HAND = "off_hand"       # 副手装备（如护符、盾牌等）
    
    # 消耗品类
    CONSUMABLE = "consumable"   # 消耗品
    MEDICINE = "medicine"       # 药品
    PILL = "pill"               # 丹药
    
    # 材料类
    MATERIAL = "material"       # 材料
    ORE = "ore"                 # 矿石
    WOOD = "wood"               # 木材
    HERB = "herb"               # 草药
    FUR = "fur"                 # 兽皮
    GATHER_ITEM = "gather_item" # 采集品
    
    # 工具类
    TOOL = "tool"               # 工具
    PICKAXE = "pickaxe"         # 矿镐
    AXE = "axe"                 # 斧头
    SICKLE = "sickle"           # 镰刀
    KNIFE = "knife"             # 小刀
    GATHER_TOOL = "gather_tool" # 采集工具
    ACCESSORY = "accessory"     # 饰品
    BOOK = "book"               # 书籍
    
    # 特殊类
    SPECIAL = "special"         # 特殊物品
    QUEST = "quest"             # 任务物品
    CURRENCY = "currency"       # 货币

class ItemQuality(Enum):
    """物品品质枚举"""
    COMMON = "common"           # 凡品
    UNCOMMON = "uncommon"       # 精品
    RARE = "rare"               # 珍品
    EPIC = "epic"               # 极品
    LEGENDARY = "legendary"     # 神品
    MYTHIC = "mythic"           # 仙品

@dataclass
class ItemEffect:
    """物品效果"""
    type: str                   # 效果类型
    value: int                  # 效果数值
    description: str            # 效果描述

@dataclass
class Item:
    """物品数据类"""
    id: str                     # 物品ID
    name: str                   # 物品名称
    type: ItemType              # 物品类型
    quality: ItemQuality        # 物品品质
    description: str            # 物品描述
    icon: str                   # 物品图标
    price: int                  # 物品价格
    sell_price: int = 0         # 出售价格，默认0
    stackable: bool = True      # 是否可堆叠
    max_stack: int = 999        # 最大堆叠数量
    
    # 装备属性
    attack: int = 0             # 攻击力
    defense: int = 0            # 防御力
    hp: int = 0                 # 生命值
    mp: int = 0                 # 内力值
    energy: int = 0             # 精力值
    spirit: int = 0             # 精力最大值
    energy_regen: float = 0.0   # 体力恢复速度
    
    # 使用效果
    effects: List[ItemEffect] = None
    
    # 合成相关
    craftable: bool = False     # 是否可合成
    recipe: Dict[str, int] = None  # 合成配方
    
    gather_times: int = 1           # 采集次数（仅采集工具用）
    
    is_sellable: bool = True      # 是否可出售
    
    level: int = 0               # 物品等级（如采集品、装备等）
    
    equip_require: dict = None   # 装备条件（如等级、门派、性别等）

    # 自定义数据字段
    data: dict = None            # 自定义数据（如门派令牌的sect_token）
    sect_token: str = None       # 门派令牌字段

    def __post_init__(self):
        if self.effects is None:
            self.effects = []
        if self.equip_require is None:
            self.equip_require = {}
        if self.data is None:
            self.data = {}
        if not hasattr(self, 'sell_price') or self.sell_price is None:
            self.sell_price = 0

class ItemSystem:
    """物品系统管理类"""
    
    def __init__(self):
        self.items: Dict[str, Item] = {}
        self.item_categories = {
            "weapons": [],
            "armor": [],
            "accessories": [],
            "consumables": [],
            "materials": [],
            "tools": [],
            "special": []
        }
        self.init_items()
    
    def init_items(self):
        """初始化所有物品数据，自动加载 items_*.json 配置"""
        # 1. 先加载内置物品（如有）
        self._init_weapons()
        self._init_armor()
        self._init_accessories()
        self._init_consumables()
        self._init_materials()
        self._init_tools()
        self._init_special_items()
        # 2. 自动加载 items_*.json 配置
        self._load_items_from_json()
        # 3. 构建分类索引
        self._build_category_index()
    
    def _load_items_from_json(self):
        """自动加载 backend/items_*.json 文件，合并进 self.items"""
        import glob
        base_dir = os.path.dirname(__file__)
        items_files = glob.glob(os.path.join(base_dir, 'items_*.json'))
        for file_path in items_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if isinstance(data, list):
                    for entry in data:
                        # desc 字段兼容
                        if 'desc' in entry and 'description' not in entry:
                            entry['description'] = entry.pop('desc')
                        # 只要有 id、name、type、quality、description
                        if 'id' in entry and 'name' in entry and 'type' in entry and 'quality' in entry and 'description' in entry:
                            try:
                                # 处理合成配方
                                recipe = None
                                if entry.get('craft_recipe'):
                                    try:
                                        # 解析 craft_recipe 字符串，格式如 "item_id:quantity,item_id2:quantity2"
                                        recipe_str = entry['craft_recipe']
                                        if recipe_str:
                                            recipe = {}
                                            for part in recipe_str.split(','):
                                                if ':' in part:
                                                    item_id, quantity = part.strip().split(':', 1)
                                                    recipe[item_id.strip()] = int(quantity.strip())
                                    except Exception as e:
                                        logging.warning(f'[物品系统] 解析合成配方失败 {entry.get("id")}: {e}')
                                
                                item = Item(
                                    id=entry['id'],
                                    name=entry['name'],
                                    type=ItemType(entry['type']),
                                    quality=ItemQuality(entry['quality']),
                                    description=entry['description'],
                                    icon=entry.get('icon', ''),
                                    price=entry.get('price', 0),
                                    sell_price=entry.get('sell_price', 0),
                                    stackable=bool(entry.get('stackable', 1)),
                                    max_stack=int(entry.get('max_stack', 999)),
                                    attack=entry.get('attack', 0),
                                    defense=entry.get('defense', 0),
                                    hp=entry.get('hp', 0),
                                    mp=entry.get('mp', 0),
                                    energy=entry.get('energy', 0),
                                    spirit=entry.get('spirit', 0),
                                    energy_regen=entry.get('energy_regen', 0.0),
                                    effects=entry.get('effects', ''),
                                    craftable=bool(entry.get('craftable', 0)),
                                    recipe=recipe,
                                    gather_times=int(entry.get('gather_times', 1)),
                                    is_sellable=bool(entry.get('sellable', 1)),
                                    level=int(entry.get('level', 0)),
                                    equip_require=entry.get('equip_require', {}),
                                    data=entry.get('data', {}),
                                    sect_token=entry.get('sect_token')
                                )
                                self.items[item.id] = item
                            except Exception as e:
                                logging.warning(f'[物品系统] 加载 {entry.get("id")} 失败: {e}')
                        else:
                            logging.warning(f'[物品系统] 跳过字段不全物品: {entry}')
                # dict 格式暂不支持
            except Exception as e:
                logging.error(f'[物品系统] 读取 {file_path} 失败: {e}')
    
    def _init_weapons(self):
        """初始化武器"""
        realms = [r[2] for r in RealmSystem.REALM_CONFIGS]
        weapons_data = [
            {
                "id": "wooden_sword",
                "name": "木剑",
                "type": ItemType.WEAPON,
                "quality": ItemQuality.COMMON,
                "description": "最基础的木制长剑，适合初学者使用。",
                "icon": "🗡️",
                "price": 100,
                "attack": 5,
                "level": 1,
                "equip_require": {}
            },
            {
                "id": "wooden_knife",
                "name": "木刀",
                "type": ItemType.WEAPON,
                "quality": ItemQuality.COMMON,
                "description": "最基础的木制短刀，适合初学者使用。",
                "icon": "🔪",
                "price": 100,
                "attack": 5,
                "level": 1,
                "equip_require": {}
            },
            {
                "id": "wooden_spear",
                "name": "木枪",
                "type": ItemType.WEAPON,
                "quality": ItemQuality.COMMON,
                "description": "最基础的木制长枪，适合初学者使用。",
                "icon": "🏹",
                "price": 120,
                "attack": 6,
                "level": 1,
                "equip_require": {}
            },
            {
                "id": "wooden_staff",
                "name": "木棍",
                "type": ItemType.WEAPON,
                "quality": ItemQuality.COMMON,
                "description": "最基础的木制棍棒，适合初学者使用。",
                "icon": "🥢",
                "price": 90,
                "attack": 4,
                "level": 1,
                "equip_require": {}
            },
            {
                "id": "iron_sword",
                "name": "铁剑",
                "type": ItemType.WEAPON,
                "quality": ItemQuality.UNCOMMON,
                "description": "精铁打造的锋利长剑，攻击力不俗。",
                "icon": "⚔️",
                "price": 500,
                "attack": 15,
                "level": 11,
                "equip_require": {"realm": realms[1]}
            },
            {
                "id": "steel_sword",
                "name": "精钢剑",
                "type": ItemType.WEAPON,
                "quality": ItemQuality.RARE,
                "description": "精钢锻造的宝剑，锋利无比，削铁如泥。",
                "icon": "⚔️",
                "price": 2000,
                "attack": 30,
                "level": 21,
                "equip_require": {"realm": realms[2]}
            },
            {
                "id": "mystic_blade",
                "name": "神秘之刃",
                "type": ItemType.WEAPON,
                "quality": ItemQuality.EPIC,
                "description": "蕴含神秘力量的宝刀，传说能斩断一切。",
                "icon": "🗡️",
                "price": 10000,
                "attack": 50,
                "mp": 20,
                "level": 31,
                "equip_require": {"realm": realms[3]}
            },
            {
                "id": "dragon_slayer",
                "name": "屠龙刀",
                "type": ItemType.WEAPON,
                "quality": ItemQuality.LEGENDARY,
                "description": "传说中的神兵利器，曾斩杀过恶龙，威力无穷。",
                "icon": "⚔️",
                "price": 50000,
                "attack": 80,
                "hp": 50,
                "mp": 30,
                "level": 41,
                "equip_require": {"realm": realms[4]}
            }
        ]
        for weapon_data in weapons_data:
            weapon_data = self._fix_type_quality(weapon_data)
            equip_require = weapon_data.get('equip_require', {})
            desc = weapon_data['description']
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            weapon_data['description'] = desc
            item = Item(**weapon_data)
            self.items[item.id] = item
    
    def _init_armor(self):
        """初始化防具"""
        realms = [r[2] for r in RealmSystem.REALM_CONFIGS]
        armor_data = [
            {
                "id": "wooden_armor",
                "name": "木甲",
                "type": ItemType.ARMOR,
                "quality": ItemQuality.COMMON,
                "description": "木板制成的简易护甲，提供基础防护。",
                "icon": "🪵",
                "price": 100,
                "defense": 5,
                "level": 1,
                "equip_require": {}
            },
            {
                "id": "wooden_shield",
                "name": "木盾",
                "type": ItemType.SHIELD,
                "quality": ItemQuality.COMMON,
                "description": "木板制成的简易盾牌，提供基础防护。",
                "icon": "🛡️",
                "price": 80,
                "defense": 4,
                "level": 1,
                "equip_require": {}
            },
            {
                "id": "cloth_armor",
                "name": "布衣",
                "type": ItemType.ARMOR,
                "quality": ItemQuality.COMMON,
                "description": "简单的布制衣服，提供基础防护。",
                "icon": "👕",
                "price": 80,
                "defense": 3,
                "level": 1,
                "equip_require": {}
            },
            {
                "id": "leather_armor",
                "name": "皮甲",
                "type": ItemType.ARMOR,
                "quality": ItemQuality.UNCOMMON,
                "description": "坚韧的皮革制成的护甲，防护效果不错。",
                "icon": "🥋",
                "price": 400,
                "defense": 8,
                "hp": 20,
                "level": 11,
                "equip_require": {"realm": realms[1]}
            },
            {
                "id": "iron_armor",
                "name": "铁甲",
                "type": ItemType.ARMOR,
                "quality": ItemQuality.RARE,
                "description": "厚重的铁制铠甲，防御力强大但略显笨重。",
                "icon": "🛡️",
                "price": 1500,
                "defense": 15,
                "hp": 40,
                "level": 21,
                "equip_require": {"realm": realms[2]}
            },
            {
                "id": "magic_robe",
                "name": "魔法长袍",
                "type": ItemType.ARMOR,
                "quality": ItemQuality.EPIC,
                "description": "蕴含魔力的长袍，既能防护又能增强法力。",
                "icon": "👘",
                "price": 8000,
                "defense": 12,
                "mp": 50,
                "hp": 30,
                "level": 31,
                "equip_require": {"realm": realms[3]}
            }
        ]
        for armor_data in armor_data:
            armor_data = self._fix_type_quality(armor_data)
            equip_require = armor_data.get('equip_require', {})
            desc = armor_data['description']
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            armor_data['description'] = desc
            item = Item(**armor_data)
            self.items[item.id] = item
    
    def _init_accessories(self):
        """初始化饰品"""
        realms = [r[2] for r in RealmSystem.REALM_CONFIGS]
        accessories_data = [
            {
                "id": "jade_necklace",
                "name": "翡翠项链",
                "type": ItemType.NECKLACE,
                "quality": ItemQuality.UNCOMMON,
                "description": "精美的翡翠项链，据说能带来好运。",
                "icon": "💎",
                "price": 300,
                "mp": 15,
                "level": 11,
                "equip_require": {"realm": realms[1]}
            },
            {
                "id": "silver_ring",
                "name": "银戒指",
                "type": ItemType.RING,
                "quality": ItemQuality.RARE,
                "description": "纯银打造的戒指，蕴含着神秘的力量。",
                "icon": "💍",
                "price": 1000,
                "attack": 5,
                "mp": 10,
                "level": 21,
                "equip_require": {"realm": realms[2]}
            },
            {
                "id": "gold_bracelet",
                "name": "金手镯",
                "type": ItemType.BRACELET,
                "quality": ItemQuality.EPIC,
                "description": "纯金打造的手镯，散发着富贵之气。",
                "icon": "💫",
                "price": 5000,
                "hp": 30,
                "mp": 20,
                "level": 31,
                "equip_require": {"realm": realms[3]}
            },
            {
                "id": "bronze_medal",
                "name": "青铜勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.COMMON,
                "description": "初入江湖的证明，象征着踏上武道之路的第一步。",
                "icon": "🥉",
                "price": 100,
                "energy": 10,
                "energy_regen": 0.1,
                "level": 1,
                "equip_require": {}
            },
            {
                "id": "iron_medal",
                "name": "铁血勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.UNCOMMON,
                "description": "历经铁血考验的证明，代表着坚韧不拔的意志。",
                "icon": "⚔️",
                "price": 300,
                "energy": 20,
                "energy_regen": 0.2,
                "level": 11,
                "equip_require": {"realm": realms[1]}
            },
            {
                "id": "silver_medal",
                "name": "银月勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.RARE,
                "description": "如银月般皎洁的勋章，象征着纯净的武道之心。",
                "icon": "🌙",
                "price": 800,
                "energy": 35,
                "energy_regen": 0.3,
                "level": 21,
                "equip_require": {"realm": realms[2]}
            },
            {
                "id": "gold_medal",
                "name": "金阳勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.EPIC,
                "description": "如金阳般耀眼的勋章，代表着无上的荣耀与实力。",
                "icon": "☀️",
                "price": 2000,
                "energy": 55,
                "energy_regen": 0.5,
                "level": 31,
                "equip_require": {"realm": realms[3]}
            },
            {
                "id": "jade_medal",
                "name": "翡翠勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.LEGENDARY,
                "description": "翡翠般珍贵的勋章，蕴含着天地间的灵气。",
                "icon": "💎",
                "price": 5000,
                "energy": 80,
                "energy_regen": 0.8,
                "level": 41,
                "equip_require": {"realm": realms[4]}
            },
            {
                "id": "dragon_medal",
                "name": "龙纹勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.MYTHIC,
                "description": "刻有龙纹的神秘勋章，传说中只有屠龙勇士才能获得。",
                "icon": "🐉",
                "price": 12000,
                "energy": 110,
                "energy_regen": 1.2,
                "level": 51,
                "equip_require": {"realm": realms[5]}
            },
            {
                "id": "phoenix_medal",
                "name": "凤凰勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.MYTHIC,
                "description": "凤凰涅槃的象征，代表着重生与超越的力量。",
                "icon": "🦅",
                "price": 20000,
                "energy": 150,
                "energy_regen": 1.6,
                "level": 61,
                "equip_require": {"realm": realms[6]}
            },
            {
                "id": "celestial_medal",
                "name": "天罡勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.MYTHIC,
                "description": "天罡星君的传承，蕴含着星辰般浩瀚的力量。",
                "icon": "⭐",
                "price": 35000,
                "energy": 200,
                "energy_regen": 2.0,
                "level": 71,
                "equip_require": {"realm": realms[7]}
            },
            {
                "id": "immortal_medal",
                "name": "仙尊勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.MYTHIC,
                "description": "仙尊级别的勋章，只有达到仙境的强者才能佩戴。",
                "icon": "👑",
                "price": 50000,
                "energy": 260,
                "energy_regen": 2.5,
                "level": 81,
                "equip_require": {"realm": realms[8]}
            },
            {
                "id": "supreme_medal",
                "name": "至尊勋章",
                "type": ItemType.MEDAL,
                "quality": ItemQuality.MYTHIC,
                "description": "至尊无上的勋章，代表着武道巅峰的至高荣誉。",
                "icon": "👑",
                "price": 100000,
                "energy": 350,
                "energy_regen": 3.0,
                "level": 91,
                "equip_require": {"realm": realms[9]}
            }
        ]
        for accessory_data in accessories_data:
            accessory_data = self._fix_type_quality(accessory_data)
            equip_require = accessory_data.get('equip_require', {})
            desc = accessory_data['description']
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            accessory_data['description'] = desc
            item = Item(**accessory_data)
            self.items[item.id] = item
    
    def _init_consumables(self):
        """初始化消耗品"""
        consumables_data = [
            {
                "id": "healing_potion",
                "name": "疗伤药",
                "type": ItemType.MEDICINE,
                "quality": ItemQuality.COMMON,
                "description": "基础的疗伤药物，能恢复少量生命值。",
                "icon": "🧪",
                "price": 50,
                "stackable": True,
                "max_stack": 99,
                "effects": [ItemEffect("heal", 50, "恢复50点生命值")]
            },
            {
                "id": "mana_potion",
                "name": "内力丹",
                "type": ItemType.PILL,
                "quality": ItemQuality.COMMON,
                "description": "恢复内力的丹药，适合修炼者使用。",
                "icon": "💊",
                "price": 60,
                "stackable": True,
                "max_stack": 99,
                "effects": [ItemEffect("mana", 30, "恢复30点内力")]
            },
            {
                "id": "energy_potion",
                "name": "精力丹",
                "type": ItemType.PILL,
                "quality": ItemQuality.COMMON,
                "description": "恢复精力的丹药，让人精神焕发。",
                "icon": "💊",
                "price": 40,
                "stackable": True,
                "max_stack": 99,
                "effects": [ItemEffect("energy", 20, "恢复20点精力")]
            },
            {
                "id": "breakthrough_pill",
                "name": "突破丹",
                "type": ItemType.PILL,
                "quality": ItemQuality.RARE,
                "description": "珍贵的突破丹药，能帮助修炼者突破境界。",
                "icon": "🌟",
                "price": 5000,
                "stackable": True,
                "max_stack": 10,
                "effects": [ItemEffect("breakthrough", 1, "境界突破必需品")]
            }
        ]
        
        for consumable_data in consumables_data:
            consumable_data = self._fix_type_quality(consumable_data)
            item = Item(**consumable_data)
            self.items[item.id] = item
    
    def _init_materials(self):
        """初始化材料"""
        # 采集品：矿石、木材、草药、兽皮
        ores = [
            {"id": f"ore_{i+1}", "name": name, "type": ItemType.ORE, "quality": quality, "description": desc, "icon": "⛏️", "price": price, "level": (i+1)*10}
            for i, (name, desc, quality, price) in enumerate([
                ("铜矿石", "常见的铜矿石，适合初学者锻造。", ItemQuality.COMMON, 10),
                ("铁矿石", "坚硬的铁矿石，江湖人常用来打造兵器。", ItemQuality.COMMON, 20),
                ("银矿石", "闪亮的银矿石，常用于打造精良装备。", ItemQuality.UNCOMMON, 40),
                ("金矿石", "稀有的金矿石，价值不菲。", ItemQuality.UNCOMMON, 80),
                ("精钢矿石", "可锻造精钢利器的矿石，江湖高手追捧。", ItemQuality.RARE, 150),
                ("玄铁矿石", "传说中可打造神兵的矿石。", ItemQuality.RARE, 300),
                ("紫金矿石", "罕见的紫金矿石，光华流转。", ItemQuality.EPIC, 600),
                ("龙骨矿石", "蕴含龙气的矿石，极为珍贵。", ItemQuality.LEGENDARY, 1200),
                ("陨星矿石", "天外陨星所化，极其罕见。", ItemQuality.LEGENDARY, 2500),
                ("神秘矿石", "蕴含天地灵气的神秘矿石。", ItemQuality.MYTHIC, 5000),
            ])
        ]
        woods = [
            {"id": f"wood_{i+1}", "name": name, "type": ItemType.WOOD, "quality": quality, "description": desc, "icon": "🪵", "price": price, "level": (i+1)*10}
            for i, (name, desc, quality, price) in enumerate([
                ("松木", "常见的松木，适合制作简易武器。", ItemQuality.COMMON, 8),
                ("柏木", "质地坚韧的柏木，常用于打造弓箭。", ItemQuality.COMMON, 16),
                ("楠木", "香气浓郁的楠木，江湖人喜用。", ItemQuality.UNCOMMON, 32),
                ("紫檀", "名贵的紫檀木，常用于雕刻。", ItemQuality.UNCOMMON, 64),
                ("乌木", "乌黑如墨的乌木，极为坚硬。", ItemQuality.RARE, 120),
                ("金丝楠木", "金丝闪烁的楠木，极为珍贵。", ItemQuality.RARE, 240),
                ("龙血木", "传说中吸收龙血的神木。", ItemQuality.EPIC, 480),
                ("凤尾竹", "凤羽所化，极为罕见。", ItemQuality.LEGENDARY, 1000),
                ("寒铁木", "寒气逼人的神木，极其稀有。", ItemQuality.LEGENDARY, 2000),
                ("神木", "蕴含天地灵气的神木。", ItemQuality.MYTHIC, 4000),
            ])
        ]
        herbs = [
            {"id": f"herb_{i+1}", "name": name, "type": ItemType.HERB, "quality": quality, "description": desc, "icon": "🌿", "price": price, "level": (i+1)*10}
            for i, (name, desc, quality, price) in enumerate([
                ("车前草", "常见的草药，入门必备。", ItemQuality.COMMON, 6),
                ("金银花", "清热解毒的良药。", ItemQuality.COMMON, 12),
                ("人参叶", "人参的叶片，略有补益。", ItemQuality.UNCOMMON, 24),
                ("何首乌", "滋补强身的名药。", ItemQuality.UNCOMMON, 48),
                ("灵芝", "珍贵的灵芝，延年益寿。", ItemQuality.RARE, 100),
                ("雪莲", "生长于雪山之巅的奇药。", ItemQuality.RARE, 200),
                ("龙涎草", "传说中龙所吐息滋养的灵草。", ItemQuality.EPIC, 400),
                ("凤凰草", "凤凰涅槃之地生长的神草。", ItemQuality.LEGENDARY, 900),
                ("九转还魂草", "可起死回生的神药。", ItemQuality.LEGENDARY, 1800),
                ("仙灵芝", "蕴含仙气的灵芝。", ItemQuality.MYTHIC, 3600),
            ])
        ]
        furs = [
            {"id": f"fur_{i+1}", "name": name, "type": ItemType.FUR, "quality": quality, "description": desc, "icon": "🦊", "price": price, "level": (i+1)*10}
            for i, (name, desc, quality, price) in enumerate([
                ("兔毛", "柔软的兔毛，常用于制作衣物。", ItemQuality.COMMON, 5),
                ("狼皮", "结实的狼皮，御寒佳品。", ItemQuality.COMMON, 10),
                ("豹皮", "花纹美丽的豹皮，江湖人喜爱。", ItemQuality.UNCOMMON, 20),
                ("熊皮", "厚重的熊皮，极为保暖。", ItemQuality.UNCOMMON, 40),
                ("貂皮", "珍贵的貂皮，价值不菲。", ItemQuality.RARE, 80),
                ("虎皮", "威猛虎皮，江湖豪杰象征。", ItemQuality.RARE, 160),
                ("龙鳞", "传说中龙的鳞片，坚不可摧。", ItemQuality.EPIC, 320),
                ("凤羽", "凤凰羽毛，极为罕见。", ItemQuality.LEGENDARY, 700),
                ("麒麟皮", "麒麟之皮，传说中的神兽遗物。", ItemQuality.LEGENDARY, 1400),
                ("仙狐皮", "仙狐所遗，蕴含灵气。", ItemQuality.MYTHIC, 2800),
            ])
        ]
        for ore in ores:
            self.items[ore["id"]] = Item(**ore)
        for wood in woods:
            self.items[wood["id"]] = Item(**wood)
        for herb in herbs:
            self.items[herb["id"]] = Item(**herb)
        for fur in furs:
            self.items[fur["id"]] = Item(**fur)
        # 增加无需采集等级的矿石：煤
        coal = {"id": "ore_coal", "name": "煤", "type": ItemType.ORE, "quality": ItemQuality.COMMON, "description": "常见的煤炭，随处可见，无需采集等级即可获得。", "icon": "⛏️", "price": 3, "level": 0}
        self.items[coal["id"]] = Item(**coal)
        # 增加无需采集等级的木材、草药、兽皮
        wood0 = {"id": "wood_firewood", "name": "柴火", "type": ItemType.WOOD, "quality": ItemQuality.COMMON, "description": "最常见的柴火，江湖客栈必备，无需采集等级即可获得。", "icon": "🪵", "price": 2, "level": 0}
        herb0 = {"id": "herb_wildgrass", "name": "野草", "type": ItemType.HERB, "quality": ItemQuality.COMMON, "description": "随处可见的野草，偶尔可入药，无需采集等级即可获得。", "icon": "🌿", "price": 1, "level": 0}
        fur0 = {"id": "fur_feather", "name": "落羽", "type": ItemType.FUR, "quality": ItemQuality.COMMON, "description": "江湖路边常见的鸟类落羽，可用作简单装饰，无需采集等级即可获得。", "icon": "🦢", "price": 1, "level": 0}
        self.items[wood0["id"]] = Item(**wood0)
        self.items[herb0["id"]] = Item(**herb0)
        self.items[fur0["id"]] = Item(**fur0)
        # 采集品对应成品装备
        # 矿石→武器，木材→棍棒，草药→丹药，兽皮→护甲
        weapon_names = [
            "铜剑", "铁剑", "银枪", "金刀", "精钢剑", "玄铁剑", "紫金枪", "龙骨刀", "陨星剑", "神秘神兵"
        ]
        weapon_descs = [
            "以铜矿石锻造的长剑，适合新手。",
            "以铁矿石锻造的长剑，锋利耐用。",
            "以银矿石锻造的长枪，银光闪烁。",
            "以金矿石锻造的金刀，贵气逼人。",
            "以精钢矿石锻造的宝剑，江湖高手常用。",
            "以玄铁矿石锻造的神剑，坚不可摧。",
            "以紫金矿石锻造的长枪，罕见珍品。",
            "以龙骨矿石锻造的龙骨刀，威力无穷。",
            "以陨星矿石锻造的陨星剑，天外神兵。",
            "以神秘矿石锻造的无上神兵，传说中的利器。"
        ]
        staff_names = [
            "松木棍", "柏木棍", "楠木棍", "紫檀棍", "乌木棍", "金丝楠棍", "龙血棍", "凤尾竹棍", "寒铁木棍", "神木棍"
        ]
        staff_descs = [
            f"用{name}制成的棍棒，结实耐用。" for name in ["松木", "柏木", "楠木", "紫檀", "乌木", "金丝楠木", "龙血木", "凤尾竹", "寒铁木", "神木"]]
        armor_names = [
            "兔毛衣", "狼皮甲", "豹皮甲", "熊皮甲", "貂皮甲", "虎皮甲", "龙鳞甲", "凤羽衣", "麒麟甲", "仙狐裘"
        ]
        armor_descs = [
            f"用{name}制成的护甲，防护力极佳。" for name in ["兔毛", "狼皮", "豹皮", "熊皮", "貂皮", "虎皮", "龙鳞", "凤羽", "麒麟皮", "仙狐皮"]]
        pill_names = [
            "草药丸", "金银花散", "参叶丹", "首乌丸", "灵芝丹", "雪莲丸", "龙涎丹", "凤凰丹", "还魂丹", "仙灵丹"]
        pill_descs = [
            f"以{name}为主材炼制的丹药，具有独特功效。" for name in ["车前草", "金银花", "人参叶", "何首乌", "灵芝", "雪莲", "龙涎草", "凤凰草", "九转还魂草", "仙灵芝"]]
        # 品质、价格、属性等与采集品等级挂钩
        realms = [r[2] for r in RealmSystem.REALM_CONFIGS]
        for i in range(10):
            # 武器
            equip_require = {} if (i+1)==1 else {"realm": realms[min(i // 10, len(realms) - 1)]}
            desc = weapon_descs[i]
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            self.items[f"weapon_{i+1}"] = Item(
                id=f"weapon_{i+1}",
                name=weapon_names[i],
                type=ItemType.WEAPON,
                quality=ores[i]["quality"],
                description=desc,
                icon="🗡️",
                price=ores[i]["price"]*10,
                attack=5 + i*8,
                stackable=False,
                level=(i+1),
                equip_require=equip_require
            )
            # 棍棒
            equip_require = {} if (i+1)==1 else {"realm": realms[min(i // 10, len(realms) - 1)]}
            desc = staff_descs[i]
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            self.items[f"staff_{i+1}"] = Item(
                id=f"staff_{i+1}",
                name=staff_names[i],
                type=ItemType.WEAPON,
                quality=woods[i]["quality"],
                description=desc,
                icon="🥢",
                price=woods[i]["price"]*8,
                attack=3 + i*6,
                stackable=False,
                level=(i+1),
                equip_require=equip_require
            )
            # 护甲
            equip_require = {} if (i+1)==1 else {"realm": realms[min(i // 10, len(realms) - 1)]}
            desc = armor_descs[i]
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            self.items[f"armor_{i+1}"] = Item(
                id=f"armor_{i+1}",
                name=armor_names[i],
                type=ItemType.ARMOR,
                quality=furs[i]["quality"],
                description=desc,
                icon="🥋",
                price=furs[i]["price"]*12,
                defense=4 + i*7,
                hp=10 + i*20,
                stackable=False,
                level=(i+1),
                equip_require=equip_require
            )
            # 丹药
            self.items[f"pill_{i+1}"] = Item(
                id=f"pill_{i+1}",
                name=pill_names[i],
                type=ItemType.PILL,
                quality=herbs[i]["quality"],
                description=pill_descs[i],
                icon="💊",
                price=herbs[i]["price"]*6,
                stackable=True,
                max_stack=99
            )
        # 刀法、棍法、暗器、拳脚、毒法等武器补充
        saber_names = [
            "铜刀", "铁刀", "银刀", "金刀", "精钢刀", "玄铁刀", "紫金刀", "龙骨刀", "陨星刀", "神秘宝刀"
        ]
        saber_descs = [
            f"以{name}锻造的宝刀，适合刀法修炼。" for name in ["铜矿石", "铁矿石", "银矿石", "金矿石", "精钢矿石", "玄铁矿石", "紫金矿石", "龙骨矿石", "陨星矿石", "神秘矿石"]]
        staff_names2 = [
            "松木棍", "柏木棍", "楠木棍", "紫檀棍", "乌木棍", "金丝楠棍", "龙血棍", "凤尾竹棍", "寒铁木棍", "神木棍"
        ]
        staff_descs2 = [
            f"用{name}制成的棍棒，适合棍法修炼。" for name in ["松木", "柏木", "楠木", "紫檀", "乌木", "金丝楠木", "龙血木", "凤尾竹", "寒铁木", "神木"]]
        hidden_names = [
            "飞镖", "袖箭", "毒针", "铁蒺藜", "流星锤", "飞蝗石", "梅花镖", "孔雀翎", "龙须针", "无影针"
        ]
        hidden_descs = [
            f"江湖常用的暗器：{name}，出其不意。" for name in ["飞镖", "袖箭", "毒针", "铁蒺藜", "流星锤", "飞蝗石", "梅花镖", "孔雀翎", "龙须针", "无影针"]]
        fist_names = [
            "布手套", "皮手套", "铁砂掌套", "虎爪套", "狼牙拳套", "龙骨拳套", "玄铁拳套", "紫金拳套", "麒麟拳套", "仙灵拳套"
        ]
        fist_descs = [
            f"适合拳脚功夫修炼的{name}。" for name in ["布手套", "皮手套", "铁砂掌套", "虎爪套", "狼牙拳套", "龙骨拳套", "玄铁拳套", "紫金拳套", "麒麟拳套", "仙灵拳套"]]
        poison_names = [
            "蛇胆粉", "蝎毒丸", "蜈蚣散", "断肠草", "鹤顶红", "七步散", "金蚕蛊", "无色毒", "冰魄毒", "九转剧毒"
        ]
        poison_descs = [
            f"江湖毒师常用的{name}，剧毒无比。" for name in ["蛇胆粉", "蝎毒丸", "蜈蚣散", "断肠草", "鹤顶红", "七步散", "金蚕蛊", "无色毒", "冰魄毒", "九转剧毒"]]
        realms = [r[2] for r in RealmSystem.REALM_CONFIGS]
        for i in range(10):
            # 刀法武器
            equip_require = {} if (i+1)==1 else {"realm": realms[min(i // 10, len(realms) - 1)]}
            desc = saber_descs[i]
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            self.items[f"saber_{i+1}"] = Item(
                id=f"saber_{i+1}",
                name=saber_names[i],
                type=ItemType.WEAPON,
                quality=ores[i]["quality"],
                description=desc,
                icon="🔪",
                price=ores[i]["price"]*11,
                attack=6 + i*8,
                stackable=False,
                level=(i+1),
                equip_require=equip_require
            )
            # 棍法武器
            equip_require = {} if (i+1)==1 else {"realm": realms[min(i // 10, len(realms) - 1)]}
            desc = staff_descs2[i]
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            self.items[f"staff2_{i+1}"] = Item(
                id=f"staff2_{i+1}",
                name=staff_names2[i],
                type=ItemType.WEAPON,
                quality=woods[i]["quality"],
                description=desc,
                icon="🥢",
                price=woods[i]["price"]*9,
                attack=4 + i*6,
                stackable=False,
                level=(i+1),
                equip_require=equip_require
            )
            # 暗器
            equip_require = {} if (i+1)==1 else {"realm": realms[min(i // 10, len(realms) - 1)]}
            desc = hidden_descs[i]
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            self.items[f"hidden_{i+1}"] = Item(
                id=f"hidden_{i+1}",
                name=hidden_names[i],
                type=ItemType.WEAPON,
                quality=ores[i]["quality"],
                description=desc,
                icon="🎯",
                price=ores[i]["price"]*7,
                attack=3 + i*5,
                stackable=True,
                max_stack=99,
                level=(i+1),
                equip_require=equip_require
            )
            # 拳脚
            equip_require = {} if (i+1)==1 else {"realm": realms[min(i // 10, len(realms) - 1)]}
            desc = fist_descs[i]
            if equip_require and 'realm' in equip_require:
                desc += f"【装备需求：境界={equip_require['realm']}】"
            self.items[f"fist_{i+1}"] = Item(
                id=f"fist_{i+1}",
                name=fist_names[i],
                type=ItemType.WEAPON,
                quality=furs[i]["quality"],
                description=desc,
                icon="🥊",
                price=furs[i]["price"]*8,
                attack=2 + i*5,
                stackable=False,
                level=(i+1),
                equip_require=equip_require
            )
            # 毒法
            self.items[f"poison_{i+1}"] = Item(
                id=f"poison_{i+1}",
                name=poison_names[i],
                type=ItemType.PILL,
                quality=herbs[i]["quality"],
                description=poison_descs[i],
                icon="☠️",
                price=herbs[i]["price"]*8,
                stackable=True,
                max_stack=99
            )
    
    def _init_tools(self):
        """初始化工具"""
        tools_data = [
            # 矿镐 1-10级
            {"id": "pickaxe_1", "name": "简易矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.COMMON, "description": "最基础的矿镐，可采集1级矿石，每次可采集1次。无需装备，背包中有即可采集。", "icon": "⛏️", "price": 50, "attack": 1, "gather_times": 1},
            {"id": "pickaxe_2", "name": "铜矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.UNCOMMON, "description": "铜制矿镐，可采集2级矿石，每次可采集2次。", "icon": "⛏️", "price": 120, "attack": 2, "gather_times": 2},
            {"id": "pickaxe_3", "name": "铁矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.UNCOMMON, "description": "铁制矿镐，可采集3级矿石，每次可采集3次。", "icon": "⛏️", "price": 250, "attack": 3, "gather_times": 3},
            {"id": "pickaxe_4", "name": "银矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.RARE, "description": "银制矿镐，可采集4级矿石，每次可采集4次。", "icon": "⛏️", "price": 500, "attack": 4, "gather_times": 4},
            {"id": "pickaxe_5", "name": "金矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.RARE, "description": "金制矿镐，可采集5级矿石，每次可采集5次。", "icon": "⛏️", "price": 900, "attack": 5, "gather_times": 5},
            {"id": "pickaxe_6", "name": "精钢矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.EPIC, "description": "精钢打造的矿镐，可采集6级矿石，每次可采集6次。", "icon": "⛏️", "price": 1500, "attack": 6, "gather_times": 6},
            {"id": "pickaxe_7", "name": "玄铁矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.EPIC, "description": "玄铁打造的矿镐，可采集7级矿石，每次可采集7次。", "icon": "⛏️", "price": 2200, "attack": 7, "gather_times": 7},
            {"id": "pickaxe_8", "name": "紫金矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.LEGENDARY, "description": "紫金打造的矿镐，可采集8级矿石，每次可采集8次。", "icon": "⛏️", "price": 3000, "attack": 8, "gather_times": 8},
            {"id": "pickaxe_9", "name": "龙骨矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.LEGENDARY, "description": "龙骨打造的矿镐，可采集9级矿石，每次可采集9次。", "icon": "⛏️", "price": 4000, "attack": 9, "gather_times": 9},
            {"id": "pickaxe_10", "name": "神工矿镐", "type": ItemType.PICKAXE, "quality": ItemQuality.MYTHIC, "description": "神工锻造的矿镐，可采集10级矿石，每次可采集10次。", "icon": "⛏️", "price": 6000, "attack": 10, "gather_times": 10},
            # 斧头 1-10级
            {"id": "axe_1", "name": "简易斧头", "type": ItemType.AXE, "quality": ItemQuality.COMMON, "description": "最基础的斧头，可采集1级木材，每次可采集1次。无需装备，背包中有即可采集。", "icon": "🪓", "price": 40, "attack": 1, "gather_times": 1},
            {"id": "axe_2", "name": "铜斧头", "type": ItemType.AXE, "quality": ItemQuality.UNCOMMON, "description": "铜制斧头，可采集2级木材，每次可采集2次。", "icon": "🪓", "price": 100, "attack": 2, "gather_times": 2},
            {"id": "axe_3", "name": "铁斧头", "type": ItemType.AXE, "quality": ItemQuality.UNCOMMON, "description": "铁制斧头，可采集3级木材，每次可采集3次。", "icon": "🪓", "price": 200, "attack": 3, "gather_times": 3},
            {"id": "axe_4", "name": "银斧头", "type": ItemType.AXE, "quality": ItemQuality.RARE, "description": "银制斧头，可采集4级木材，每次可采集4次。", "icon": "🪓", "price": 400, "attack": 4, "gather_times": 4},
            {"id": "axe_5", "name": "金斧头", "type": ItemType.AXE, "quality": ItemQuality.RARE, "description": "金制斧头，可采集5级木材，每次可采集5次。", "icon": "🪓", "price": 800, "attack": 5, "gather_times": 5},
            {"id": "axe_6", "name": "精钢斧头", "type": ItemType.AXE, "quality": ItemQuality.EPIC, "description": "精钢打造的斧头，可采集6级木材，每次可采集6次。", "icon": "🪓", "price": 1300, "attack": 6, "gather_times": 6},
            {"id": "axe_7", "name": "玄铁斧头", "type": ItemType.AXE, "quality": ItemQuality.EPIC, "description": "玄铁打造的斧头，可采集7级木材，每次可采集7次。", "icon": "🪓", "price": 1900, "attack": 7, "gather_times": 7},
            {"id": "axe_8", "name": "紫金斧头", "type": ItemType.AXE, "quality": ItemQuality.LEGENDARY, "description": "紫金打造的斧头，可采集8级木材，每次可采集8次。", "icon": "🪓", "price": 2600, "attack": 8, "gather_times": 8},
            {"id": "axe_9", "name": "龙骨斧头", "type": ItemType.AXE, "quality": ItemQuality.LEGENDARY, "description": "龙骨打造的斧头，可采集9级木材，每次可采集9次。", "icon": "🪓", "price": 3500, "attack": 9, "gather_times": 9},
            {"id": "axe_10", "name": "神工斧头", "type": ItemType.AXE, "quality": ItemQuality.MYTHIC, "description": "神工锻造的斧头，可采集10级木材，每次可采集10次。", "icon": "🪓", "price": 5000, "attack": 10, "gather_times": 10},
            # 镰刀 1-10级
            {"id": "sickle_1", "name": "简易镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.COMMON, "description": "最基础的镰刀，可采集1级草药，每次可采集1次。无需装备，背包中有即可采集。", "icon": "🔪", "price": 35, "attack": 1, "gather_times": 1},
            {"id": "sickle_2", "name": "铜镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.UNCOMMON, "description": "铜制镰刀，可采集2级草药，每次可采集2次。", "icon": "🔪", "price": 90, "attack": 2, "gather_times": 2},
            {"id": "sickle_3", "name": "铁镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.UNCOMMON, "description": "铁制镰刀，可采集3级草药，每次可采集3次。", "icon": "🔪", "price": 180, "attack": 3, "gather_times": 3},
            {"id": "sickle_4", "name": "银镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.RARE, "description": "银制镰刀，可采集4级草药，每次可采集4次。", "icon": "🔪", "price": 350, "attack": 4, "gather_times": 4},
            {"id": "sickle_5", "name": "金镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.RARE, "description": "金制镰刀，可采集5级草药，每次可采集5次。", "icon": "🔪", "price": 700, "attack": 5, "gather_times": 5},
            {"id": "sickle_6", "name": "精钢镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.EPIC, "description": "精钢打造的镰刀，可采集6级草药，每次可采集6次。", "icon": "🔪", "price": 1100, "attack": 6, "gather_times": 6},
            {"id": "sickle_7", "name": "玄铁镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.EPIC, "description": "玄铁打造的镰刀，可采集7级草药，每次可采集7次。", "icon": "🔪", "price": 1600, "attack": 7, "gather_times": 7},
            {"id": "sickle_8", "name": "紫金镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.LEGENDARY, "description": "紫金打造的镰刀，可采集8级草药，每次可采集8次。", "icon": "🔪", "price": 2200, "attack": 8, "gather_times": 8},
            {"id": "sickle_9", "name": "龙骨镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.LEGENDARY, "description": "龙骨打造的镰刀，可采集9级草药，每次可采集9次。", "icon": "🔪", "price": 3000, "attack": 9, "gather_times": 9},
            {"id": "sickle_10", "name": "神工镰刀", "type": ItemType.SICKLE, "quality": ItemQuality.MYTHIC, "description": "神工锻造的镰刀，可采集10级草药，每次可采集10次。", "icon": "🔪", "price": 4200, "attack": 10, "gather_times": 10},
            # 小刀 1-10级
            {"id": "knife_1", "name": "简易小刀", "type": ItemType.KNIFE, "quality": ItemQuality.COMMON, "description": "最基础的小刀，可采集1级兽皮，每次可采集1次。无需装备，背包中有即可采集。", "icon": "🔪", "price": 30, "attack": 1, "gather_times": 1},
            {"id": "knife_2", "name": "铜小刀", "type": ItemType.KNIFE, "quality": ItemQuality.UNCOMMON, "description": "铜制小刀，可采集2级兽皮，每次可采集2次。", "icon": "🔪", "price": 80, "attack": 2, "gather_times": 2},
            {"id": "knife_3", "name": "铁小刀", "type": ItemType.KNIFE, "quality": ItemQuality.UNCOMMON, "description": "铁制小刀，可采集3级兽皮，每次可采集3次。", "icon": "🔪", "price": 160, "attack": 3, "gather_times": 3},
            {"id": "knife_4", "name": "银小刀", "type": ItemType.KNIFE, "quality": ItemQuality.RARE, "description": "银制小刀，可采集4级兽皮，每次可采集4次。", "icon": "🔪", "price": 320, "attack": 4, "gather_times": 4},
            {"id": "knife_5", "name": "金小刀", "type": ItemType.KNIFE, "quality": ItemQuality.RARE, "description": "金制小刀，可采集5级兽皮，每次可采集5次。", "icon": "🔪", "price": 650, "attack": 5, "gather_times": 5},
            {"id": "knife_6", "name": "精钢小刀", "type": ItemType.KNIFE, "quality": ItemQuality.EPIC, "description": "精钢打造的小刀，可采集6级兽皮，每次可采集6次。", "icon": "🔪", "price": 1000, "attack": 6, "gather_times": 6},
            {"id": "knife_7", "name": "玄铁小刀", "type": ItemType.KNIFE, "quality": ItemQuality.EPIC, "description": "玄铁打造的小刀，可采集7级兽皮，每次可采集7次。", "icon": "🔪", "price": 1450, "attack": 7, "gather_times": 7},
            {"id": "knife_8", "name": "紫金小刀", "type": ItemType.KNIFE, "quality": ItemQuality.LEGENDARY, "description": "紫金打造的小刀，可采集8级兽皮，每次可采集8次。", "icon": "🔪", "price": 2000, "attack": 8, "gather_times": 8},
            {"id": "knife_9", "name": "龙骨小刀", "type": ItemType.KNIFE, "quality": ItemQuality.LEGENDARY, "description": "龙骨打造的小刀，可采集9级兽皮，每次可采集9次。", "icon": "🔪", "price": 2700, "attack": 9, "gather_times": 9},
            {"id": "knife_10", "name": "神工小刀", "type": ItemType.KNIFE, "quality": ItemQuality.MYTHIC, "description": "神工锻造的小刀，可采集10级兽皮，每次可采集10次。", "icon": "🔪", "price": 3800, "attack": 10, "gather_times": 10},
        ]
        
        for tool_data in tools_data:
            tool_data = self._fix_type_quality(tool_data)
            item = Item(**tool_data)
            self.items[item.id] = item
    
    def _init_special_items(self):
        """初始化特殊物品"""
        special_data = [
            {
                "id": "mystery_box",
                "name": "神秘宝箱",
                "type": ItemType.SPECIAL,
                "quality": ItemQuality.RARE,
                "description": "神秘的宝箱，打开后可能获得珍贵物品。",
                "icon": "📦",
                "price": 1000,
                "stackable": False
            },
            {
                "id": "lucky_coin",
                "name": "幸运金币",
                "type": ItemType.CURRENCY,
                "quality": ItemQuality.EPIC,
                "description": "传说中的幸运金币，据说能带来好运。",
                "icon": "🪙",
                "price": 500,
                "stackable": True,
                "max_stack": 10
            }
        ]
        
        for special_data in special_data:
            special_data = self._fix_type_quality(special_data)
            item = Item(**special_data)
            self.items[item.id] = item
    
    def _build_category_index(self):
        """构建物品分类索引"""
        for item_id, item in self.items.items():
            if item.type in [ItemType.WEAPON, ItemType.SHIELD]:
                self.item_categories["weapons"].append(item_id)
            elif item.type in [ItemType.ARMOR, ItemType.HELMET, ItemType.PANTS, ItemType.SHOES]:
                self.item_categories["armor"].append(item_id)
            elif item.type in [ItemType.NECKLACE, ItemType.RING, ItemType.BRACELET, ItemType.CLOAK, ItemType.MEDAL]:
                self.item_categories["accessories"].append(item_id)
            elif item.type in [ItemType.CONSUMABLE, ItemType.MEDICINE, ItemType.PILL]:
                self.item_categories["consumables"].append(item_id)
            elif item.type in [ItemType.MATERIAL, ItemType.ORE, ItemType.WOOD, ItemType.HERB, ItemType.FUR]:
                self.item_categories["materials"].append(item_id)
            elif item.type in [ItemType.TOOL, ItemType.PICKAXE, ItemType.AXE, ItemType.SICKLE, ItemType.KNIFE]:
                self.item_categories["tools"].append(item_id)
            else:
                self.item_categories["special"].append(item_id)
    
    def get_item(self, item_id: str) -> Optional[Item]:
        """根据ID获取物品"""
        return self.items.get(item_id)
    
    def get_items_by_category(self, category: str) -> List[Item]:
        """根据分类获取物品列表"""
        item_ids = self.item_categories.get(category, [])
        return [self.items[item_id] for item_id in item_ids if item_id in self.items]
    
    def get_items_by_type(self, item_type: ItemType) -> List[Item]:
        """根据类型获取物品列表"""
        return [item for item in self.items.values() if item.type == item_type]
    
    def get_items_by_quality(self, quality: ItemQuality) -> List[Item]:
        """根据品质获取物品列表"""
        return [item for item in self.items.values() if item.quality == quality]
    
    def can_equip(self, item: Item, slot_type: str) -> bool:
        """检查物品是否可以装备到指定槽位"""
        slot_mapping = {
            'main_hand': [ItemType.WEAPON, ItemType.SHIELD],
            'off_hand': [ItemType.WEAPON, ItemType.SHIELD, ItemType.OFF_HAND],
            'helmet': [ItemType.HELMET],
            'necklace': [ItemType.NECKLACE],
            'armor': [ItemType.ARMOR],
            'cloak': [ItemType.CLOAK],
            'pants': [ItemType.PANTS],
            'shoes': [ItemType.SHOES],
            'bracelet1': [ItemType.BRACELET, ItemType.ACCESSORY],
            'bracelet2': [ItemType.BRACELET, ItemType.ACCESSORY],
            'ring1': [ItemType.RING],
            'ring2': [ItemType.RING],
            'medal': [ItemType.MEDAL]
        }
        
        allowed_types = slot_mapping.get(slot_type, [])
        return item.type in allowed_types
    
    def can_use(self, item: Item) -> bool:
        """检查物品是否可以使用"""
        return item.type in [ItemType.CONSUMABLE, ItemType.MEDICINE, ItemType.PILL]
    
    def can_craft(self, item: Item) -> bool:
        """检查物品是否可以合成"""
        return item.craftable and item.recipe is not None
    
    def get_quality_color(self, quality: ItemQuality) -> str:
        """获取品质对应的颜色"""
        quality_colors = {
            ItemQuality.COMMON: "#b0b0b0",      # 灰色-凡品
            ItemQuality.UNCOMMON: "#1abc9c",    # 青色-精品
            ItemQuality.RARE: "#3498db",        # 蓝色-珍品
            ItemQuality.EPIC: "#9b59b6",        # 紫色-极品
            ItemQuality.LEGENDARY: "#e67e22",   # 橙色-神品
            ItemQuality.MYTHIC: "#e74c3c"       # 红色-仙品
        }
        return quality_colors.get(quality, "#b0b0b0")

    def randomize_equipment_effects(self, effects_string: str, variation_percent: int = 30) -> str:
        """
        对装备属性进行随机浮动

        Args:
            effects_string: 原始effects字符串，如 "attack:8,defense:5"
            variation_percent: 浮动百分比，默认30%

        Returns:
            随机化后的effects字符串
        """
        if not effects_string or not isinstance(effects_string, str):
            return effects_string

        import random

        effect_pairs = effects_string.split(',')
        randomized_pairs = []

        for pair in effect_pairs:
            if ':' in pair:
                attr, val = pair.split(':')
                attr_name = attr.strip()
                try:
                    base_value = float(val.strip())

                    # 计算随机范围 (70% - 130%)
                    min_value = base_value * (1 - variation_percent / 100)
                    max_value = base_value * (1 + variation_percent / 100)

                    # 生成随机值并四舍五入
                    random_value = random.uniform(min_value, max_value)
                    final_value = round(random_value)

                    # 确保最小值为1
                    final_value = max(1, final_value)

                    randomized_pairs.append(f"{attr_name}:{final_value}")
                except ValueError:
                    # 如果无法解析数值，保持原样
                    randomized_pairs.append(pair)
            else:
                randomized_pairs.append(pair)

        return ','.join(randomized_pairs)
    
    def get_quality_text(self, quality: ItemQuality) -> str:
        """获取品质对应的文本"""
        quality_texts = {
            ItemQuality.COMMON: "凡品",
            ItemQuality.UNCOMMON: "精品",
            ItemQuality.RARE: "珍品",
            ItemQuality.EPIC: "极品",
            ItemQuality.LEGENDARY: "神品",
            ItemQuality.MYTHIC: "仙品"
        }
        return quality_texts.get(quality, "凡品")
    
    def get_type_text(self, item_type: ItemType) -> str:
        """获取类型对应的文本"""
        type_texts = {
            ItemType.WEAPON: "武器",
            ItemType.HELMET: "头盔",
            ItemType.NECKLACE: "项链",
            ItemType.ARMOR: "衣服",
            ItemType.CLOAK: "披风",
            ItemType.PANTS: "裤子",
            ItemType.SHOES: "鞋子",
            ItemType.BRACELET: "手镯",
            ItemType.RING: "戒指",
            ItemType.SHIELD: "盾牌",
            ItemType.MEDAL: "勋章",
            ItemType.CONSUMABLE: "消耗品",
            ItemType.MEDICINE: "药品",
            ItemType.PILL: "丹药",
            ItemType.MATERIAL: "材料",
            ItemType.ORE: "矿石",
            ItemType.WOOD: "木材",
            ItemType.HERB: "草药",
            ItemType.FUR: "兽皮",
            ItemType.TOOL: "工具",
            ItemType.PICKAXE: "矿镐",
            ItemType.AXE: "斧头",
            ItemType.SICKLE: "镰刀",
            ItemType.KNIFE: "小刀",
            ItemType.SPECIAL: "特殊",
            ItemType.QUEST: "任务",
            ItemType.CURRENCY: "货币",
            ItemType.GATHER_TOOL: "采集工具",
            ItemType.ACCESSORY: "饰品",
            ItemType.BOOK: "书籍"
        }
        return type_texts.get(item_type, "未知")
    
    def create_inventory_item(self, item_id: str, quantity: int = 1) -> dict:
        """创建背包物品数据"""
        item = self.get_item(item_id)
        if not item:
            return None
        inventory_item = {
            'id': item.id,
            'name': item.name,
            'type': item.type.value,
            'quality': item.quality.value,  # 保证为字符串
            'description': item.description,
            'icon': item.icon,
            'price': item.price,
            'quantity': quantity,
            'stackable': item.stackable,
            'max_stack': item.max_stack,
            'attack': item.attack,
            'defense': item.defense,
            'hp': item.hp,
            'mp': item.mp,
            'energy': item.energy,
            'spirit': getattr(item, 'spirit', 0),  # 添加精力属性支持
            'energy_regen': item.energy_regen,
            'effects': getattr(item, 'effects', ''),  # 添加effects字段支持
            'unique_id': str(uuid.uuid4()),  # 新增唯一ID
            'sellable': item.is_sellable
        }
        return inventory_item
    
    def get_random_item(self, category: str = None, quality: ItemQuality = None) -> Optional[Item]:
        """获取随机物品"""
        if category:
            items = self.get_items_by_category(category)
        elif quality:
            items = self.get_items_by_quality(quality)
        else:
            items = list(self.items.values())
        
        if items:
            return random.choice(items)
        return None
    
    def _assign_realm_require(self, item: Item):
        """根据装备等级自动分配境界要求，1级不设，2级及以上依次分配境界名称"""
        if not hasattr(item, 'level') or item.level is None or item.level <= 1:
            return  # 1级及以下不设境界要求
        # 获取所有境界名称
        realms = [r[2] for r in RealmSystem.REALM_CONFIGS]
        # 以每10级一个档分配境界
        idx = min((item.level - 1) // 10, len(realms) - 1)
        realm_name = realms[idx]
        if item.equip_require is None:
            item.equip_require = {}
        item.equip_require['realm'] = realm_name
    
    def _fix_type_quality(self, entry):
        # 兼容 type/quality 为中文
        # t = entry.get('type')
        # q = entry.get('quality')
        # if t in TYPE_MAP:
        #     entry['type'] = TYPE_MAP[t]
        # if q in QUALITY_MAP:
        #     entry['quality'] = QUALITY_MAP[q]
        return entry

def load_items_config():
    """
    自动合并加载 backend/ 下所有 items_*.json 文件，返回物品字典结构
    """
    import glob
    import logging
    import json
    base_dir = os.path.dirname(__file__)
    items_files = glob.glob(os.path.join(base_dir, 'items_*.json'))
    items = {}
    logger = logging.getLogger("item_parse")
    for file_path in items_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            # 支持两种格式：list（推荐）或dict
            if isinstance(data, list):
                for entry in data:
                    if 'id' in entry:
                        items[entry['id']] = entry
                    else:
                        logger.warning(f"[物品解析] 跳过无id物品块: {entry}")
            elif isinstance(data, dict):
                for k, entry in data.items():
                    if 'id' in entry:
                        items[entry['id']] = entry
                    else:
                        logger.warning(f"[物品解析] 跳过无id物品块: {entry}")
        except Exception as e:
            logger.error(f"[物品解析] 读取 {file_path} 失败: {e}")
    return items

# 全局物品系统实例
item_system = ItemSystem() 