<view class="container data-v-44e2720a"><view wx:if="{{a}}" class="guild-info data-v-44e2720a"><view class="guild-header data-v-44e2720a"><text class="guild-name data-v-44e2720a">{{b}}</text><text class="guild-level data-v-44e2720a">等级 {{c}}</text></view><view class="guild-stats data-v-44e2720a"><view class="stat-item data-v-44e2720a"><text class="stat-label data-v-44e2720a">声望:</text><text class="stat-value data-v-44e2720a">{{d}}</text></view><view class="stat-item data-v-44e2720a"><text class="stat-label data-v-44e2720a">贡献:</text><text class="stat-value data-v-44e2720a">{{e}}</text></view><view class="stat-item data-v-44e2720a"><text class="stat-label data-v-44e2720a">职位:</text><text class="stat-value data-v-44e2720a">{{f}}</text></view></view><view wx:if="{{g}}" class="guild-actions data-v-44e2720a"><button class="daily-reward-btn data-v-44e2720a" bindtap="{{i}}" disabled="{{j}}">{{h}}</button></view></view><view wx:else class="no-guild data-v-44e2720a"><text class="no-guild-title data-v-44e2720a">尚未加入门派</text><text class="no-guild-desc data-v-44e2720a">加入门派可以获得专属武功和任务</text><button class="join-guild-btn data-v-44e2720a" bindtap="{{k}}">加入门派</button></view><view wx:if="{{l}}" class="guild-functions data-v-44e2720a"><view class="function-grid data-v-44e2720a"><view class="function-item data-v-44e2720a" bindtap="{{m}}"><text class="function-icon data-v-44e2720a">📋</text><text class="function-name data-v-44e2720a">门派任务</text></view><view class="function-item data-v-44e2720a" bindtap="{{n}}"><text class="function-icon data-v-44e2720a">⚔️</text><text class="function-name data-v-44e2720a">门派武功</text></view><view class="function-item data-v-44e2720a" bindtap="{{o}}"><text class="function-icon data-v-44e2720a">👥</text><text class="function-name data-v-44e2720a">门派成员</text></view><view class="function-item data-v-44e2720a" bindtap="{{p}}"><text class="function-icon data-v-44e2720a">🏪</text><text class="function-name data-v-44e2720a">门派商店</text></view><view class="function-item data-v-44e2720a" bindtap="{{q}}"><text class="function-icon data-v-44e2720a">🏆</text><text class="function-name data-v-44e2720a">门派排行</text></view></view></view><view wx:if="{{r}}" class="tasks-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派任务</text><text class="section-close data-v-44e2720a" bindtap="{{s}}">×</text></view><scroll-view class="tasks-list data-v-44e2720a" scroll-y="true"><view wx:for="{{t}}" wx:for-item="task" wx:key="h" class="task-item data-v-44e2720a" bindtap="{{task.i}}"><view class="task-info data-v-44e2720a"><text class="task-name data-v-44e2720a">{{task.a}}</text><text class="task-desc data-v-44e2720a">{{task.b}}</text><text class="task-reward data-v-44e2720a">奖励: {{task.c}}</text></view><view class="task-status data-v-44e2720a"><text class="{{['task-difficulty', 'data-v-44e2720a', task.e]}}">{{task.d}}</text><button class="accept-task-btn data-v-44e2720a" catchtap="{{task.f}}" disabled="{{task.g}}"> 接受 </button></view></view><view wx:if="{{v}}" class="empty-tasks data-v-44e2720a"><text class="data-v-44e2720a">暂无可接任务</text></view></scroll-view></view><view wx:if="{{w}}" class="skills-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派武功</text><text class="section-close data-v-44e2720a" bindtap="{{x}}">×</text></view><scroll-view class="skills-list data-v-44e2720a" scroll-y="true"><view wx:for="{{y}}" wx:for-item="skill" wx:key="i" class="skill-item data-v-44e2720a" bindtap="{{skill.j}}"><view class="skill-info data-v-44e2720a"><text class="skill-name data-v-44e2720a">{{skill.a}}</text><text class="skill-type data-v-44e2720a">{{skill.b}}</text><text class="skill-desc data-v-44e2720a">{{skill.c}}</text></view><view class="skill-status data-v-44e2720a"><text wx:if="{{skill.d}}" class="skill-level data-v-44e2720a">等级 {{skill.e}}</text><button class="learn-skill-btn data-v-44e2720a" catchtap="{{skill.g}}" disabled="{{skill.h}}">{{skill.f}}</button></view></view></scroll-view></view><view wx:if="{{z}}" class="members-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派成员</text><text class="section-close data-v-44e2720a" bindtap="{{A}}">×</text></view><scroll-view class="members-list data-v-44e2720a" scroll-y="true"><view wx:for="{{B}}" wx:for-item="member" wx:key="e" class="member-item data-v-44e2720a"><view class="member-info data-v-44e2720a"><text class="member-name data-v-44e2720a">{{member.a}}</text><text class="member-position data-v-44e2720a">{{member.b}}</text><text class="member-level data-v-44e2720a">等级 {{member.c}}</text></view><view class="member-contribution data-v-44e2720a"><text class="contribution-label data-v-44e2720a">贡献:</text><text class="contribution-value data-v-44e2720a">{{member.d}}</text></view></view></scroll-view></view><view wx:if="{{C}}" class="shop-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派商店</text><text class="section-close data-v-44e2720a" bindtap="{{D}}">×</text></view><scroll-view class="shop-list data-v-44e2720a" scroll-y="true"><view wx:for="{{E}}" wx:for-item="item" wx:key="f" class="shop-item data-v-44e2720a" bindtap="{{item.g}}"><view class="item-info data-v-44e2720a"><text class="item-name data-v-44e2720a">{{item.a}}</text><text class="item-desc data-v-44e2720a">{{item.b}}</text></view><view class="item-price data-v-44e2720a"><text class="price-value data-v-44e2720a">{{item.c}}</text><text class="price-unit data-v-44e2720a">贡献</text></view><button class="buy-item-btn data-v-44e2720a" catchtap="{{item.d}}" disabled="{{item.e}}"> 购买 </button></view></scroll-view></view><view wx:if="{{F}}" class="rankings-section data-v-44e2720a"><view class="section-header data-v-44e2720a"><text class="section-title data-v-44e2720a">门派排行榜</text><text class="section-close data-v-44e2720a" bindtap="{{G}}">×</text></view><view class="ranking-tabs data-v-44e2720a"><view class="{{['ranking-tab', 'data-v-44e2720a', H && 'active']}}" bindtap="{{I}}"> 实力排行 </view><view class="{{['ranking-tab', 'data-v-44e2720a', J && 'active']}}" bindtap="{{K}}"> 成员排行 </view><view class="{{['ranking-tab', 'data-v-44e2720a', L && 'active']}}" bindtap="{{M}}"> 贡献排行 </view></view><scroll-view class="rankings-list data-v-44e2720a" scroll-y="true"><view wx:for="{{N}}" wx:for-item="sect" wx:key="i" class="ranking-item data-v-44e2720a"><view class="ranking-rank data-v-44e2720a"><text class="rank-number data-v-44e2720a">{{sect.a}}</text></view><view class="ranking-info data-v-44e2720a"><text class="sect-name data-v-44e2720a">{{sect.b}}</text><view class="sect-stats data-v-44e2720a"><text wx:if="{{O}}" class="stat-text data-v-44e2720a"> 实力: {{sect.c}} | 成员: {{sect.d}}人 </text><text wx:elif="{{P}}" class="stat-text data-v-44e2720a"> 成员: {{sect.e}}人 | 平均等级: {{sect.f}}</text><text wx:elif="{{Q}}" class="stat-text data-v-44e2720a"> 总贡献: {{sect.g}} | 平均贡献: {{sect.h}}</text></view></view></view><view wx:if="{{R}}" class="no-rankings data-v-44e2720a"><text class="data-v-44e2720a">暂无排行数据</text></view></scroll-view></view><view wx:if="{{S}}" class="modal-overlay data-v-44e2720a" bindtap="{{W}}"><view class="modal-content data-v-44e2720a" catchtap="{{V}}"><view class="modal-header data-v-44e2720a"><text class="modal-title data-v-44e2720a">选择门派</text><text class="modal-close data-v-44e2720a" bindtap="{{T}}">×</text></view><view class="modal-body data-v-44e2720a"><view wx:for="{{U}}" wx:for-item="guild" wx:key="e" bindtap="{{guild.f}}" class="{{['guild-option', 'data-v-44e2720a', guild.g && 'guild-option-disabled']}}"><text class="guild-option-name data-v-44e2720a">{{guild.a}}</text><text class="guild-option-desc data-v-44e2720a">{{guild.b}}</text><text wx:if="{{guild.c}}" class="guild-option-requirement data-v-44e2720a"> 要求: {{guild.d}}</text><text wx:else class="guild-option-available data-v-44e2720a">可以加入</text></view></view></view></view><view wx:if="{{X}}" class="modal-overlay data-v-44e2720a" bindtap="{{ai}}"><view class="modal-content data-v-44e2720a" catchtap="{{ah}}"><view class="modal-header data-v-44e2720a"><text class="modal-title data-v-44e2720a">任务详情</text><text class="modal-close data-v-44e2720a" bindtap="{{Y}}">×</text></view><view wx:if="{{Z}}" class="modal-body data-v-44e2720a"><text class="detail-name data-v-44e2720a">{{aa}}</text><text class="detail-desc data-v-44e2720a">{{ab}}</text><text class="detail-requirement data-v-44e2720a">要求: {{ac}}</text><text class="detail-reward data-v-44e2720a">奖励: {{ad}}</text></view><view class="modal-footer data-v-44e2720a"><button class="modal-btn cancel-btn data-v-44e2720a" bindtap="{{ae}}">关闭</button><button class="modal-btn confirm-btn data-v-44e2720a" bindtap="{{af}}" disabled="{{ag}}"> 接受任务 </button></view></view></view></view>