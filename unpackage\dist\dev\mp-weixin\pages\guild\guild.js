"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_gameState = require("../../utils/gameState.js");
const utils_gameData = require("../../utils/gameData.js");
const _sfc_main = {
  data() {
    return {
      player: {},
      playerGuild: null,
      sectInfo: null,
      showTasksSection: false,
      showSkillsSection: false,
      showMembersSection: false,
      showShopSection: false,
      showRankingsSection: false,
      showWarsSection: false,
      showBuildingsSection: false,
      showGuildListModal: false,
      showDeclareWarModal: false,
      showTaskDetail: false,
      selectedTask: null,
      availableGuilds: [],
      loading: false,
      availableTasks: [],
      guildSkills: [],
      guildMembers: [],
      guildShopItems: [],
      sectRankings: [],
      currentRankingType: "power",
      sectWars: [],
      availableTargetSects: [],
      selectedTargetSectIndex: 0,
      selectedTargetSect: null,
      warReason: "",
      sectBuildings: {},
      sectResources: {}
    };
  },
  onLoad() {
    this.updateData();
    this.loadSectInfo();
  },
  onShow() {
    this.updateData();
    this.loadSectInfo();
  },
  onReady() {
    setTimeout(() => {
      this.loadSectInfo();
    }, 500);
  },
  methods: {
    updateData() {
      this.player = { ...utils_gameState.gameState.player };
    },
    // 加载门派信息
    async loadSectInfo() {
      if (!utils_gameState.gameState.isAuthed) {
        return;
      }
      try {
        this.loading = true;
        const sectInfoResponse = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_info" }
        });
        console.log("[门派页面] 收到门派信息响应:", sectInfoResponse);
        if (sectInfoResponse.type === "sect_action_success" && sectInfoResponse.data.action === "get_sect_info") {
          console.log("[门派页面] 门派信息成功，has_sect:", sectInfoResponse.data.has_sect);
          if (sectInfoResponse.data.has_sect) {
            this.sectInfo = sectInfoResponse.data;
            this.playerGuild = {
              id: sectInfoResponse.data.sect_id,
              name: sectInfoResponse.data.sect_name,
              level: sectInfoResponse.data.rank,
              contribution: sectInfoResponse.data.contribution,
              position: sectInfoResponse.data.rank_name
            };
            console.log("[门派页面] 设置playerGuild:", this.playerGuild);
          } else {
            this.sectInfo = null;
            this.playerGuild = null;
            console.log("[门派页面] 玩家未加入门派");
          }
        } else {
          console.log("[门派页面] 门派信息响应类型错误:", sectInfoResponse.type);
        }
        if (!this.playerGuild) {
          const availableResponse = await utils_gameData.gameUtils.sendMessage({
            type: "sect_action",
            data: { action: "get_available_sects" }
          });
          if (availableResponse.type === "available_sects_success") {
            this.availableGuilds = availableResponse.data.sects.map((sect) => ({
              id: sect.sect_id,
              name: sect.sect_name,
              description: sect.description,
              requirement: sect.requirements.level || 1,
              can_join: sect.can_join,
              reasons: sect.reasons
            }));
          }
        }
      } catch (error) {
        console.error("加载门派信息失败:", error);
        common_vendor.index.showToast({
          title: "加载门派信息失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 领取每日奖励
    async claimDailyReward() {
      var _a;
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "claim_daily_reward" }
        });
        if (response.type === "sect_action_success" && response.data.action === "claim_daily_reward") {
          const rewards = response.data.rewards || {};
          let rewardText = "领取每日奖励成功！";
          const rewardItems = [];
          if (rewards.silver)
            rewardItems.push(`银两 +${rewards.silver}`);
          if (rewards.exp)
            rewardItems.push(`历练 +${rewards.exp}`);
          if (rewardItems.length > 0) {
            rewardText += "\n获得：" + rewardItems.join("，");
          }
          common_vendor.index.showToast({
            title: rewardText,
            icon: "success",
            duration: 3e3
          });
          await this.loadSectInfo();
          this.updateData();
        } else {
          common_vendor.index.showToast({
            title: ((_a = response.data) == null ? void 0 : _a.message) || "领取失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("领取每日奖励失败:", error);
        common_vendor.index.showToast({
          title: "领取失败",
          icon: "none"
        });
      }
    },
    // 加载门派武功
    async loadSectSkills() {
      var _a;
      if (!utils_gameState.gameState.isAuthed || !this.playerGuild) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_skills" }
        });
        if (response.type === "sect_action_success" && response.data.action === "get_sect_skills") {
          this.guildSkills = response.data.skills.map((skill) => ({
            id: skill.skill_name,
            name: skill.skill_name,
            type: skill.skill_type,
            quality: skill.quality,
            weapon: skill.weapon,
            description: `攻击+${skill.attack} 防御+${skill.defense} 内力+${skill.internal_power}`,
            rank_requirement: skill.rank_requirement,
            contribution_requirement: skill.contribution_requirement,
            can_learn: skill.can_learn,
            reason: skill.reason.join("，"),
            learned: false
            // TODO: 检查是否已学会
          }));
        } else {
          console.error("加载门派武功失败:", (_a = response.data) == null ? void 0 : _a.message);
        }
      } catch (error) {
        console.error("加载门派武功失败:", error);
      }
    },
    // 加载门派任务
    async loadSectQuests() {
      var _a;
      if (!utils_gameState.gameState.isAuthed || !this.playerGuild) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_quests" }
        });
        if (response.type === "sect_action_success" && response.data.action === "get_sect_quests") {
          this.availableTasks = response.data.quests.map((quest) => ({
            id: quest.id,
            name: quest.name,
            description: quest.description,
            difficulty: quest.difficulty,
            status: quest.status,
            progress: quest.progress || 0,
            maxProgress: quest.max_progress || 1,
            rewards: quest.rewards,
            reward: this.formatRewards(quest.rewards),
            // 格式化奖励显示
            canAccept: quest.status === "available"
          }));
          console.log("门派任务数据:", this.availableTasks);
        } else {
          console.error("加载门派任务失败:", (_a = response.data) == null ? void 0 : _a.message);
        }
      } catch (error) {
        console.error("加载门派任务失败:", error);
      }
    },
    // 格式化奖励显示
    formatRewards(rewards) {
      if (!rewards)
        return "无奖励";
      const rewardTexts = [];
      if (rewards.contribution)
        rewardTexts.push(`贡献 ${rewards.contribution}`);
      if (rewards.exp)
        rewardTexts.push(`经验 ${rewards.exp}`);
      if (rewards.silver)
        rewardTexts.push(`银两 ${rewards.silver}`);
      return rewardTexts.join(", ") || "无奖励";
    },
    // 加载门派成员
    async loadSectMembers() {
      var _a;
      if (!utils_gameState.gameState.isAuthed || !this.playerGuild) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_members" }
        });
        if (response.type === "sect_action_success" && response.data.action === "get_sect_members") {
          this.guildMembers = response.data.members.map((member) => ({
            id: member.user_id,
            name: member.name,
            level: member.level,
            position: member.rank_name,
            contribution: member.contribution,
            joinTime: member.join_time,
            isSelf: member.is_self
          }));
        } else {
          console.error("加载门派成员失败:", (_a = response.data) == null ? void 0 : _a.message);
        }
      } catch (error) {
        console.error("加载门派成员失败:", error);
      }
    },
    // 加载门派商店
    async loadSectShop() {
      var _a;
      if (!utils_gameState.gameState.isAuthed || !this.playerGuild) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_shop" }
        });
        if (response.type === "sect_action_success" && response.data.action === "get_sect_shop") {
          this.guildShopItems = response.data.items.map((item) => ({
            id: item.id,
            name: item.name,
            description: item.description,
            price: item.price,
            currency: item.currency,
            canBuy: item.can_buy,
            reasons: item.reasons || []
          }));
        } else {
          console.error("加载门派商店失败:", (_a = response.data) == null ? void 0 : _a.message);
        }
      } catch (error) {
        console.error("加载门派商店失败:", error);
      }
    },
    getPositionName(position) {
      const positions = {
        "master": "掌门",
        "elder": "长老",
        "disciple": "弟子",
        "outer": "外门弟子"
      };
      return positions[position] || "弟子";
    },
    showGuildList() {
      this.showGuildListModal = true;
    },
    closeGuildList() {
      this.showGuildListModal = false;
    },
    async selectGuild(guild) {
      if (!guild.can_join) {
        const reasons = guild.reasons.join("，");
        common_vendor.index.showToast({
          title: `无法加入：${reasons}`,
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认加入",
        content: `确定要加入 ${guild.name} 吗？`,
        success: async (res) => {
          var _a;
          if (res.confirm) {
            try {
              const response = await utils_gameData.gameUtils.sendMessage({
                type: "sect_action",
                data: {
                  action: "join_sect",
                  sect_id: guild.id
                }
              });
              if (response.type === "join_sect_success") {
                this.closeGuildList();
                common_vendor.index.showToast({
                  title: response.data.message,
                  icon: "success"
                });
                await this.loadSectInfo();
              } else {
                common_vendor.index.showToast({
                  title: ((_a = response.data) == null ? void 0 : _a.message) || "加入门派失败",
                  icon: "none"
                });
              }
            } catch (error) {
              console.error("加入门派失败:", error);
              common_vendor.index.showToast({
                title: "加入门派失败",
                icon: "none"
              });
            }
          }
        }
      });
    },
    async showTasks() {
      this.showTasksSection = true;
      this.showSkillsSection = false;
      this.showMembersSection = false;
      this.showShopSection = false;
      this.showRankingsSection = false;
      this.showWarsSection = false;
      this.showBuildingsSection = false;
      await this.loadSectQuests();
    },
    hideTasks() {
      this.showTasksSection = false;
    },
    async showSkills() {
      this.showSkillsSection = true;
      this.showTasksSection = false;
      this.showMembersSection = false;
      this.showShopSection = false;
      this.showRankingsSection = false;
      this.showWarsSection = false;
      this.showBuildingsSection = false;
      await this.loadSectSkills();
    },
    hideSkills() {
      this.showSkillsSection = false;
    },
    async showMembers() {
      this.showMembersSection = true;
      this.showTasksSection = false;
      this.showSkillsSection = false;
      this.showShopSection = false;
      this.showRankingsSection = false;
      this.showWarsSection = false;
      this.showBuildingsSection = false;
      await this.loadSectMembers();
    },
    hideMembers() {
      this.showMembersSection = false;
    },
    async showShop() {
      this.showShopSection = true;
      this.showTasksSection = false;
      this.showSkillsSection = false;
      this.showMembersSection = false;
      this.showRankingsSection = false;
      this.showWarsSection = false;
      this.showBuildingsSection = false;
      await this.loadSectShop();
    },
    hideShop() {
      this.showShopSection = false;
    },
    hideOtherSections() {
      this.showTasksSection = false;
      this.showSkillsSection = false;
      this.showMembersSection = false;
      this.showRankingsSection = false;
      this.showWarsSection = false;
      this.showBuildingsSection = false;
    },
    getDifficultyClass(difficulty) {
      const classes = {
        "easy": "difficulty-easy",
        "medium": "difficulty-medium",
        "hard": "difficulty-hard"
      };
      return classes[difficulty] || "difficulty-easy";
    },
    getDifficultyName(difficulty) {
      const names = {
        "easy": "简单",
        "medium": "中等",
        "hard": "困难"
      };
      return names[difficulty] || "简单";
    },
    getSkillTypeName(type) {
      const types = {
        "external": "外功",
        "internal": "内功",
        "light": "轻功",
        "heart": "心法",
        "special": "特技"
      };
      return types[type] || "武功";
    },
    canAcceptTask(task) {
      return true;
    },
    canLearnSkill(skill) {
      return skill.can_learn;
    },
    canBuyItem(item) {
      return this.playerGuild.contribution >= item.price;
    },
    showTaskDetail(task) {
      this.selectedTask = task;
      this.showTaskDetail = true;
    },
    closeTaskDetail() {
      this.showTaskDetail = false;
      this.selectedTask = null;
    },
    acceptTask(task) {
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!this.canAcceptTask(task)) {
        common_vendor.index.showToast({
          title: "不满足任务要求",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "接受任务",
        content: `确定要接受任务 "${task.name}" 吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "任务已接受",
              icon: "success"
            });
            this.closeTaskDetail();
          }
        }
      });
    },
    showSkillDetail(skill) {
    },
    async learnSkill(skill) {
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!skill.can_learn) {
        common_vendor.index.showToast({
          title: skill.reason || "无法学习",
          icon: "none"
        });
        return;
      }
      if (skill.learned) {
        common_vendor.index.showToast({
          title: "开始修炼",
          icon: "success"
        });
      } else {
        common_vendor.index.showModal({
          title: "学习武功",
          content: `确定要学习 ${skill.name} 吗？
消耗贡献: ${skill.contribution_requirement}`,
          success: async (res) => {
            var _a;
            if (res.confirm) {
              try {
                const response = await utils_gameData.gameUtils.sendMessage({
                  type: "sect_action",
                  data: {
                    action: "learn_sect_skill",
                    skill_name: skill.name
                  }
                });
                if (response.type === "learn_skill_success") {
                  common_vendor.index.showToast({
                    title: response.data.message,
                    icon: "success"
                  });
                  await this.loadSectInfo();
                  await this.loadSectSkills();
                } else {
                  common_vendor.index.showToast({
                    title: ((_a = response.data) == null ? void 0 : _a.message) || "学习失败",
                    icon: "none"
                  });
                }
              } catch (error) {
                console.error("学习武功失败:", error);
                common_vendor.index.showToast({
                  title: "学习失败",
                  icon: "none"
                });
              }
            }
          }
        });
      }
    },
    showShopItemDetail(item) {
    },
    buyShopItem(item) {
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!this.canBuyItem(item)) {
        common_vendor.index.showToast({
          title: "贡献不足",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "购买物品",
        content: `确定要购买 ${item.name} 吗？
消耗贡献: ${item.price}`,
        success: (res) => {
          if (res.confirm) {
            this.playerGuild.contribution -= item.price;
            const type = item.type || "";
            const sellable = (typeof item.sellable !== "undefined" ? item.sellable : true) ? true : false;
            const unique_id = item.unique_id || `${item.id}_${Date.now()}_${Math.floor(Math.random() * 1e4)}`;
            utils_gameState.gameState.addItem({ ...item, type, sellable, unique_id });
            utils_gameState.gameState.save();
            common_vendor.index.showToast({
              title: "购买成功！",
              icon: "success"
            });
          }
        }
      });
    },
    // 显示排行榜
    async showRankings() {
      this.showRankingsSection = true;
      await this.loadSectRankings();
    },
    // 隐藏排行榜
    hideRankings() {
      this.showRankingsSection = false;
    },
    // 切换排行榜类型
    async switchRankingType(type) {
      this.currentRankingType = type;
      await this.loadSectRankings();
    },
    // 加载门派排行榜
    async loadSectRankings() {
      var _a, _b;
      if (!utils_gameState.gameState.isAuthed) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: {
            action: "get_sect_rankings",
            ranking_type: this.currentRankingType
          }
        });
        if (response.type === "sect_action_success" && response.data.action === "get_sect_rankings") {
          this.sectRankings = response.data.rankings || [];
          console.log("门派排行榜数据:", this.sectRankings);
        } else {
          console.error("加载门派排行榜失败:", (_a = response.data) == null ? void 0 : _a.message);
          common_vendor.index.showToast({
            title: ((_b = response.data) == null ? void 0 : _b.message) || "加载排行榜失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("加载门派排行榜失败:", error);
        common_vendor.index.showToast({
          title: "加载排行榜失败",
          icon: "none"
        });
      }
    },
    // 显示战争
    async showWars() {
      this.showWarsSection = true;
      await this.loadSectWars();
    },
    // 隐藏战争
    hideWars() {
      this.showWarsSection = false;
    },
    // 加载门派战争
    async loadSectWars() {
      var _a;
      if (!utils_gameState.gameState.isAuthed) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_wars" }
        });
        if (response.type === "sect_action_success" && response.data.action === "get_sect_wars") {
          this.sectWars = response.data.wars || [];
        } else {
          console.error("加载门派战争失败:", (_a = response.data) == null ? void 0 : _a.message);
        }
      } catch (error) {
        console.error("加载门派战争失败:", error);
      }
    },
    // 显示宣战弹窗
    showDeclareWarModal() {
      this.showDeclareWarModal = true;
      this.loadAvailableTargetSects();
    },
    // 关闭宣战弹窗
    closeDeclareWar() {
      this.showDeclareWarModal = false;
      this.selectedTargetSect = null;
      this.selectedTargetSectIndex = 0;
      this.warReason = "";
    },
    // 加载可宣战的门派
    async loadAvailableTargetSects() {
      var _a, _b;
      const allSects = Object.values(((_b = (_a = this.sectInfo) == null ? void 0 : _a.sect_config) == null ? void 0 : _b.sects) || {});
      this.availableTargetSects = allSects.filter(
        (sect) => {
          var _a2;
          return sect.id !== ((_a2 = this.sectInfo) == null ? void 0 : _a2.sect_id);
        }
      ).map((sect) => ({
        id: sect.id,
        name: sect.name
      }));
    },
    // 选择目标门派
    onTargetSectChange(e) {
      const index = e.detail.value;
      this.selectedTargetSectIndex = index;
      this.selectedTargetSect = this.availableTargetSects[index];
    },
    // 确认宣战
    async confirmDeclareWar() {
      var _a;
      if (!this.selectedTargetSect || !this.warReason.trim()) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: {
            action: "declare_war",
            target_sect_id: this.selectedTargetSect.id,
            war_reason: this.warReason.trim()
          }
        });
        if (response.type === "declare_war_success") {
          common_vendor.index.showToast({
            title: response.data.message,
            icon: "success"
          });
          this.closeDeclareWar();
          await this.loadSectWars();
        } else {
          common_vendor.index.showToast({
            title: ((_a = response.data) == null ? void 0 : _a.message) || "宣战失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "宣战失败: " + error.message,
          icon: "none"
        });
      }
    },
    // 获取战争状态文本
    getWarStatusText(status) {
      const statusMap = {
        "declared": "已宣战",
        "active": "战争中",
        "ended": "已结束"
      };
      return statusMap[status] || "未知";
    },
    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr)
        return "";
      const date = new Date(timeStr);
      return date.toLocaleString("zh-CN");
    },
    // 显示建设
    async showBuildings() {
      this.showBuildingsSection = true;
      await this.loadSectBuildings();
    },
    // 隐藏建设
    hideBuildings() {
      this.showBuildingsSection = false;
    },
    // 加载门派建筑
    async loadSectBuildings() {
      var _a;
      if (!utils_gameState.gameState.isAuthed) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_buildings" }
        });
        if (response.type === "sect_action_success" && response.data.action === "get_sect_buildings") {
          this.sectBuildings = response.data.buildings || {};
          this.sectResources = response.data.resources || {};
        } else {
          console.error("加载门派建筑失败:", (_a = response.data) == null ? void 0 : _a.message);
        }
      } catch (error) {
        console.error("加载门派建筑失败:", error);
      }
    },
    // 升级建筑
    async upgradeBuilding(buildingType) {
      var _a;
      if (!this.canUpgradeBuilding(buildingType)) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: {
            action: "upgrade_building",
            building_type: buildingType
          }
        });
        if (response.type === "upgrade_building_success") {
          common_vendor.index.showToast({
            title: response.data.message,
            icon: "success"
          });
          await this.loadSectBuildings();
        } else {
          common_vendor.index.showToast({
            title: ((_a = response.data) == null ? void 0 : _a.message) || "升级失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "升级失败: " + error.message,
          icon: "none"
        });
      }
    },
    // 检查是否可以升级建筑
    canUpgradeBuilding(buildingType) {
      const building = this.sectBuildings[buildingType];
      if (!building)
        return false;
      const upgradeCost = this.getBuildingUpgradeCost(buildingType, building.level + 1);
      if (!upgradeCost)
        return false;
      for (const [resource, cost] of Object.entries(upgradeCost)) {
        if ((this.sectResources[resource] || 0) < cost) {
          return false;
        }
      }
      return true;
    },
    // 获取建筑升级成本
    getBuildingUpgradeCost(buildingType, level) {
      const baseCosts = {
        "main_hall": { wood: 100, stone: 150, iron: 50, gold: 200 },
        "training_ground": { wood: 80, stone: 100, iron: 120, gold: 150 },
        "library": { wood: 120, stone: 80, iron: 60, gold: 180 },
        "warehouse": { wood: 60, stone: 120, iron: 80, gold: 100 }
      };
      const baseCost = baseCosts[buildingType];
      if (!baseCost)
        return null;
      const multiplier = Math.pow(1.5, level - 1);
      const cost = {};
      for (const [resource, baseCostValue] of Object.entries(baseCost)) {
        cost[resource] = Math.floor(baseCostValue * multiplier);
      }
      return cost;
    },
    // 获取资源名称
    getResourceName(resource) {
      const names = {
        "wood": "木材",
        "stone": "石料",
        "iron": "铁矿",
        "gold": "金币"
      };
      return names[resource] || resource;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.playerGuild
  }, $data.playerGuild ? common_vendor.e({
    b: common_vendor.t($data.playerGuild.name),
    c: common_vendor.t($data.playerGuild.level),
    d: common_vendor.t($data.player.reputation || 0),
    e: common_vendor.t($data.playerGuild.contribution),
    f: common_vendor.t($options.getPositionName($data.playerGuild.position)),
    g: $data.sectInfo
  }, $data.sectInfo ? {
    h: common_vendor.t($data.sectInfo.can_claim_daily ? "领取每日奖励" : "今日已领取"),
    i: common_vendor.o((...args) => $options.claimDailyReward && $options.claimDailyReward(...args)),
    j: !$data.sectInfo.can_claim_daily
  } : {}) : {
    k: common_vendor.o((...args) => $options.showGuildList && $options.showGuildList(...args))
  }, {
    l: $data.playerGuild
  }, $data.playerGuild ? {
    m: common_vendor.o((...args) => $options.showTasks && $options.showTasks(...args)),
    n: common_vendor.o((...args) => $options.showSkills && $options.showSkills(...args)),
    o: common_vendor.o((...args) => $options.showMembers && $options.showMembers(...args)),
    p: common_vendor.o((...args) => $options.showShop && $options.showShop(...args)),
    q: common_vendor.o((...args) => $options.showRankings && $options.showRankings(...args)),
    r: common_vendor.o((...args) => $options.showWars && $options.showWars(...args)),
    s: common_vendor.o((...args) => $options.showBuildings && $options.showBuildings(...args))
  } : {}, {
    t: $data.showTasksSection
  }, $data.showTasksSection ? common_vendor.e({
    v: common_vendor.o((...args) => $options.hideTasks && $options.hideTasks(...args)),
    w: common_vendor.f($data.availableTasks, (task, index, i0) => {
      return {
        a: common_vendor.t(task.name),
        b: common_vendor.t(task.description),
        c: common_vendor.t(task.reward),
        d: common_vendor.t($options.getDifficultyName(task.difficulty)),
        e: common_vendor.n($options.getDifficultyClass(task.difficulty)),
        f: common_vendor.o(($event) => $options.acceptTask(task), index),
        g: !$options.canAcceptTask(task),
        h: index,
        i: common_vendor.o(($event) => $options.showTaskDetail(task), index)
      };
    }),
    x: $data.availableTasks.length === 0
  }, $data.availableTasks.length === 0 ? {} : {}) : {}, {
    y: $data.showSkillsSection
  }, $data.showSkillsSection ? common_vendor.e({
    z: common_vendor.o((...args) => $options.hideSkills && $options.hideSkills(...args)),
    A: common_vendor.f($data.guildSkills, (skill, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(skill.name),
        b: common_vendor.t($options.getSkillTypeName(skill.type)),
        c: common_vendor.t(skill.description),
        d: skill.level
      }, skill.level ? {
        e: common_vendor.t(skill.level)
      } : {}, {
        f: common_vendor.t(skill.learned ? "修炼" : "学习"),
        g: common_vendor.o(($event) => $options.learnSkill(skill), index),
        h: !$options.canLearnSkill(skill),
        i: index,
        j: common_vendor.o(($event) => $options.showSkillDetail(skill), index)
      });
    }),
    B: $data.guildSkills.length === 0
  }, $data.guildSkills.length === 0 ? {} : {}) : {}, {
    C: $data.showMembersSection
  }, $data.showMembersSection ? common_vendor.e({
    D: common_vendor.o((...args) => $options.hideMembers && $options.hideMembers(...args)),
    E: common_vendor.f($data.guildMembers, (member, index, i0) => {
      return {
        a: common_vendor.t(member.name),
        b: common_vendor.t($options.getPositionName(member.position)),
        c: common_vendor.t(member.level),
        d: common_vendor.t(member.contribution),
        e: index
      };
    }),
    F: $data.guildMembers.length === 0
  }, $data.guildMembers.length === 0 ? {} : {}) : {}, {
    G: $data.showShopSection
  }, $data.showShopSection ? common_vendor.e({
    H: common_vendor.o((...args) => $options.hideShop && $options.hideShop(...args)),
    I: common_vendor.f($data.guildShopItems, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.description),
        c: common_vendor.t(item.price),
        d: common_vendor.o(($event) => $options.buyShopItem(item), index),
        e: !$options.canBuyItem(item),
        f: index,
        g: common_vendor.o(($event) => $options.showShopItemDetail(item), index)
      };
    }),
    J: $data.guildShopItems.length === 0
  }, $data.guildShopItems.length === 0 ? {} : {}) : {}, {
    K: $data.showRankingsSection
  }, $data.showRankingsSection ? common_vendor.e({
    L: common_vendor.o((...args) => $options.hideRankings && $options.hideRankings(...args)),
    M: $data.currentRankingType === "power" ? 1 : "",
    N: common_vendor.o(($event) => $options.switchRankingType("power")),
    O: $data.currentRankingType === "martial" ? 1 : "",
    P: common_vendor.o(($event) => $options.switchRankingType("martial")),
    Q: $data.currentRankingType === "contribution" ? 1 : "",
    R: common_vendor.o(($event) => $options.switchRankingType("contribution")),
    S: common_vendor.f($data.sectRankings, (sect, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(sect.rank),
        b: common_vendor.t(sect.player_name)
      }, $data.currentRankingType === "power" ? {
        c: common_vendor.t(sect.experience),
        d: common_vendor.t(sect.contribution)
      } : $data.currentRankingType === "martial" ? {
        e: common_vendor.t(sect.martial_score),
        f: common_vendor.t(sect.skill_count)
      } : $data.currentRankingType === "contribution" ? {
        g: common_vendor.t(sect.contribution),
        h: common_vendor.t(sect.sect_rank)
      } : {}, {
        i: index
      });
    }),
    T: $data.currentRankingType === "power",
    U: $data.currentRankingType === "martial",
    V: $data.currentRankingType === "contribution",
    W: $data.sectRankings.length === 0
  }, $data.sectRankings.length === 0 ? {} : {}) : {}, {
    X: $data.showWarsSection
  }, $data.showWarsSection ? common_vendor.e({
    Y: common_vendor.o((...args) => $options.hideWars && $options.hideWars(...args)),
    Z: common_vendor.o((...args) => $options.showDeclareWarModal && $options.showDeclareWarModal(...args)),
    aa: common_vendor.f($data.sectWars, (war, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(war.attacker_sect_name),
        b: war.is_attacker ? 1 : "",
        c: common_vendor.t(war.defender_sect_name),
        d: !war.is_attacker ? 1 : "",
        e: common_vendor.t($options.getWarStatusText(war.status)),
        f: common_vendor.n("status-" + war.status),
        g: common_vendor.t(war.war_reason),
        h: common_vendor.t($options.formatTime(war.declare_time)),
        i: war.status === "ended"
      }, war.status === "ended" ? {
        j: common_vendor.t(war.winner_sect_name || "平局"),
        k: common_vendor.t(war.attacker_score),
        l: common_vendor.t(war.defender_score)
      } : {}, {
        m: index
      });
    }),
    ab: $data.sectWars.length === 0
  }, $data.sectWars.length === 0 ? {} : {}) : {}, {
    ac: $options.showDeclareWarModal
  }, $options.showDeclareWarModal ? {
    ad: common_vendor.o((...args) => $options.closeDeclareWar && $options.closeDeclareWar(...args)),
    ae: common_vendor.t($data.selectedTargetSect ? $data.selectedTargetSect.name : "请选择门派"),
    af: $data.selectedTargetSectIndex,
    ag: $data.availableTargetSects,
    ah: common_vendor.o((...args) => $options.onTargetSectChange && $options.onTargetSectChange(...args)),
    ai: $data.warReason,
    aj: common_vendor.o(($event) => $data.warReason = $event.detail.value),
    ak: common_vendor.o((...args) => $options.closeDeclareWar && $options.closeDeclareWar(...args)),
    al: common_vendor.o((...args) => $options.confirmDeclareWar && $options.confirmDeclareWar(...args)),
    am: !$data.selectedTargetSect || !$data.warReason.trim(),
    an: common_vendor.o(() => {
    }),
    ao: common_vendor.o((...args) => $options.closeDeclareWar && $options.closeDeclareWar(...args))
  } : {}, {
    ap: $data.showBuildingsSection
  }, $data.showBuildingsSection ? common_vendor.e({
    aq: common_vendor.o((...args) => $options.hideBuildings && $options.hideBuildings(...args)),
    ar: common_vendor.t($data.sectResources.wood || 0),
    as: common_vendor.t($data.sectResources.stone || 0),
    at: common_vendor.t($data.sectResources.iron || 0),
    av: common_vendor.t($data.sectResources.gold || 0),
    aw: common_vendor.f($data.sectBuildings, (building, type, i0) => {
      return common_vendor.e({
        a: common_vendor.t(building.name),
        b: common_vendor.t(building.level),
        c: common_vendor.o(($event) => $options.upgradeBuilding(type), type),
        d: !$options.canUpgradeBuilding(type),
        e: $options.getBuildingUpgradeCost(type, building.level + 1)
      }, $options.getBuildingUpgradeCost(type, building.level + 1) ? {
        f: common_vendor.f($options.getBuildingUpgradeCost(type, building.level + 1), (cost, resource, i1) => {
          return {
            a: common_vendor.t($options.getResourceName(resource)),
            b: common_vendor.t(cost),
            c: resource,
            d: ($data.sectResources[resource] || 0) < cost ? 1 : ""
          };
        })
      } : {}, {
        g: type
      });
    }),
    ax: Object.keys($data.sectBuildings).length === 0
  }, Object.keys($data.sectBuildings).length === 0 ? {} : {}) : {}, {
    ay: $data.showGuildListModal
  }, $data.showGuildListModal ? {
    az: common_vendor.o((...args) => $options.closeGuildList && $options.closeGuildList(...args)),
    aA: common_vendor.o((...args) => $options.closeGuildList && $options.closeGuildList(...args)),
    aB: common_vendor.o(() => {
    }),
    aC: common_vendor.o((...args) => $options.closeGuildList && $options.closeGuildList(...args))
  } : {}, {
    aD: $options.showTaskDetail
  }, $options.showTaskDetail ? common_vendor.e({
    aE: common_vendor.o((...args) => $options.closeTaskDetail && $options.closeTaskDetail(...args)),
    aF: $data.selectedTask
  }, $data.selectedTask ? {
    aG: common_vendor.t($data.selectedTask.name),
    aH: common_vendor.t($data.selectedTask.description),
    aI: common_vendor.t($data.selectedTask.requirement),
    aJ: common_vendor.t($data.selectedTask.reward)
  } : {}, {
    aK: common_vendor.o((...args) => $options.closeTaskDetail && $options.closeTaskDetail(...args)),
    aL: common_vendor.o(($event) => $options.acceptTask($data.selectedTask)),
    aM: !$options.canAcceptTask($data.selectedTask),
    aN: common_vendor.o(() => {
    }),
    aO: common_vendor.o((...args) => $options.closeTaskDetail && $options.closeTaskDetail(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-44e2720a"]]);
wx.createPage(MiniProgramPage);
