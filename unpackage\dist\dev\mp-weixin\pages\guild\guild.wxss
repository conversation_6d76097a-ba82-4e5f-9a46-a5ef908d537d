
.container.data-v-44e2720a {
	padding: 20rpx;
	padding-bottom: 140rpx; /* 为tabBar留出空间 */
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}
.guild-info.data-v-44e2720a {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.guild-header.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.guild-name.data-v-44e2720a {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.guild-level.data-v-44e2720a {
	font-size: 28rpx;
	color: #667eea;
	background: #f0f4ff;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}
.guild-stats.data-v-44e2720a {
	display: flex;
	gap: 30rpx;
}
.stat-item.data-v-44e2720a {
	display: flex;
	align-items: center;
}
.stat-label.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
	margin-right: 10rpx;
}
.stat-value.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}
.guild-actions.data-v-44e2720a {
	margin-top: 20rpx;
	text-align: center;
}
.daily-reward-btn.data-v-44e2720a {
	background: linear-gradient(135deg, #f39c12, #e67e22);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 15rpx 30rpx;
	font-size: 26rpx;
}
.daily-reward-btn[disabled].data-v-44e2720a {
	background: #ccc;
	color: #666;
}
.no-guild.data-v-44e2720a {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 60rpx 30rpx;
	text-align: center;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.no-guild-title.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}
.no-guild-desc.data-v-44e2720a {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 40rpx;
}
.join-guild-btn.data-v-44e2720a {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}
.guild-functions.data-v-44e2720a {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.function-grid.data-v-44e2720a {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}
.function-item.data-v-44e2720a {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
	text-align: center;
	transition: all 0.3s;
}
.function-item.data-v-44e2720a:active {
	background: #e9ecef;
	transform: scale(0.98);
}
.function-icon.data-v-44e2720a {
	font-size: 48rpx;
	display: block;
	margin-bottom: 15rpx;
}
.function-name.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}
.tasks-section.data-v-44e2720a,
.skills-section.data-v-44e2720a,
.members-section.data-v-44e2720a,
.shop-section.data-v-44e2720a,
.rankings-section.data-v-44e2720a,
.wars-section.data-v-44e2720a,
.buildings-section.data-v-44e2720a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.98);
	z-index: 1000;
	padding: 20rpx;
}
.section-header.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
	margin-bottom: 20rpx;
}
.section-title.data-v-44e2720a {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.section-close.data-v-44e2720a {
	font-size: 40rpx;
	color: #999;
	line-height: 1;
}
.tasks-list.data-v-44e2720a,
.skills-list.data-v-44e2720a,
.members-list.data-v-44e2720a,
.shop-list.data-v-44e2720a {
	height: calc(100vh - 120rpx);
}
.task-item.data-v-44e2720a,
.skill-item.data-v-44e2720a,
.member-item.data-v-44e2720a,
.shop-item.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: white;
	border-radius: 15rpx;
	margin-bottom: 15rpx;
}
.task-info.data-v-44e2720a,
.skill-info.data-v-44e2720a,
.member-info.data-v-44e2720a,
.item-info.data-v-44e2720a {
	flex: 1;
}
.task-name.data-v-44e2720a,
.skill-name.data-v-44e2720a,
.member-name.data-v-44e2720a,
.item-name.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}
.task-desc.data-v-44e2720a,
.skill-desc.data-v-44e2720a,
.item-desc.data-v-44e2720a {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}
.task-reward.data-v-44e2720a {
	font-size: 24rpx;
	color: #f39c12;
	display: block;
}
.skill-type.data-v-44e2720a {
	font-size: 24rpx;
	color: #667eea;
	display: block;
	margin-bottom: 8rpx;
}
.member-position.data-v-44e2720a,
.member-level.data-v-44e2720a {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}
.task-status.data-v-44e2720a,
.skill-status.data-v-44e2720a,
.member-contribution.data-v-44e2720a {
	text-align: center;
}
.task-difficulty.data-v-44e2720a {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	display: block;
	margin-bottom: 10rpx;
}
.difficulty-easy.data-v-44e2720a {
	background: #d4edda;
	color: #155724;
}
.difficulty-medium.data-v-44e2720a {
	background: #fff3cd;
	color: #856404;
}
.difficulty-hard.data-v-44e2720a {
	background: #f8d7da;
	color: #721c24;
}
.skill-level.data-v-44e2720a {
	font-size: 24rpx;
	color: #667eea;
	display: block;
	margin-bottom: 10rpx;
}
.contribution-label.data-v-44e2720a {
	font-size: 24rpx;
	color: #666;
	margin-right: 10rpx;
}
.contribution-value.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	color: #f39c12;
}
.accept-task-btn.data-v-44e2720a,
.learn-skill-btn.data-v-44e2720a,
.buy-item-btn.data-v-44e2720a {
	background: #27ae60;
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 12rpx 24rpx;
	font-size: 26rpx;
}
.accept-task-btn[disabled].data-v-44e2720a,
.learn-skill-btn[disabled].data-v-44e2720a,
.buy-item-btn[disabled].data-v-44e2720a {
	opacity: 0.5;
	background: #ccc;
}
.empty-tasks.data-v-44e2720a {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}
.modal-overlay.data-v-44e2720a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}
.modal-content.data-v-44e2720a {
	background: white;
	border-radius: 20rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}
.modal-header.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.modal-title.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.modal-close.data-v-44e2720a {
	font-size: 40rpx;
	color: #999;
	line-height: 1;
}
.modal-body.data-v-44e2720a {
	padding: 30rpx;
	max-height: 400rpx;
	overflow-y: auto;
}
.guild-option.data-v-44e2720a {
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.guild-option.data-v-44e2720a:last-child {
	border-bottom: none;
}
.guild-option-name.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.guild-option-desc.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}
.guild-option-requirement.data-v-44e2720a {
	font-size: 24rpx;
	color: #e74c3c;
	display: block;
}
.guild-option-available.data-v-44e2720a {
	font-size: 24rpx;
	color: #27ae60;
	display: block;
}
.guild-option-disabled.data-v-44e2720a {
	opacity: 0.6;
	background: #f8f9fa;
}
.detail-name.data-v-44e2720a {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}
.detail-desc.data-v-44e2720a,
.detail-requirement.data-v-44e2720a,
.detail-reward.data-v-44e2720a {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}
.modal-footer.data-v-44e2720a {
	display: flex;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
	gap: 20rpx;
}
.modal-btn.data-v-44e2720a {
	flex: 1;
	padding: 20rpx;
	border: none;
	border-radius: 15rpx;
	font-size: 28rpx;
}
.cancel-btn.data-v-44e2720a {
	background: #f0f0f0;
	color: #666;
}
.confirm-btn.data-v-44e2720a {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}
.confirm-btn[disabled].data-v-44e2720a {
	opacity: 0.5;
	background: #ccc;
}

/* 排行榜样式 */
.ranking-tabs.data-v-44e2720a {
	display: flex;
	background: #f8f9fa;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 6rpx;
}
.ranking-tab.data-v-44e2720a {
	flex: 1;
	text-align: center;
	padding: 16rpx 12rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	color: #666;
	transition: all 0.3s;
}
.ranking-tab.active.data-v-44e2720a {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	font-weight: bold;
}
.rankings-list.data-v-44e2720a {
	height: calc(100vh - 200rpx);
}
.ranking-item.data-v-44e2720a {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
}
.ranking-rank.data-v-44e2720a {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #ffd700, #ffb347);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}
.rank-number.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: white;
}
.ranking-info.data-v-44e2720a {
	flex: 1;
}
.sect-name.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}
.sect-stats.data-v-44e2720a {
	display: flex;
	flex-direction: column;
}
.stat-text.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
}
.no-rankings.data-v-44e2720a {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
	font-size: 28rpx;
}

/* 战争样式 */
.war-actions.data-v-44e2720a {
	margin-bottom: 20rpx;
	text-align: center;
}
.declare-war-btn.data-v-44e2720a {
	background: linear-gradient(135deg, #e74c3c, #c0392b);
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
	font-weight: bold;
}
.wars-list.data-v-44e2720a {
	height: calc(100vh - 240rpx);
}
.war-item.data-v-44e2720a {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.war-header.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}
.war-sides.data-v-44e2720a {
	display: flex;
	align-items: center;
	flex: 1;
}
.sect-name.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	background: #f0f0f0;
	color: #333;
}
.sect-name.own-sect.data-v-44e2720a {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}
.vs-text.data-v-44e2720a {
	margin: 0 16rpx;
	font-size: 24rpx;
	color: #e74c3c;
	font-weight: bold;
}
.war-status.data-v-44e2720a {
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	font-weight: bold;
}
.status-declared.data-v-44e2720a {
	background: #ffeaa7;
	color: #d63031;
}
.status-active.data-v-44e2720a {
	background: #fab1a0;
	color: #e17055;
}
.status-ended.data-v-44e2720a {
	background: #ddd;
	color: #636e72;
}
.war-info.data-v-44e2720a {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}
.war-reason.data-v-44e2720a,
.war-time.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
}
.war-result.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	margin-top: 8rpx;
}
.winner.data-v-44e2720a,
.score.data-v-44e2720a {
	font-size: 26rpx;
	font-weight: bold;
	color: #e74c3c;
}
.no-wars.data-v-44e2720a {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
	font-size: 28rpx;
}
.war-reason-input.data-v-44e2720a {
	width: 100%;
	min-height: 120rpx;
	border: 2rpx solid #ddd;
	border-radius: 8rpx;
	padding: 16rpx;
	font-size: 28rpx;
	resize: none;
}
.picker-display.data-v-44e2720a {
	padding: 16rpx;
	border: 2rpx solid #ddd;
	border-radius: 8rpx;
	background: white;
	font-size: 28rpx;
}

/* 建设样式 */
.resources-display.data-v-44e2720a {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.resources-title.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
}
.resources-grid.data-v-44e2720a {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 16rpx;
}
.resource-item.data-v-44e2720a {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}
.resource-icon.data-v-44e2720a {
	font-size: 32rpx;
	margin-bottom: 8rpx;
}
.resource-name.data-v-44e2720a {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 4rpx;
}
.resource-value.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}
.buildings-list.data-v-44e2720a {
	height: calc(100vh - 300rpx);
}
.building-item.data-v-44e2720a {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.building-header.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}
.building-info.data-v-44e2720a {
	flex: 1;
}
.building-name.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 4rpx;
}
.building-level.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
}
.upgrade-btn.data-v-44e2720a {
	background: linear-gradient(135deg, #52c41a, #389e0d);
	color: white;
	border: none;
	border-radius: 12rpx;
	padding: 12rpx 24rpx;
	font-size: 26rpx;
	font-weight: bold;
}
.upgrade-btn[disabled].data-v-44e2720a {
	background: #ccc;
	color: #999;
}
.building-cost.data-v-44e2720a {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 16rpx;
}
.cost-title.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}
.cost-items.data-v-44e2720a {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}
.cost-item.data-v-44e2720a {
	font-size: 24rpx;
	color: #333;
	background: #f0f0f0;
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
}
.cost-item.insufficient.data-v-44e2720a {
	background: #ffebee;
	color: #e74c3c;
}
.no-buildings.data-v-44e2720a {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
	font-size: 28rpx;
}

/* 加入门派指南样式 */
.join-sect-guide.data-v-44e2720a {
	max-width: 600rpx;
	max-height: 80vh;
}
.guide-section.data-v-44e2720a {
	margin-bottom: 32rpx;
}
.guide-title.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}
.guide-subtitle.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	color: #666;
	margin-bottom: 12rpx;
	display: block;
}
.guide-text.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	display: block;
}
.guide-list.data-v-44e2720a {
	padding-left: 20rpx;
}
.guide-item.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
	line-height: 1.8;
	margin-bottom: 8rpx;
	display: block;
}
.sect-list.data-v-44e2720a {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}
.sect-item.data-v-44e2720a {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	border-left: 4rpx solid #667eea;
}
.sect-name.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}
.sect-desc.data-v-44e2720a {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
	display: block;
}
